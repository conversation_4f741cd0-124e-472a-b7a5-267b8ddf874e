<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsReplevyMainMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="claim_no" property="claimNo"/>
		 <result column="case_no" property="caseNo"/>
		 <result column="policy_no" property="policyNo"/>
		 <result column="replevy_no" property="replevyNo"/> 
		 <result column="risk_code" property="riskCode"/> 
		 <result column="replevy_times" property="replevyTimes"/>
		 <result column="case_times" property="caseTimes"/>
		 <result column="replevy_currency" property="replevyCurrency"/> 
		 <result column="sum_plan_replevy" property="sumPlanReplevy"/> 
		 <result column="sum_real_replevy" property="sumRealReplevy"/> 
		 <result column="sum_replevy_fee" property="sumReplevyFee"/> 
		 <result column="replevy_opinion" property="replevyOpinion"/> 
		 <result column="opinion_text" property="opinionText"/> 
		 <result column="replevy_text" property="replevyText"/> 
		 <result column="cancel_date" property="cancelDate"/> 
		 <result column="cancel_reason" property="cancelReason"/>
		 <result column="status" property="status"/>
		 <result column="make_com" property="makeCom"/> 
		 <result column="com_code" property="comCode"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="handler_code" property="handlerCode"/> 
		 <result column="serial_no" property="serialNo"/> 
		 <result column="compensate_no" property="compensateNo"/> 
		 <result column="approve_flag" property="approveFlag"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, claim_no, case_no, policy_no, replevy_no,
		 risk_code, replevy_times,case_times, replevy_currency, sum_plan_replevy, sum_real_replevy,
		 sum_replevy_fee, replevy_opinion, opinion_text, replevy_text, cancel_date,
		 cancel_reason, status, make_com, com_code, valid_flag,
		 flag, handler_code, serial_no, compensate_no, approve_flag,
		 created_by, sys_ctime, updated_by, sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="claimNo != null" >
			and claim_no = #{claimNo}
		</if>
		<if test="policyNo != null" >
			and policy_no = #{policyNo}
		</if>
		<if test="replevyNo != null" >
			and replevy_no = #{replevyNo}
		</if>
		<if test="riskCode != null" >
			and risk_code = #{riskCode}
		</if>
		<if test="replevyTimes != null">
			and replevy_times = #{replevyTimes}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="replevyCurrency != null" >
			and replevy_currency = #{replevyCurrency}
		</if>
		<if test="sumPlanReplevy != null" >
			and sum_plan_replevy = #{sumPlanReplevy}
		</if>
		<if test="sumRealReplevy != null" >
			and sum_real_replevy = #{sumRealReplevy}
		</if>
		<if test="sumReplevyFee != null" >
			and sum_replevy_fee = #{sumReplevyFee}
		</if>
		<if test="replevyOpinion != null" >
			and replevy_opinion = #{replevyOpinion}
		</if>
		<if test="opinionText != null" >
			and opinion_text = #{opinionText}
		</if>
		<if test="replevyText != null" >
			and replevy_text = #{replevyText}
		</if>
		<if test="cancelDate != null" >
			and cancel_date = #{cancelDate}
		</if>
		<if test="cancelReason != null" >
			and cancel_reason = #{cancelReason}
		</if>
		<if test="status != null" >
			and status = #{status}
		</if>
		<if test="makeCom != null" >
			and make_com = #{makeCom}
		</if>
		<if test="comCode != null" >
			and com_code = #{comCode}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="handlerCode != null" >
			and handler_code = #{handlerCode}
		</if>
		<if test="serialNo != null" >
			and serial_no = #{serialNo}
		</if>
		<if test="compensateNo != null" >
			and compensate_no = #{compensateNo}
		</if>
		<if test="approveFlag != null" >
			and approve_flag = #{approveFlag}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_main
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_main
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_main
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_replevy_main
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_replevy_main
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain">
		insert into clms_replevy_main (id, report_no, claim_no, policy_no, replevy_no, 
			risk_code, replevy_times,case_times, replevy_currency, sum_plan_replevy, sum_real_replevy,
			sum_replevy_fee, replevy_opinion, opinion_text, replevy_text, cancel_date,
			cancel_reason, status, make_com, com_code, valid_flag, 
			flag, handler_code, serial_no, compensate_no, approve_flag, 
			created_by, sys_ctime, updated_by, sys_utime)
		values(#{id}, #{reportNo}, #{claimNo}, #{policyNo}, #{replevyNo}, 
			#{riskCode}, #{replevyTimes},#{caseTimes}, #{replevyCurrency}, #{sumPlanReplevy}, #{sumRealReplevy},
			#{sumReplevyFee}, #{replevyOpinion}, #{opinionText}, #{replevyText}, #{cancelDate},
			#{cancelReason}, #{status}, #{makeCom}, #{comCode}, #{validFlag}, 
			#{flag}, #{handlerCode}, #{serialNo}, #{compensateNo}, #{approveFlag}, 
			#{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain">
		insert into clms_replevy_main
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="claimNo != null" >
				claim_no,
			</if>
			<if test="caseNo != null" >
				case_no,
			</if>
			<if test="policyNo != null" >
				policy_no,
			</if>
			<if test="replevyNo != null" >
				replevy_no,
			</if>
			<if test="riskCode != null" >
				risk_code,
			</if>
			<if test="replevyTimes != null" >
				replevy_times,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="replevyCurrency != null" >
				replevy_currency,
			</if>
			<if test="sumPlanReplevy != null" >
				sum_plan_replevy,
			</if>
			<if test="sumRealReplevy != null" >
				sum_real_replevy,
			</if>
			<if test="sumReplevyFee != null" >
				sum_replevy_fee,
			</if>
			<if test="replevyOpinion != null" >
				replevy_opinion,
			</if>
			<if test="opinionText != null" >
				opinion_text,
			</if>
			<if test="replevyText != null" >
				replevy_text,
			</if>
			<if test="cancelDate != null" >
				cancel_date,
			</if>
			<if test="cancelReason != null" >
				cancel_reason,
			</if>
			<if test="status != null" >
				status,
			</if>
			<if test="makeCom != null" >
				make_com,
			</if>
			<if test="comCode != null" >
				com_code,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="handlerCode != null" >
				handler_code,
			</if>
			<if test="serialNo != null" >
				serial_no,
			</if>
			<if test="compensateNo != null" >
				compensate_no,
			</if>
			<if test="approveFlag != null" >
				approve_flag,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="claimNo != null" >
				#{claimNo},
			</if>
			<if test="caseNo != null" >
				#{caseNo},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="replevyNo != null" >
				#{replevyNo},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="replevyTimes != null" >
				#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="replevyCurrency != null" >
				#{replevyCurrency},
			</if>
			<if test="sumPlanReplevy != null" >
				#{sumPlanReplevy},
			</if>
			<if test="sumRealReplevy != null" >
				#{sumRealReplevy},
			</if>
			<if test="sumReplevyFee != null" >
				#{sumReplevyFee},
			</if>
			<if test="replevyOpinion != null" >
				#{replevyOpinion},
			</if>
			<if test="opinionText != null" >
				#{opinionText},
			</if>
			<if test="replevyText != null" >
				#{replevyText},
			</if>
			<if test="cancelDate != null" >
				#{cancelDate},
			</if>
			<if test="cancelReason != null" >
				#{cancelReason},
			</if>
			<if test="status != null" >
				#{status},
			</if>
			<if test="makeCom != null" >
				#{makeCom},
			</if>
			<if test="comCode != null" >
				#{comCode},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="handlerCode != null" >
				#{handlerCode},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="compensateNo != null" >
				#{compensateNo},
			</if>
			<if test="approveFlag != null" >
				#{approveFlag},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain">
		update clms_replevy_main
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="claimNo != null" >
				claim_no=#{claimNo},
			</if>
			<if test="caseNo != null" >
				case_no=#{caseNo},
			</if>
			<if test="policyNo != null" >
				policy_no=#{policyNo},
			</if>
			<if test="replevyNo != null" >
				replevy_no=#{replevyNo},
			</if>
			<if test="riskCode != null" >
				risk_code=#{riskCode},
			</if>
			<if test="replevyTimes != null" >
				replevy_times=#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				case_times = #{caseTimes},
			</if>
			<if test="replevyCurrency != null" >
				replevy_currency=#{replevyCurrency},
			</if>
			<if test="sumPlanReplevy != null" >
				sum_plan_replevy=#{sumPlanReplevy},
			</if>
			<if test="sumRealReplevy != null" >
				sum_real_replevy=#{sumRealReplevy},
			</if>
			<if test="sumReplevyFee != null" >
				sum_replevy_fee=#{sumReplevyFee},
			</if>
			<if test="replevyOpinion != null" >
				replevy_opinion=#{replevyOpinion},
			</if>
			<if test="opinionText != null" >
				opinion_text=#{opinionText},
			</if>
			<if test="replevyText != null" >
				replevy_text=#{replevyText},
			</if>
			<if test="cancelDate != null" >
				cancel_date=#{cancelDate},
			</if>
			<if test="cancelReason != null" >
				cancel_reason=#{cancelReason},
			</if>
			<if test="status != null" >
				status=#{status},
			</if>
			<if test="makeCom != null" >
				make_com=#{makeCom},
			</if>
			<if test="comCode != null" >
				com_code=#{comCode},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="handlerCode != null" >
				handler_code=#{handlerCode},
			</if>
			<if test="serialNo != null" >
				serial_no=#{serialNo},
			</if>
			<if test="compensateNo != null" >
				compensate_no=#{compensateNo},
			</if>
			<if test="approveFlag != null" >
				approve_flag=#{approveFlag},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain">
		update clms_replevy_main
		set report_no=#{reportNo},
			claim_no=#{claimNo},
			policy_no=#{policyNo},
			replevy_no=#{replevyNo},
			risk_code=#{riskCode},
			replevy_times=#{replevyTimes},
		    case_times=#{caseTimes},
			replevy_currency=#{replevyCurrency},
			sum_plan_replevy=#{sumPlanReplevy},
			sum_real_replevy=#{sumRealReplevy},
			sum_replevy_fee=#{sumReplevyFee},
			replevy_opinion=#{replevyOpinion},
			opinion_text=#{opinionText},
			replevy_text=#{replevyText},
			cancel_date=#{cancelDate},
			cancel_reason=#{cancelReason},
			status=#{status},
			make_com=#{makeCom},
			com_code=#{comCode},
			valid_flag=#{validFlag},
			flag=#{flag},
			handler_code=#{handlerCode},
			serial_no=#{serialNo},
			compensate_no=#{compensateNo},
			approve_flag=#{approveFlag},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<select id="selectSerialNo" resultMap="BaseResultMap" parameterType="String">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_main
		where
		<if test="reportNo != null and reportNo!=''" >
			report_no=#{reportNo}
		</if>
		order by sys_ctime desc
		limit 1
	</select>
	<select id="selectClmsReplevyMain" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_main
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
		    and	report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and	case_times=#{caseTimes}
		</if>
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		<if test="replevyId != null and replevyId!=''" >
			and id = #{replevyId}
		</if>
		<if test="replevyTimes != null" >
			and	replevy_times=#{replevyTimes}
		</if>
		<if test="flag != null and flag!=''" >
			and	flag=#{flag}
		</if>
		and valid_flag='Y'
		order by sys_ctime desc
		limit 1
	</select>

	<select id="selectReplevyMain" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_main
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and	report_no=#{reportNo}
		</if>
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		<if test="replevyId != null and replevyId!=''" >
			and	id=#{replevyId}
		</if>
		and valid_flag='Y'
		order by sys_ctime desc
		limit 1
	</select>
	<select id="selectClmsReplevyMainList" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_main
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and	report_no=#{reportNo}
		</if>
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		and valid_flag='Y'
		order by sys_ctime
	</select>

	<!-- 更新总追偿收入金额 -->
	<update id="updateSumRealReplevy">
		UPDATE clms_replevy_main
		SET
		<if test="sumRepleviedMoney != null" >
			sum_real_replevy = #{sumRepleviedMoney},
		</if>
		<if test="sumChargeMoney != null" >
			sum_replevy_fee=#{sumChargeMoney},
		</if>
		    sys_utime = NOW()
		WHERE id = #{replevyId}
	</update>

	<!-- 减少总追偿费用金额 -->
	<update id="decreaseSumReplevyFee">
		UPDATE clms_replevy_main
		SET sum_replevy_fee = IFNULL(sum_replevy_fee, 0) - #{chargeMoney},
		    sys_utime = NOW()
		WHERE id = #{replevyId}
	</update>

	<!-- 减少总追偿收入金额 -->
	<update id="decreaseSumRealReplevy">
		UPDATE clms_replevy_main
		SET sum_real_replevy = IFNULL(sum_real_replevy, 0) - #{repleviedMoney},
		    sys_utime = NOW()
		WHERE id = #{replevyId}
	</update>

	<!-- 根据报案号查询追偿主表信息 -->
	<select id="queryReplevyMainByReportNo" resultType="com.paic.ncbs.claim.replevy.dto.ReplevyMainQueryDTO">
		SELECT
		    m.report_no as reportNo,
		    m.replevy_no as replevyNo,
		    row_number() over(partition by m.report_no order by m.case_times desc) as serialNo,
		    m.sum_real_replevy as sumRealReplevy,
		    m.flag,
			m.case_times,
			m.make_com,
			(SELECT ti.ASSIGNER FROM CLMS_TASK_INFO ti WHERE ti.report_no = m.report_no and STATUS = '0' and TASK_DEFINITION_BPM_KEY  = 'OC_REPLEVY' LIMIT 1 ) AS ASSIGNER,
		    (SELECT cp.PROCESS_STATUS FROM CLMS_case_process cp WHERE cp.report_no = m.report_no ORDER BY cp.case_times DESC LIMIT 1 ) AS processStatus
		FROM clms_replevy_main m
		WHERE m.report_no = #{reportNo}
	</select>
	<select id="selectNotFinished" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_main
		where flag!='2'
		and valid_flag='Y'
		and report_no=#{reportNo}
		order by sys_ctime desc
		limit 1
	</select>
</mapper>