<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsReplevyChargeMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge">
		 <id column="id" property="id"/>
		 <result column="invoice_info_id" property="invoiceInfoId"/> 
		 <result column="payment_info_id" property="paymentInfoId"/>
		 <result column="id_clm_payment_item" property="idClmPaymentItem"/>
		 <result column="report_no" property="reportNo"/> 
		 <result column="replevy_no" property="replevyNo"/> 
		 <result column="replevy_times" property="replevyTimes"/>
		 <result column="case_times" property="caseTimes"/>
		 <result column="plan_code" property="planCode"/> 
		 <result column="duty_code" property="dutyCode"/> 
		 <result column="charge_type" property="chargeType"/> 
		 <result column="pay_object" property="payObject"/>
		 <result column="charge_area" property="chargeArea"/> 
		 <result column="currency" property="currency"/> 
		 <result column="charge_money" property="chargeMoney"/> 
		 <result column="apply_link" property="applyLink"/> 
		 <result column="apply_person" property="applyPerson"/> 
		 <result column="apply_time" property="applyTime"/>
		 <result column="apply_reason" property="applyReason"/>
		 <result column="charge_describe" property="chargeDescribe"/> 
		 <result column="client_name" property="clientName"/>
		 <result column="client_account" property="clientAccount"/> 
		 <result column="approve_flag" property="approveFlag"/> 
		 <result column="settlement_flag" property="settlementFlag"/> 
		 <result column="statement_flag" property="statementFlag"/> 
		 <result column="divide_flag" property="divideFlag"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="serial_no" property="serialNo"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, invoice_info_id, payment_info_id,id_clm_payment_item, report_no,
		 replevy_no, replevy_times,case_times, plan_code, duty_code, charge_type,
		 pay_object, charge_area, currency, charge_money, apply_link,
		 apply_person, apply_time, apply_reason, charge_describe, client_name,
		 client_account, approve_flag, settlement_flag, statement_flag, divide_flag,
		 valid_flag, flag, serial_no, created_by, sys_ctime,
		 updated_by, sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="invoiceInfoId != null" >
			and invoice_info_id = #{invoiceInfoId}
		</if>
		<if test="paymentInfoId != null" >
			and payment_info_id = #{paymentInfoId}
		</if>
		<if test="idClmPaymentItem != null" >
			and id_clm_payment_item = #{idClmPaymentItem}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="replevyNo != null" >
			and replevy_no = #{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and replevy_times = #{replevyTimes}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="planCode != null" >
			and plan_code = #{planCode}
		</if>
		<if test="dutyCode != null" >
			and duty_code = #{dutyCode}
		</if>
		<if test="chargeType != null" >
			and charge_type = #{chargeType}
		</if>
		<if test="payObject != null" >
			and pay_object = #{payObject}
		</if>
		<if test="chargeArea != null" >
			and charge_area = #{chargeArea}
		</if>
		<if test="currency != null" >
			and currency = #{currency}
		</if>
		<if test="chargeMoney != null" >
			and charge_money = #{chargeMoney}
		</if>
		<if test="applyLink != null" >
			and apply_link = #{applyLink}
		</if>
		<if test="applyPerson != null" >
			and apply_person = #{applyPerson}
		</if>
		<if test="applyTime != null" >
			and apply_time = #{applyTime}
		</if>
		<if test="applyReason != null" >
			and apply_reason = #{applyReason}
		</if>
		<if test="chargeDescribe != null" >
			and charge_describe = #{chargeDescribe}
		</if>
		<if test="clientName != null" >
			and client_name = #{clientName}
		</if>
		<if test="clientAccount != null" >
			and client_account = #{clientAccount}
		</if>
		<if test="approveFlag != null" >
			and approve_flag = #{approveFlag}
		</if>
		<if test="settlementFlag != null" >
			and settlement_flag = #{settlementFlag}
		</if>
		<if test="statementFlag != null" >
			and statement_flag = #{statementFlag}
		</if>
		<if test="divideFlag != null" >
			and divide_flag = #{divideFlag}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="serialNo != null" >
			and serial_no = #{serialNo}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_charge
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectById" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_charge
		where id=#{id}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_charge
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_replevy_charge
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_replevy_charge
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge">
		insert into clms_replevy_charge (id, invoice_info_id, payment_info_id, id_clm_payment_item,report_no,
			replevy_no, replevy_times,case_times, plan_code, duty_code, charge_type,
			pay_object, charge_area, currency, charge_money, apply_link, 
			apply_person, apply_time, apply_reason, charge_describe, client_name, 
			client_account, approve_flag, settlement_flag, statement_flag, divide_flag, 
			valid_flag, flag, serial_no, created_by, sys_ctime,
			updated_by, sys_utime)
		values(#{id}, #{invoiceInfoId}, #{paymentInfoId},#{idClmPaymentItem}, #{reportNo},
			#{replevyNo}, #{replevyTimes},#{caseTimes}, #{planCode}, #{dutyCode}, #{chargeType},
			#{payObject}, #{chargeArea}, #{currency}, #{chargeMoney}, #{applyLink}, 
			#{applyPerson}, #{applyTime}, #{applyReason}, #{chargeDescribe}, #{clientName}, 
			#{clientAccount}, #{approveFlag}, #{settlementFlag}, #{statementFlag}, #{divideFlag}, 
			#{validFlag}, #{flag}, #{serialNo}, #{createdBy}, #{sysCtime}, 
			#{updatedBy}, #{sysUtime})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge">
		insert into clms_replevy_charge
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="invoiceInfoId != null" >
				invoice_info_id,
			</if>
			<if test="paymentInfoId != null" >
				payment_info_id,
			</if>
			<if test="idClmPaymentItem != null" >
				id_clm_payment_item,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="replevyNo != null" >
				replevy_no,
			</if>
			<if test="replevyTimes != null" >
				replevy_times,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="planCode != null" >
				plan_code,
			</if>
			<if test="dutyCode != null" >
				duty_code,
			</if>
			<if test="chargeType != null" >
				charge_type,
			</if>
			<if test="payObject != null" >
				pay_object,
			</if>
			<if test="chargeArea != null" >
				charge_area,
			</if>
			<if test="currency != null" >
				currency,
			</if>
			<if test="chargeMoney != null" >
				charge_money,
			</if>
			<if test="applyLink != null" >
				apply_link,
			</if>
			<if test="applyPerson != null" >
				apply_person,
			</if>
			<if test="applyTime != null" >
				apply_time,
			</if>
			<if test="applyReason != null" >
				apply_reason,
			</if>
			<if test="chargeDescribe != null" >
				charge_describe,
			</if>
			<if test="clientName != null" >
				client_name,
			</if>
			<if test="clientAccount != null" >
				client_account,
			</if>
			<if test="approveFlag != null" >
				approve_flag,
			</if>
			<if test="settlementFlag != null" >
				settlement_flag,
			</if>
			<if test="statementFlag != null" >
				statement_flag,
			</if>
			<if test="divideFlag != null" >
				divide_flag,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="serialNo != null" >
				serial_no,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="invoiceInfoId != null" >
				#{invoiceInfoId},
			</if>
			<if test="paymentInfoId != null" >
				#{paymentInfoId},
			</if>
			<if test="idClmPaymentItem != null" >
				#{idClmPaymentItem},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="replevyNo != null" >
				#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="planCode != null" >
				#{planCode},
			</if>
			<if test="dutyCode != null" >
				#{dutyCode},
			</if>
			<if test="chargeType != null" >
				#{chargeType},
			</if>
			<if test="payObject != null" >
				#{payObject},
			</if>
			<if test="chargeArea != null" >
				#{chargeArea},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="chargeMoney != null" >
				#{chargeMoney},
			</if>
			<if test="applyLink != null" >
				#{applyLink},
			</if>
			<if test="applyPerson != null" >
				#{applyPerson},
			</if>
			<if test="applyTime != null" >
				#{applyTime},
			</if>
			<if test="applyReason != null" >
				#{applyReason},
			</if>
			<if test="chargeDescribe != null" >
				#{chargeDescribe},
			</if>
			<if test="clientName != null" >
				#{clientName},
			</if>
			<if test="clientAccount != null" >
				#{clientAccount},
			</if>
			<if test="approveFlag != null" >
				#{approveFlag},
			</if>
			<if test="settlementFlag != null" >
				#{settlementFlag},
			</if>
			<if test="statementFlag != null" >
				#{statementFlag},
			</if>
			<if test="divideFlag != null" >
				#{divideFlag},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge">
		update clms_replevy_charge
		<set>
			<if test="invoiceInfoId != null" >
				invoice_info_id=#{invoiceInfoId},
			</if>
			<if test="paymentInfoId != null" >
				payment_info_id=#{paymentInfoId},
			</if>
			<if test="idClmPaymentItem != null" >
				id_clm_payment_item= #{idClmPaymentItem},
			</if>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="replevyNo != null" >
				replevy_no=#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				replevy_times=#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				case_times = #{caseTimes},
			</if>
			<if test="planCode != null" >
				plan_code=#{planCode},
			</if>
			<if test="dutyCode != null" >
				duty_code=#{dutyCode},
			</if>
			<if test="chargeType != null" >
				charge_type=#{chargeType},
			</if>
			<if test="payObject != null" >
				pay_object=#{payObject},
			</if>
			<if test="chargeArea != null" >
				charge_area=#{chargeArea},
			</if>
			<if test="currency != null" >
				currency=#{currency},
			</if>
			<if test="chargeMoney != null" >
				charge_money=#{chargeMoney},
			</if>
			<if test="applyLink != null" >
				apply_link=#{applyLink},
			</if>
			<if test="applyPerson != null" >
				apply_person=#{applyPerson},
			</if>
			<if test="applyTime != null" >
				apply_time=#{applyTime},
			</if>
			<if test="applyReason != null" >
				apply_reason=#{applyReason},
			</if>
			<if test="chargeDescribe != null" >
				charge_describe=#{chargeDescribe},
			</if>
			<if test="clientName != null" >
				client_name=#{clientName},
			</if>
			<if test="clientAccount != null" >
				client_account=#{clientAccount},
			</if>
			<if test="approveFlag != null" >
				approve_flag=#{approveFlag},
			</if>
			<if test="settlementFlag != null" >
				settlement_flag=#{settlementFlag},
			</if>
			<if test="statementFlag != null" >
				statement_flag=#{statementFlag},
			</if>
			<if test="divideFlag != null" >
				divide_flag=#{divideFlag},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="serialNo != null" >
				serial_no=#{serialNo},
			</if>
			<if test="finishDate!=null">
				finish_date=#{finishDate},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge">
		update clms_replevy_charge
		set invoice_info_id=#{invoiceInfoId},
			payment_info_id=#{paymentInfoId},
			id_clm_payment_item= #{idClmPaymentItem},
			report_no=#{reportNo},
			replevy_no=#{replevyNo},
			replevy_times=#{replevyTimes},
		    case_times=#{caseTimes},
			plan_code=#{planCode},
			duty_code=#{dutyCode},
			charge_type=#{chargeType},
			pay_object=#{payObject},
			charge_area=#{chargeArea},
			currency=#{currency},
			charge_money=#{chargeMoney},
			apply_link=#{applyLink},
			apply_person=#{applyPerson},
			apply_time=#{applyTime},
			apply_reason=#{applyReason},
			charge_describe=#{chargeDescribe},
			client_name=#{clientName},
			client_account=#{clientAccount},
			approve_flag=#{approveFlag},
			settlement_flag=#{settlementFlag},
			statement_flag=#{statementFlag},
			divide_flag=#{divideFlag},
			valid_flag=#{validFlag},
			flag=#{flag},
			serial_no=#{serialNo},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<select id="selectClmsReplevyCharge" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyChargeVo" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_charge
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and	report_no=#{reportNo}
		</if>
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		and valid_flag='Y'
		order by sys_ctime asc
	</select>
	<select id="getReplevyChargeByIdClmPaymentItem"
			resultType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_charge
		where id_clm_payment_item=#{idClmPaymentItem}
		and valid_flag='Y'
		limit 1
	</select>
	<select id="getMaxSerialNoByReportNo"
			resultType="java.lang.Integer">
		select
		max(serial_no)
		from clms_replevy_charge
		where report_no=#{reportNo}
		and valid_flag='Y'
	</select>
	<update id="updateIdPaymentItemByPrimaryKey">
		update clms_replevy_charge
		set id_clm_payment_item=#{idClmPaymentItem}
		where id=#{id}
	</update>
	<select id="getTotalChargeMoney"
			resultType="java.math.BigDecimal">
		select
		sum(charge_money)
		from clms_replevy_charge
		where replevy_no= #{replevyNo} and valid_flag='Y'
	</select>
    <select id="selectReplevyChargeByReportNoAndSerialNo"
            resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyChargeVo">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_charge
		where report_no= #{reportNo}
		and serial_no= #{serialNo}
		and valid_flag='Y'
		limit 1
	</select>
</mapper>