<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsRelatedActualReceiptMapper">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt">
        <id column="id" property="id"/>
        <result column="report_no" property="reportNo"/>
        <result column="case_times" property="caseTimes"/>
        <result column="sub_times" property="subTimes"/>
        <result column="business_id" property="businessId"/>
        <result column="receipt_type" property="receiptType"/>
        <result column="business_no" property="businessNo"/>
        <result column="trans_date" property="transDate"/>
        <result column="direction_type" property="directionType"/>
        <result column="trans_amount" property="transAmount"/>
        <result column="bank_trans_flow_no" property="bankTransFlowNo"/>
        <result column="our_bank_account" property="ourBankAccount"/>
        <result column="partner_bank_account" property="partnerBankAccount"/>
        <result column="partner_bank_account_name" property="partnerBankAccountName"/>
        <result column="partner_bank_name" property="partnerBankName"/>
        <result column="partner_bank_branch_name" property="partnerBankBranchName"/>
        <result column="post_script" property="postScript"/>
        <result column="write_off_remain_amount" property="writeOffRemainAmount"/>
        <result column="write_off_amount" property="writeOffAmount"/>
        <result column="freeze_flag" property="freezeFlag"/>
        <result column="valid_flag" property="validFlag"/>
        <result column="flag" property="flag"/>
        <result column="created_by" property="createdBy"/>
        <result column="sys_ctime" property="sysCtime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="sys_utime" property="sysUtime"/>
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        id, report_no,case_times,sub_times, business_id, receipt_type,
        business_no, trans_date, direction_type, trans_amount,
        bank_trans_flow_no, our_bank_account, partner_bank_account,
        partner_bank_account_name,partner_bank_name,partner_bank_branch_name, post_script, write_off_remain_amount, write_off_amount,
        freeze_flag,valid_flag, flag,
        created_by, sys_ctime, updated_by,
        sys_utime
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="id != null" >
            and id = #{id}
        </if>
        <if test="reportNo != null" >
            and report_no = #{reportNo}
        </if>
        <if test="caseTimes != null" >
            and case_times = #{caseTimes}
        </if>
        <if test="subTimes != null" >
            and sub_times = #{subTimes}
        </if>
        <if test="businessId != null" >
            and business_id = #{businessId}
        </if>
        <if test="receiptType != null">
            and receipt_type = #{receiptType}
        </if>
        <if test="businessNo != null" >
            and business_no = #{businessNo}
        </if>
        <if test="transDate != null" >
            and trans_date = #{transDate}
        </if>
        <if test="directionType != null" >
            and direction_type = #{directionType}
        </if>
        <if test="transAmount != null" >
            and trans_amount = #{transAmount}
        </if>
        <if test="bankTransFlowNo != null" >
            and bank_trans_flow_no = #{bankTransFlowNo}
        </if>
        <if test="ourBankAccount != null" >
            and our_bank_account = #{ourBankAccount}
        </if>
        <if test="partnerBankAccount != null" >
            and partner_bank_account = #{partnerBankAccount}
        </if>
        <if test="partnerBankAccountName != null" >
            and partner_bank_account_name = #{partnerBankAccountName}
        </if>
        <if test="partnerBankName != null" >
            and partner_bank_name = #{partnerBankName}
        </if>
        <if test="partnerBankBranchName != null" >
            and partner_bank_branch_name = #{partnerBankBranchName}
        </if>
        <if test="postScript != null" >
            and post_script = #{postScript}
        </if>
        <if test="writeOffRemainAmount != null" >
            and write_off_remain_amount = #{writeOffRemainAmount}
        </if>
        <if test="writeOffAmount != null" >
            and write_off_amount = #{writeOffAmount}
        </if>
        <if test="freezeFlag != null" >
            and freeze_flag = #{freezeFlag}
        </if>
        <if test="validFlag != null" >
            and valid_flag = #{validFlag}
        </if>
        <if test="flag != null" >
            and flag = #{flag}
        </if>
        <if test="createdBy != null" >
            and created_by = #{createdBy}
        </if>
        <if test="sysCtime != null" >
            and sys_ctime = #{sysCtime}
        </if>
        <if test="updatedBy != null" >
            and updated_by = #{updatedBy}
        </if>
        <if test="sysUtime != null" >
            and sys_utime = #{sysUtime}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
        <include refid="Base_Column_List" />
        from clms_related_actual_receipt
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List" />
        from clms_related_actual_receipt
        where id = #{param1}
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List" />
        from clms_related_actual_receipt
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey" parameterType="map">
        delete from clms_related_actual_receipt
        where id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys" parameterType="map">
        delete from clms_related_actual_receipt
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt">
        insert into clms_related_actual_receipt (id, report_no,case_times,sub_times, business_id,receipt_type,
        business_no, trans_date, direction_type, trans_amount,
        bank_trans_flow_no, our_bank_account, partner_bank_account,
        partner_bank_account_name,partner_bank_name,partner_bank_branch_name, post_script, write_off_remain_amount, write_off_amount,
        freeze_flag,valid_flag, flag, created_by, sys_ctime, updated_by,
        sys_utime)
        values(#{id}, #{reportNo},#{caseTimes},#{subTimes},#{businessId},#{receiptType},
        #{businessNo}, #{transDate}, #{directionType}, #{transAmount},
        #{bankTransFlowNo}, #{ourBankAccount}, #{partnerBankAccount},
        #{partnerBankAccountName},#{partnerBankName},#{partnerBankBranchName} #{postScript}, #{writeOffRemainAmount},#{writeOffAmount},
        #{freezeFlag}, #{validFlag}, #{flag}, #{createdBy}, #{sysCtime}, #{updatedBy},
        #{sysUtime})
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt">
        insert into clms_related_actual_receipt
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="reportNo != null" >
                report_no,
            </if>
            <if test="caseTimes != null" >
                case_times,
            </if>
            <if test="subTimes != null" >
                sub_times,
            </if>
            <if test="businessId != null" >
                business_id,
            </if>
            <if test="receiptType != null">
                receipt_type,
            </if>
            <if test="businessNo != null" >
                business_no,
            </if>
            <if test="transDate != null" >
                trans_date,
            </if>
            <if test="directionType != null" >
                direction_type,
            </if>
            <if test="transAmount != null" >
                trans_amount,
            </if>
            <if test="bankTransFlowNo != null" >
                bank_trans_flow_no,
            </if>
            <if test="ourBankAccount != null" >
                our_bank_account,
            </if>
            <if test="partnerBankAccount != null" >
                partner_bank_account,
            </if>
            <if test="partnerBankAccountName != null" >
                partner_bank_account_name,
            </if>
            <if test="partnerBankName != null" >
                partner_bank_name ,
            </if>
            <if test="partnerBankBranchName != null" >
                partner_bank_branch_name,
            </if>
            <if test="postScript != null" >
                post_script,
            </if>
            <if test="writeOffRemainAmount != null" >
                write_off_remain_amount,
            </if>
            <if test="writeOffAmount != null" >
                write_off_amount,
            </if>
            <if test="freezeFlag != null" >
                freeze_flag,
            </if>
            <if test="validFlag != null" >
                valid_flag,
            </if>
            <if test="flag != null" >
                flag,
            </if>
            <if test="createdBy != null" >
                created_by,
            </if>
            <if test="sysCtime != null" >
                sys_ctime,
            </if>
            <if test="updatedBy != null" >
                updated_by,
            </if>
            <if test="sysUtime != null" >
                sys_utime,
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="id != null" >
            #{id},
        </if>
        <if test="reportNo != null" >
            #{reportNo},
        </if>
        <if test="caseTimes != null" >
            #{caseTimes},
        </if>
        <if test="subTimes != null" >
            #{subTimes},
        </if>
        <if test="businessId != null" >
            #{businessId},
        </if>
        <if test="receiptType != null" >
            #{receiptType},
        </if>
        <if test="businessNo != null" >
            #{businessNo},
        </if>
        <if test="transDate != null" >
            #{transDate},
        </if>
        <if test="directionType != null" >
            #{directionType},
        </if>
        <if test="transAmount != null" >
            #{transAmount},
        </if>
        <if test="bankTransFlowNo != null" >
            #{bankTransFlowNo},
        </if>
        <if test="ourBankAccount != null" >
            #{ourBankAccount},
        </if>
        <if test="partnerBankAccount != null" >
            #{partnerBankAccount},
        </if>
        <if test="partnerBankAccountName != null" >
            #{partnerBankAccountName},
        </if>
        <if test="partnerBankName != null" >
            #{partnerBankName},
        </if>
        <if test="partnerBankBranchName != null" >
            #{partnerBankBranchName},
        </if>
        <if test="postScript != null" >
            #{postScript},
        </if>
        <if test="writeOffRemainAmount != null" >
            #{writeOffRemainAmount},
        </if>
        <if test="writeOffAmount != null" >
            #{writeOffAmount},
        </if>
        <if test="freezeFlag != null" >
            #{freezeFlag},
        </if>
        <if test="validFlag != null" >
            #{validFlag},
        </if>
        <if test="flag != null" >
            #{flag},
        </if>
        <if test="createdBy != null" >
            #{createdBy},
        </if>
        <if test="sysCtime != null" >
            #{sysCtime},
        </if>
        <if test="updatedBy != null" >
            #{updatedBy},
        </if>
        <if test="sysUtime != null" >
            #{sysUtime},
        </if>
    </trim>
    </insert>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt">
        update clms_related_actual_receipt
        <set>
            <if test="reportNo != null" >
                report_no=#{reportNo},
            </if>
            <if test="caseTimes != null" >
                case_times = #{caseTimes},
            </if>
            <if test="subTimes != null" >
                sub_times = #{subTimes},
            </if>
            <if test="businessId != null" >
                business_id=#{businessId},
            </if>
            <if test="receiptType != null" >
                receipt_type=#{receiptType},
            </if>
            <if test="businessNo != null" >
                business_no=#{businessNo},
            </if>
            <if test="transDate != null" >
                trans_date=#{transDate},
            </if>
            <if test="directionType != null" >
                direction_type=#{directionType},
            </if>
            <if test="transAmount != null" >
                trans_amount=#{transAmount},
            </if>
            <if test="bankTransFlowNo != null" >
                bank_trans_flow_no=#{bankTransFlowNo},
            </if>
            <if test="ourBankAccount != null" >
                our_bank_account=#{ourBankAccount},
            </if>
            <if test="partnerBankAccount != null" >
                partner_bank_account=#{partnerBankAccount},
            </if>
            <if test="partnerBankAccountName != null" >
                partner_bank_account_name=#{partnerBankAccountName},
            </if>
            <if test="partnerBankName != null" >
                partner_bank_name = #{partnerBankName},
            </if>
            <if test="partnerBankBranchName != null" >
                partner_bank_branch_name = #{partnerBankBranchName},
            </if>
            <if test="postScript != null" >
                post_script=#{postScript},
            </if>
            <if test="writeOffRemainAmount != null" >
                write_off_remain_amount=#{writeOffRemainAmount},
            </if>
            <if test="writeOffAmount != null" >
                write_off_amount=#{writeOffAmount},
            </if>
            <if test="freezeFlag != null" >
                freeze_flag=#{freezeFlag},
            </if>
            <if test="batchNo!=null">
                batch_no=#{batchNo},
            </if>
            <if test="validFlag != null" >
                valid_flag=#{validFlag},
            </if>
            <if test="flag != null" >
                flag=#{flag},
            </if>
            <if test="createdBy != null" >
                created_by=#{createdBy},
            </if>
            <if test="sysCtime != null" >
                sys_ctime=#{sysCtime},
            </if>
            <if test="updatedBy != null" >
                updated_by=#{updatedBy},
            </if>
            <if test="sysUtime != null" >
                sys_utime=#{sysUtime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt">
        update clms_related_actual_receipt
        set report_no=#{reportNo},
        case_times=#{caseTimes},
        sub_times= #{subTimes},
        business_id=#{businessId},
        receipt_type= #{receiptType},
        business_no=#{businessNo},
        trans_date=#{transDate},
        direction_type=#{directionType},
        trans_amount=#{transAmount},
        bank_trans_flow_no=#{bankTransFlowNo},
        our_bank_account=#{ourBankAccount},
        partner_bank_account=#{partnerBankAccount},
        partner_bank_account_name=#{partnerBankAccountName},
        partner_bank_name=#{partnerBankName},
        partner_bank_branch_name=#{partnerBankBranchName},
        post_script=#{postScript},
        write_off_remain_amount=#{writeOffRemainAmount},
        write_off_amount=#{writeOffAmount},
        freeze_flag=#{freezeFlag},
        valid_flag=#{validFlag},
        flag=#{flag},
        created_by=#{createdBy},
        sys_ctime=#{sysCtime},
        updated_by=#{updatedBy},
        sys_utime=#{sysUtime}
        where id = #{id}
    </update>
    <select id="getRelatedActualReceiptByEntity" resultType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt" parameterType="map">
        select
        <include refid="Base_Column_List" />
        from clms_related_actual_receipt
        where 1=1
        <if test="reportNo != null and reportNo!=''" >
            and	report_no=#{reportNo}
        </if>
        <if test="caseTimes != null" >
            and	case_times=#{caseTimes}
        </if>
        <if test="subTimes != null" >
            and	sub_times=#{subTimes}
        </if>
        <if test="businessId != null and businessId!=''" >
            and	business_id=#{businessId}
        </if>
        <if test="receiptType != null and receiptType!=''" >
            and	receipt_type= #{receiptType}
        </if>
        <if test="flag != null and flag!=''" >
            and	flag=#{flag}
        </if>
        <if test="freezeFlag != null and freezeFlag!=''" >
            and	freeze_flag=#{freezeFlag}
        </if>
        <if test="validFlag != null and validFlag!=''" >
            and	valid_flag= #{validFlag}
        </if>
        <if test="batchNo != null and batchNo!=''" >
            and	batch_no= #{batchNo}
        </if>
        and valid_flag='Y'
        order by sys_ctime asc
    </select>
    <select id="getListGroupByBankTransFlowNo" resultType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt" parameterType="map">
        select
        bank_trans_flow_no,
        sum(write_off_amount) as write_off_amount
        from clms_related_actual_receipt
        where 1=1
        <if test="reportNo != null and reportNo!=''" >
            and	report_no=#{reportNo}
        </if>
        <if test="caseTimes != null" >
            and	case_times=#{caseTimes}
        </if>
        <if test="subTimes != null" >
            and	sub_times=#{subTimes}
        </if>
        <if test="flag != null and flag!=''" >
            and	flag=#{flag}
        </if>
        <if test="freezeFlag != null and freezeFlag!=''" >
            and	freeze_flag=#{freezeFlag}
        </if>
        and valid_flag='Y'
        group by bank_trans_flow_no
    </select>
    <select id="getListByBusinessId" resultType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt" parameterType="map">
        select
        <include refid="Base_Column_List" />
        from clms_related_actual_receipt
        where business_id=#{businessId} and valid_flag='Y'
    </select>
    <update id="updateBatchNo" parameterType="map">
        update clms_related_actual_receipt
        set batch_no=#{batchNo}
        where report_no=#{reportNo}
        <if test="caseTimes != null" >
            and	case_times= #{caseTimes}
        </if>
        <if test="subTimes != null" >
            and	sub_times= #{subTimes}
        </if>
    </update>
    <select id="getTotalWriteOffAmountByDetailId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(write_off_amount), 0)
        FROM clms_related_actual_receipt
        WHERE business_id = #{businessId}
          AND receipt_type = '1'
          AND valid_flag = 'Y'
    </select>
    <delete id="deleteByBusinessId" parameterType="map">
        delete from clms_related_actual_receipt
        where business_id=#{replevyDetailId}
    </delete>
    <select id="getFreezeData" resultType="com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt">
        select
        <include refid="Base_Column_List" />
        from clms_related_actual_receipt
        where report_no=#{reportNo}
        and (freeze_flag!=#{freezeFlag} or freeze_flag is null)
        and	sub_times= #{subTimes}
        and valid_flag='Y'
        and receipt_type='1'
        limit 1
    </select>

</mapper>