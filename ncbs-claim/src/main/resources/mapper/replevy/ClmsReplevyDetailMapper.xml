<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsReplevyDetailMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail">
		 <id column="id" property="id"/> 
		 <result column="replevy_id" property="replevyId"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="replevy_no" property="replevyNo"/> 
		 <result column="replevy_times" property="replevyTimes"/>
		 <result column="case_times" property="caseTimes"/>
		 <result column="clause_code" property="clauseCode"/> 
		 <result column="kind_code" property="kindCode"/> 
		 <result column="replevied_name" property="repleviedName"/> 
		 <result column="replevied_type" property="repleviedType"/> 
		 <result column="replevied_certi_type" property="repleviedCertiType"/> 
		 <result column="replevied_certi_code" property="repleviedCertiCode"/>
		 <result column="customer_no" property="customerNo"/>
		 <result column="replevied_country_code" property="repleviedCountryCode"/> 
		 <result column="replevied_location" property="repleviedLocation"/> 
		 <result column="replevy_sum" property="replevySum"/> 
		 <result column="replevied_mobile" property="repleviedMobile"/> 
		 <result column="replevied_phone" property="repleviedPhone"/> 
		 <result column="replevied_address" property="repleviedAddress"/> 
		 <result column="replevied_post_code" property="repleviedPostCode"/> 
		 <result column="replevied_tax" property="repleviedTax"/> 
		 <result column="replevied_email" property="repleviedEmail"/> 
		 <result column="replevy_type" property="replevyType"/> 
		 <result column="replevy_agency" property="replevyAgency"/> 
		 <result column="replevy_person" property="replevyPerson"/> 
		 <result column="replevy_person_tel" property="replevyPersonTel"/> 
		 <result column="replevy_date" property="replevyDate"/> 
		 <result column="replevy_handle_date" property="replevyHandleDate"/> 
		 <result column="transfer_date" property="transferDate"/> 
		 <result column="replevye_ffect_date" property="replevyeFfectDate"/> 
		 <result column="replevy_way" property="replevyWay"/> 
		 <result column="replevy_reason" property="replevyReason"/> 
		 <result column="real_replevy" property="realReplevy"/> 
		 <result column="replevy_fee" property="replevyFee"/> 
		 <result column="status" property="status"/> 
		 <result column="approve_flag" property="approveFlag"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="currency" property="currency"/> 
		 <result column="replevy_progress" property="replevyProgress"/> 
		 <result column="replevied_area" property="repleviedArea"/> 
		 <result column="replevied_area_code" property="repleviedAreaCode"/> 
		 <result column="replevy_approve_flag" property="replevyApproveFlag"/> 
		 <result column="approve_person" property="approvePerson"/> 
		 <result column="serial_no" property="serialNo"/> 
		 <result column="approve_date" property="approveDate"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, replevy_id, report_no, replevy_no, replevy_times,case_times,
		 clause_code, kind_code, replevied_name, replevied_type, replevied_certi_type,
		 replevied_certi_code,customer_no, replevied_country_code, replevied_location, replevy_sum, replevied_mobile,
		 replevied_phone, replevied_address, replevied_post_code, replevied_tax, replevied_email,
		 replevy_type, replevy_agency, replevy_person, replevy_person_tel, replevy_date,
		 replevy_handle_date, transfer_date, replevye_ffect_date, replevy_way, replevy_reason,
		 real_replevy, replevy_fee, status, approve_flag, valid_flag,
		 flag, currency, replevy_progress, replevied_area, replevied_area_code,
		 replevy_approve_flag, approve_person, serial_no, approve_date, created_by,
		 sys_ctime, updated_by, sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="replevyId != null" >
			and replevy_id = #{replevyId}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="replevyNo != null" >
			and replevy_no = #{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and replevy_times = #{replevyTimes}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="clauseCode != null" >
			and clause_code = #{clauseCode}
		</if>
		<if test="kindCode != null" >
			and kind_code = #{kindCode}
		</if>
		<if test="repleviedName != null" >
			and replevied_name = #{repleviedName}
		</if>
		<if test="repleviedType != null" >
			and replevied_type = #{repleviedType}
		</if>
		<if test="repleviedCertiType != null" >
			and replevied_certi_type = #{repleviedCertiType}
		</if>
		<if test="repleviedCertiCode != null" >
			and replevied_certi_code = #{repleviedCertiCode}
		</if>
		<if test="customerNo != null" >
			and customer_no = #{customerNo}
		</if>
		<if test="repleviedCountryCode != null" >
			and replevied_country_code = #{repleviedCountryCode}
		</if>
		<if test="repleviedLocation != null" >
			and replevied_location = #{repleviedLocation}
		</if>
		<if test="replevySum != null" >
			and replevy_sum = #{replevySum}
		</if>
		<if test="repleviedMobile != null" >
			and replevied_mobile = #{repleviedMobile}
		</if>
		<if test="repleviedPhone != null" >
			and replevied_phone = #{repleviedPhone}
		</if>
		<if test="repleviedAddress != null" >
			and replevied_address = #{repleviedAddress}
		</if>
		<if test="repleviedPostCode != null" >
			and replevied_post_code = #{repleviedPostCode}
		</if>
		<if test="repleviedTax != null" >
			and replevied_tax = #{repleviedTax}
		</if>
		<if test="repleviedEmail != null" >
			and replevied_email = #{repleviedEmail}
		</if>
		<if test="replevyType != null" >
			and replevy_type = #{replevyType}
		</if>
		<if test="replevyAgency != null" >
			and replevy_agency = #{replevyAgency}
		</if>
		<if test="replevyPerson != null" >
			and replevy_person = #{replevyPerson}
		</if>
		<if test="replevyPersonTel != null" >
			and replevy_person_tel = #{replevyPersonTel}
		</if>
		<if test="replevyDate != null" >
			and replevy_date = #{replevyDate}
		</if>
		<if test="replevyHandleDate != null" >
			and replevy_handle_date = #{replevyHandleDate}
		</if>
		<if test="transferDate != null" >
			and transfer_date = #{transferDate}
		</if>
		<if test="replevyeFfectDate != null" >
			and replevye_ffect_date = #{replevyeFfectDate}
		</if>
		<if test="replevyWay != null" >
			and replevy_way = #{replevyWay}
		</if>
		<if test="replevyReason != null" >
			and replevy_reason = #{replevyReason}
		</if>
		<if test="realReplevy != null" >
			and real_replevy = #{realReplevy}
		</if>
		<if test="replevyFee != null" >
			and replevy_fee = #{replevyFee}
		</if>
		<if test="status != null" >
			and status = #{status}
		</if>
		<if test="approveFlag != null" >
			and approve_flag = #{approveFlag}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="currency != null" >
			and currency = #{currency}
		</if>
		<if test="replevyProgress != null" >
			and replevy_progress = #{replevyProgress}
		</if>
		<if test="repleviedArea != null" >
			and replevied_area = #{repleviedArea}
		</if>
		<if test="repleviedAreaCode != null" >
			and replevied_area_code = #{repleviedAreaCode}
		</if>
		<if test="replevyApproveFlag != null" >
			and replevy_approve_flag = #{replevyApproveFlag}
		</if>
		<if test="approvePerson != null" >
			and approve_person = #{approvePerson}
		</if>
		<if test="serialNo != null" >
			and serial_no = #{serialNo}
		</if>
		<if test="approveDate != null" >
			and approve_date = #{approveDate}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_detail
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectById" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_detail
		where id = #{id}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_detail
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_replevy_detail
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_replevy_detail
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail">
		insert into clms_replevy_detail (id, replevy_id, report_no, replevy_no, replevy_times, case_times,
			clause_code, kind_code, replevied_name, replevied_type, replevied_certi_type, 
			replevied_certi_code,customer_no, replevied_country_code, replevied_location, replevy_sum, replevied_mobile,
			replevied_phone, replevied_address, replevied_post_code, replevied_tax, replevied_email, 
			replevy_type, replevy_agency, replevy_person, replevy_person_tel, replevy_date, 
			replevy_handle_date, transfer_date, replevye_ffect_date, replevy_way, replevy_reason, 
			real_replevy, replevy_fee, status, approve_flag, valid_flag, 
			flag, currency, replevy_progress, replevied_area, replevied_area_code, 
			replevy_approve_flag, approve_person, serial_no, approve_date, created_by, 
			sys_ctime, updated_by, sys_utime)
		values(#{id}, #{replevyId}, #{reportNo}, #{replevyNo}, #{replevyTimes}, #{caseTimes},
			#{clauseCode}, #{kindCode}, #{repleviedName}, #{repleviedType}, #{repleviedCertiType}, 
			#{repleviedCertiCode},#{customer_no}, #{repleviedCountryCode}, #{repleviedLocation}, #{replevySum}, #{repleviedMobile},
			#{repleviedPhone}, #{repleviedAddress}, #{repleviedPostCode}, #{repleviedTax}, #{repleviedEmail}, 
			#{replevyType}, #{replevyAgency}, #{replevyPerson}, #{replevyPersonTel}, #{replevyDate}, 
			#{replevyHandleDate}, #{transferDate}, #{replevyeFfectDate}, #{replevyWay}, #{replevyReason}, 
			#{realReplevy}, #{replevyFee}, #{status}, #{approveFlag}, #{validFlag}, 
			#{flag}, #{currency}, #{replevyProgress}, #{repleviedArea}, #{repleviedAreaCode}, 
			#{replevyApproveFlag}, #{approvePerson}, #{serialNo}, #{approveDate}, #{createdBy}, 
			#{sysCtime}, #{updatedBy}, #{sysUtime})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="saveClmsReplevyDetail" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail">
		insert into clms_replevy_detail
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="replevyId != null" >
				replevy_id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="replevyNo != null" >
				replevy_no,
			</if>
			<if test="replevyTimes != null" >
				replevy_times,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="clauseCode != null" >
				clause_code,
			</if>
			<if test="kindCode != null" >
				kind_code,
			</if>
			<if test="repleviedName != null" >
				replevied_name,
			</if>
			<if test="repleviedType != null" >
				replevied_type,
			</if>
			<if test="repleviedCertiType != null" >
				replevied_certi_type,
			</if>
			<if test="repleviedCertiCode != null" >
				replevied_certi_code,
			</if>
			<if test="customerNo != null" >
				customer_no,
			</if>
			<if test="repleviedCountryCode != null" >
				replevied_country_code,
			</if>
			<if test="repleviedLocation != null" >
				replevied_location,
			</if>
			<if test="replevySum != null" >
				replevy_sum,
			</if>
			<if test="repleviedMobile != null" >
				replevied_mobile,
			</if>
			<if test="repleviedPhone != null" >
				replevied_phone,
			</if>
			<if test="repleviedAddress != null" >
				replevied_address,
			</if>
			<if test="repleviedPostCode != null" >
				replevied_post_code,
			</if>
			<if test="repleviedTax != null" >
				replevied_tax,
			</if>
			<if test="repleviedEmail != null" >
				replevied_email,
			</if>
			<if test="replevyType != null" >
				replevy_type,
			</if>
			<if test="replevyAgency != null" >
				replevy_agency,
			</if>
			<if test="replevyPerson != null" >
				replevy_person,
			</if>
			<if test="replevyPersonTel != null" >
				replevy_person_tel,
			</if>
			<if test="replevyDate != null" >
				replevy_date,
			</if>
			<if test="replevyHandleDate != null" >
				replevy_handle_date,
			</if>
			<if test="transferDate != null" >
				transfer_date,
			</if>
			<if test="replevyeFfectDate != null" >
				replevye_ffect_date,
			</if>
			<if test="replevyWay != null" >
				replevy_way,
			</if>
			<if test="replevyReason != null" >
				replevy_reason,
			</if>
			<if test="realReplevy != null" >
				real_replevy,
			</if>
			<if test="replevyFee != null" >
				replevy_fee,
			</if>
			<if test="status != null" >
				status,
			</if>
			<if test="approveFlag != null" >
				approve_flag,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="currency != null" >
				currency,
			</if>
			<if test="replevyProgress != null" >
				replevy_progress,
			</if>
			<if test="repleviedArea != null" >
				replevied_area,
			</if>
			<if test="repleviedAreaCode != null" >
				replevied_area_code,
			</if>
			<if test="replevyApproveFlag != null" >
				replevy_approve_flag,
			</if>
			<if test="approvePerson != null" >
				approve_person,
			</if>
			<if test="serialNo != null" >
				serial_no,
			</if>
			<if test="approveDate != null" >
				approve_date,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="replevyId != null" >
				#{replevyId},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="replevyNo != null" >
				#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				#{replevyTimes},
			</if>
		    <if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="clauseCode != null" >
				#{clauseCode},
			</if>
			<if test="kindCode != null" >
				#{kindCode},
			</if>
			<if test="repleviedName != null" >
				#{repleviedName},
			</if>
			<if test="repleviedType != null" >
				#{repleviedType},
			</if>
			<if test="repleviedCertiType != null" >
				#{repleviedCertiType},
			</if>
			<if test="repleviedCertiCode != null" >
				#{repleviedCertiCode},
			</if>
			<if test="repleviedCountryCode != null" >
				#{repleviedCountryCode},
			</if>
			<if test="repleviedLocation != null" >
				#{repleviedLocation},
			</if>
			<if test="replevySum != null" >
				#{replevySum},
			</if>
			<if test="repleviedMobile != null" >
				#{repleviedMobile},
			</if>
			<if test="repleviedPhone != null" >
				#{repleviedPhone},
			</if>
			<if test="repleviedAddress != null" >
				#{repleviedAddress},
			</if>
			<if test="repleviedPostCode != null" >
				#{repleviedPostCode},
			</if>
			<if test="repleviedTax != null" >
				#{repleviedTax},
			</if>
			<if test="repleviedEmail != null" >
				#{repleviedEmail},
			</if>
			<if test="replevyType != null" >
				#{replevyType},
			</if>
			<if test="replevyAgency != null" >
				#{replevyAgency},
			</if>
			<if test="replevyPerson != null" >
				#{replevyPerson},
			</if>
			<if test="replevyPersonTel != null" >
				#{replevyPersonTel},
			</if>
			<if test="replevyDate != null" >
				#{replevyDate},
			</if>
			<if test="replevyHandleDate != null" >
				#{replevyHandleDate},
			</if>
			<if test="transferDate != null" >
				#{transferDate},
			</if>
			<if test="replevyeFfectDate != null" >
				#{replevyeFfectDate},
			</if>
			<if test="replevyWay != null" >
				#{replevyWay},
			</if>
			<if test="replevyReason != null" >
				#{replevyReason},
			</if>
			<if test="realReplevy != null" >
				#{realReplevy},
			</if>
			<if test="replevyFee != null" >
				#{replevyFee},
			</if>
			<if test="status != null" >
				#{status},
			</if>
			<if test="approveFlag != null" >
				#{approveFlag},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="replevyProgress != null" >
				#{replevyProgress},
			</if>
			<if test="repleviedArea != null" >
				#{repleviedArea},
			</if>
			<if test="repleviedAreaCode != null" >
				#{repleviedAreaCode},
			</if>
			<if test="replevyApproveFlag != null" >
				#{replevyApproveFlag},
			</if>
			<if test="approvePerson != null" >
				#{approvePerson},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="approveDate != null" >
				#{approveDate},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail">
		update clms_replevy_detail
		<set>
			<if test="replevyId != null" >
				replevy_id=#{replevyId},
			</if>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="replevyNo != null" >
				replevy_no=#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				replevy_times=#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				case_times = #{caseTimes},
			</if>
			<if test="clauseCode != null" >
				clause_code=#{clauseCode},
			</if>
			<if test="kindCode != null" >
				kind_code=#{kindCode},
			</if>
			<if test="repleviedName != null" >
				replevied_name=#{repleviedName},
			</if>
			<if test="repleviedType != null" >
				replevied_type=#{repleviedType},
			</if>
			<if test="repleviedCertiType != null" >
				replevied_certi_type=#{repleviedCertiType},
			</if>
			<if test="repleviedCertiCode != null" >
				replevied_certi_code=#{repleviedCertiCode},
			</if>
			<if test="customerNo != null" >
				customer_no=#{customerNo},
			</if>
			<if test="repleviedCountryCode != null" >
				replevied_country_code=#{repleviedCountryCode},
			</if>
			<if test="repleviedLocation != null" >
				replevied_location=#{repleviedLocation},
			</if>
			<if test="replevySum != null" >
				replevy_sum=#{replevySum},
			</if>
			<if test="repleviedMobile != null" >
				replevied_mobile=#{repleviedMobile},
			</if>
			<if test="repleviedPhone != null" >
				replevied_phone=#{repleviedPhone},
			</if>
			<if test="repleviedAddress != null" >
				replevied_address=#{repleviedAddress},
			</if>
			<if test="repleviedPostCode != null" >
				replevied_post_code=#{repleviedPostCode},
			</if>
			<if test="repleviedTax != null" >
				replevied_tax=#{repleviedTax},
			</if>
			<if test="repleviedEmail != null" >
				replevied_email=#{repleviedEmail},
			</if>
			<if test="replevyType != null" >
				replevy_type=#{replevyType},
			</if>
			<if test="replevyAgency != null" >
				replevy_agency=#{replevyAgency},
			</if>
			<if test="replevyPerson != null" >
				replevy_person=#{replevyPerson},
			</if>
			<if test="replevyPersonTel != null" >
				replevy_person_tel=#{replevyPersonTel},
			</if>
			<if test="replevyDate != null" >
				replevy_date=#{replevyDate},
			</if>
			<if test="replevyHandleDate != null" >
				replevy_handle_date=#{replevyHandleDate},
			</if>
			<if test="transferDate != null" >
				transfer_date=#{transferDate},
			</if>
			<if test="replevyeFfectDate != null" >
				replevye_ffect_date=#{replevyeFfectDate},
			</if>
			<if test="replevyWay != null" >
				replevy_way=#{replevyWay},
			</if>
			<if test="replevyReason != null" >
				replevy_reason=#{replevyReason},
			</if>
			<if test="realReplevy != null" >
				real_replevy=#{realReplevy},
			</if>
			<if test="replevyFee != null" >
				replevy_fee=#{replevyFee},
			</if>
			<if test="status != null" >
				status=#{status},
			</if>
			<if test="approveFlag != null" >
				approve_flag=#{approveFlag},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="currency != null" >
				currency=#{currency},
			</if>
			<if test="replevyProgress != null" >
				replevy_progress=#{replevyProgress},
			</if>
			<if test="repleviedArea != null" >
				replevied_area=#{repleviedArea},
			</if>
			<if test="repleviedAreaCode != null" >
				replevied_area_code=#{repleviedAreaCode},
			</if>
			<if test="replevyApproveFlag != null" >
				replevy_approve_flag=#{replevyApproveFlag},
			</if>
			<if test="approvePerson != null" >
				approve_person=#{approvePerson},
			</if>
			<if test="serialNo != null" >
				serial_no=#{serialNo},
			</if>
			<if test="approveDate != null" >
				approve_date=#{approveDate},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail">
		update clms_replevy_detail
		set replevy_id=#{replevyId},
			report_no=#{reportNo},
			replevy_no=#{replevyNo},
			replevy_times=#{replevyTimes},
		    case_times = #{caseTimes},
			clause_code=#{clauseCode},
			kind_code=#{kindCode},
			replevied_name=#{repleviedName},
			replevied_type=#{repleviedType},
			replevied_certi_type=#{repleviedCertiType},
			replevied_certi_code=#{repleviedCertiCode},
			customer_no= #{customerNo},
			replevied_country_code=#{repleviedCountryCode},
			replevied_location=#{repleviedLocation},
			replevy_sum=#{replevySum},
			replevied_mobile=#{repleviedMobile},
			replevied_phone=#{repleviedPhone},
			replevied_address=#{repleviedAddress},
			replevied_post_code=#{repleviedPostCode},
			replevied_tax=#{repleviedTax},
			replevied_email=#{repleviedEmail},
			replevy_type=#{replevyType},
			replevy_agency=#{replevyAgency},
			replevy_person=#{replevyPerson},
			replevy_person_tel=#{replevyPersonTel},
			replevy_date=#{replevyDate},
			replevy_handle_date=#{replevyHandleDate},
			transfer_date=#{transferDate},
			replevye_ffect_date=#{replevyeFfectDate},
			replevy_way=#{replevyWay},
			replevy_reason=#{replevyReason},
			real_replevy=#{realReplevy},
			replevy_fee=#{replevyFee},
			status=#{status},
			approve_flag=#{approveFlag},
			valid_flag=#{validFlag},
			flag=#{flag},
			currency=#{currency},
			replevy_progress=#{replevyProgress},
			replevied_area=#{repleviedArea},
			replevied_area_code=#{repleviedAreaCode},
			replevy_approve_flag=#{replevyApproveFlag},
			approve_person=#{approvePerson},
			serial_no=#{serialNo},
			approve_date=#{approveDate},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	
	<!-- 根据ID全量更新clms_replevy_detail表 -->
	<update id="updateClmsReplevyDetailById" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail">
		update clms_replevy_detail
		set 
			replevy_id=#{replevyId},
			report_no=#{reportNo},
			replevy_no=#{replevyNo},
			replevy_times=#{replevyTimes},
		    case_times = #{caseTimes},
			clause_code=#{clauseCode},
			kind_code=#{kindCode},
			replevied_name=#{repleviedName},
			replevied_type=#{repleviedType},
			replevied_certi_type=#{repleviedCertiType},
			replevied_certi_code=#{repleviedCertiCode},
			customer_no= #{customerNo},
			replevied_country_code=#{repleviedCountryCode},
			replevied_location=#{repleviedLocation},
			replevy_sum=#{replevySum},
			replevied_mobile=#{repleviedMobile},
			replevied_phone=#{repleviedPhone},
			replevied_address=#{repleviedAddress},
			replevied_post_code=#{repleviedPostCode},
			replevied_tax=#{repleviedTax},
			replevied_email=#{repleviedEmail},
			replevy_type=#{replevyType},
			replevy_agency=#{replevyAgency},
			replevy_person=#{replevyPerson},
			replevy_person_tel=#{replevyPersonTel},
			replevy_date=#{replevyDate},
			replevy_handle_date=#{replevyHandleDate},
			transfer_date=#{transferDate},
			replevye_ffect_date=#{replevyeFfectDate},
			replevy_way=#{replevyWay},
			replevy_reason=#{replevyReason},
			real_replevy=#{realReplevy},
			replevy_fee=#{replevyFee},
			status=#{status},
			approve_flag=#{approveFlag},
			valid_flag=#{validFlag},
			flag=#{flag},
			currency=#{currency},
			replevy_progress=#{replevyProgress},
			replevied_area=#{repleviedArea},
			replevied_area_code=#{repleviedAreaCode},
			replevy_approve_flag=#{replevyApproveFlag},
			approve_person=#{approvePerson},
			serial_no=#{serialNo},
			approve_date=#{approveDate},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=now()
		where id = #{id}
	</update>
	
	<select id="selectClmsReplevyDetail" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyDetailVo" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_detail
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and	report_no=#{reportNo}
		</if>
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		and valid_flag='Y'
		order by sys_ctime asc
	</select>
</mapper>