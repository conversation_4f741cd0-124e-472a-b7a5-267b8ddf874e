<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSecondUnderwritingMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity" id="ClmsSecondUnderwritingMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/>
        <result property="accidentDate" column="accident_date" jdbcType="TIMESTAMP"/>
        <result property="diseaseInfo" column="disease_info" jdbcType="VARCHAR"/>
        <result property="evidenceMaterialType" column="evidence_material_type" jdbcType="VARCHAR"/>
        <result property="materialFileId" column="material_file_id" jdbcType="VARCHAR"/>
        <result property="underwritingExplain" column="underwriting_explain" jdbcType="VARCHAR"/>
        <result property="underwritingStatus" column="underwriting_status" jdbcType="VARCHAR"/>
        <result property="uwStartDate" column="uw_start_date" jdbcType="TIMESTAMP"/>
        <result property="uwCompleteDate" column="uw_complete_date" jdbcType="TIMESTAMP"/>
        <result property="uwAdvice" column="uw_advice" jdbcType="VARCHAR"/>
        <result property="conclusion" column="conclusion" jdbcType="VARCHAR"/>
        <result property="uwOperator" column="uw_operator" jdbcType="VARCHAR"/>
        <result property="taskCode" column="task_code" jdbcType="VARCHAR"/>
        <result property="manualInfoId" column="manual_info_id" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="lettersCancelStatus" column="letters_cancel_status" jdbcType="VARCHAR"/>
        <result property="waitingPeriodDays" column="waiting_period_days" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,report_no,case_times,serial_no,accident_date,disease_info,evidence_material_type,material_file_id,
        underwriting_explain,underwriting_status,uw_start_date,uw_complete_date,uw_advice,uw_operator,manual_info_id,
        created_by,created_date,updated_by,updated_date,task_code,letters_cancel_status,waiting_period_days
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsSecondUnderwritingMap">
        select
        <include refid="Base_Column_List"/>
        from clms_second_underwriting
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsSecondUnderwritingMap">
        select
        <include refid="Base_Column_List"/>
        from clms_second_underwriting
        where report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != ''">
            and case_times = #{caseTimes}
        </if>
        order by serial_no
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_second_underwriting(
        id,
        report_no,
        case_times,
        serial_no,
        disease_info,
        evidence_material_type,
        material_file_id,
        underwriting_explain,
        underwriting_status,
        uw_start_date,
        created_by,
        created_date,
        updated_by,
        updated_date,
        accident_date,
        task_code,
        manual_info_id,
        waiting_period_days)
        values(
        #{id},
        #{reportNo},
        #{caseTimes},
        #{serialNo},
        #{diseaseInfo},
        #{evidenceMaterialType},
        #{materialFileId},
        #{underwritingExplain},
        #{underwritingStatus},
        #{uwStartDate},
        #{createdBy},
        #{createdDate},
        #{updatedBy},
        #{updatedDate},
        #{accidentDate},
        #{taskCode},
        #{manualInfoId},
        #{waitingPeriodDays})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_second_underwriting
        <set>
            <if test="lettersCancelStatus != null and lettersCancelStatus != ''">
                letters_cancel_status = #{lettersCancelStatus},
            </if>
            <if test="uwOperator != null and uwOperator != ''">
                uw_operator = #{uwOperator},
            </if>
            <if test="uwAdvice != null and uwAdvice != ''">
                uw_advice = #{uwAdvice},
            </if>
            <if test="underwritingStatus != null and underwritingStatus != ''">
                underwriting_status = #{underwritingStatus},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="uwCompleteDate!= null">
                uw_complete_date=#{uwCompleteDate},
            </if>
            <if test="manualInfoId != null and manualInfoId !='' ">
                manual_info_id=#{manualInfoId},
            </if>
            <if test="conclusion != null and conclusion !=''">
                conclusion=#{conclusion}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getUWRecord"  resultMap="ClmsSecondUnderwritingMap">
        select <include refid="Base_Column_List" />
        from clms_second_underwriting
        where report_no =#{reportNo}
        and case_times =#{caseTimes}
        and underwriting_status='01'
    </select>

    <select id="getUnderwritingPassRecord"  resultType="java.lang.Integer">
        select count(*)
        from clms_second_underwriting
        where report_no =#{reportNo}
        and case_times =#{caseTimes}
        and underwriting_status='02'
        and letters_cancel_status = '02'
    </select>

    <select id="getUwInfoOrderBySerialNo" resultMap="ClmsSecondUnderwritingMap">
        select <include refid="Base_Column_List" />
        from clms_second_underwriting
        where report_no=#{reportNo}
        <if test="caseTimes != null and caseTimes != ''">
            and case_times =#{caseTimes}
        </if>
        and underwriting_status='02'
        order by serial_no desc limit 1
    </select>

    <select id="getUwsLetterCode" resultType="string">
        select GROUP_CONCAT(letter_Code SEPARATOR ',') letterCode from uws_letter_info where id_ums_manual_info = #{manualInfoId}
    </select>

    <select id="getSecondUWRecord" resultMap="ClmsSecondUnderwritingMap">
        SELECT SU.ID,
               SU.REPORT_NO,
               SU.CASE_TIMES,
               SU.SERIAL_NO,
               SU.ACCIDENT_DATE,
               SU.DISEASE_INFO,
               SU.EVIDENCE_MATERIAL_TYPE,
               SU.MATERIAL_FILE_ID,
               SU.UNDERWRITING_EXPLAIN,
               SU.UNDERWRITING_STATUS,
               SU.UW_START_DATE,
               SU.UW_COMPLETE_DATE,
               SU.UW_ADVICE,
               SU.UW_OPERATOR,
               SU.MANUAL_INFO_ID,
               SU.CREATED_BY,
               SU.CREATED_DATE,
               SU.UPDATED_BY,
               SU.UPDATED_DATE,
               SU.TASK_CODE,
               SU.LETTERS_CANCEL_STATUS
        FROM CLMS_SECOND_UNDERWRITING SU
                 JOIN CLM_CASE_BASE CB ON CB.REPORT_NO = SU.REPORT_NO
            AND CB.CASE_TIMES = SU.CASE_TIMES
                 JOIN CLMS_REPORT_CUSTOMER RC ON RC.REPORT_NO = CB.REPORT_NO
        WHERE (CB.POLICY_NO, RC.CLIENT_NO) IN (SELECT CB1.POLICY_NO,
                                                      RC1.CLIENT_NO
                                               FROM CLM_CASE_BASE CB1
                                                        JOIN CLMS_REPORT_CUSTOMER RC1 ON RC1.REPORT_NO = CB1.REPORT_NO
                                               WHERE CB1.REPORT_NO = #{reportNo}
                                                 AND CB1.CASE_TIMES = #{caseTimes})
            <if test="underwritingStatus != null and underwritingStatus.size() > 0">
                AND SU.UNDERWRITING_STATUS IN
                <foreach collection="underwritingStatus" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
    </select>

    <select id="getRejectSecondUWByClientNo" resultMap="ClmsSecondUnderwritingMap">
        SELECT SU.ID,
               SU.REPORT_NO,
               SU.CASE_TIMES,
               SU.SERIAL_NO,
               SU.ACCIDENT_DATE,
               SU.DISEASE_INFO,
               SU.EVIDENCE_MATERIAL_TYPE,
               SU.MATERIAL_FILE_ID,
               SU.UNDERWRITING_EXPLAIN,
               SU.UNDERWRITING_STATUS,
               SU.UW_START_DATE,
               SU.UW_COMPLETE_DATE,
               SU.UW_ADVICE,
               SU.CONCLUSION,
               SU.UW_OPERATOR,
               SU.MANUAL_INFO_ID,
               SU.CREATED_BY,
               SU.CREATED_DATE,
               SU.UPDATED_BY,
               SU.UPDATED_DATE,
               SU.TASK_CODE,
               SU.LETTERS_CANCEL_STATUS
        FROM CLMS_SECOND_UNDERWRITING SU
                 JOIN CLMS_REPORT_CUSTOMER RC ON RC.REPORT_NO = SU.REPORT_NO
        WHERE RC.CLIENT_NO = #{clientNo}
          AND SU.UNDERWRITING_STATUS IN ('02', '03')
          AND EXISTS (SELECT 1
                      FROM CLMS_SECONDUW_POLICY_CONCLUSION SPC
                               JOIN CLMS_SECONDUW_PLAN_CONCLUSION SPLC
                                    ON SPLC.ID_CLMS_SECONDUW_POLICY_CONCLUSION = SPC.ID
                      WHERE SPC.ID_CLMS_SECOND_UNDERWRITING = SU.ID
                        AND (SPC.UW_CONCLUSION = '2' OR splc.UW_DECISIONS = '2'))
    </select>
</mapper>

