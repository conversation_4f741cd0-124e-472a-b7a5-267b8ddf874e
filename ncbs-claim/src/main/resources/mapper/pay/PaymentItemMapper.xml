<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper">

	<resultMap id="resultDTO" type="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		<id column="ID_CLM_PAYMENT_ITEM" property="idClmPaymentItem"/>
		<result column="POLICY_NO" property="policyNo"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="CASE_NO" property="caseNo"/>
		<result column="ID_CLM_BATCH" property="idClmBatch"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="CLAIM_TYPE" property="claimType"/>
		<result column="SUB_TIMES"  property="subTimes"/>
		<result column="PAYMENT_TYPE"  property="paymentType"/>
		<result column="ID_CLM_PAYMENT_INFO"  property="idClmPaymentInfo"/>
		<result column="COLLECT_PAY_SIGN"  property="collectPaySign"/>
		<result column="PAYMENT_AMOUNT"  property="paymentAmount"/>
		<result column="PAYMENT_CURRENCY_CODE"  property="paymentCurrencyCode"/>
		<result column="CLIENT_NAME"  property="clientName"/>
		<result column="CLIENT_CERTIFICATE_TYPE"  property="clientCertificateType"/>
		<result column="CLIENT_CERTIFICATE_NO"  property="clientCertificateNo"/>
		<result column="CLIENT_BANK_CODE"  property="clientBankCode"/>
		<result column="CLIENT_BANK_NAME"  property="clientBankName"/>
		<result column="CLIENT_BANK_ACCOUNT"  property="clientBankAccount"/>
		<result column="CLIENT_MOBILE"  property="clientMobile"/>
		<result column="CLIENT_TYPE"  property="clientType"/>
		<result column="PROVINCE_NAME"  property="provinceName"/>
		<result column="CITY_NAME"  property="cityName"/>
		<result column="REGION_CODE"  property="regionCode"/>
		<result column="COLLECT_PAY_APPROACH"  property="collectPayApproach"/>
		<result column="BANK_ACCOUNT_ATTRIBUTE"  property="bankAccountAttribute"/>
		<result column="MERGE_SIGN"  property="mergeSign"/>
		<result column="PAYMENT_ITEM_STATUS"  property="paymentItemStatus"/>
		<result column="EFFECTIVE_DATE"  property="effectiveDate"/>
		<result column="INVALIDATE_DATE"  property="invalidateDate"/>
		<result column="REMARK"  property="remark"/>
		<result column="IS_COINSURE"  property="isCoinsure"/>
		<result column="MIGRATE_FROM"  property="migrateFrom"/>
		<result column="COLLECT_PAY_NO"  property="collectPayNo"/>
		<result column="HIS_ID_CLM_PAYMENT_NOTICE"  property="hisIdClmPaymentNotice"/>
		<result column="ID_CLM_SUBROGATION_SETTLE"  property="idClmSubrogationSettle"/>
		<result column="EXCHANGE_RATE"  property="exchangeRate"/>
		<result column="CONVERT_AMOUNT"  property="convertAmount"/>
		<result column="EXTEND_INFO"  property="extendInfo"/>
		<result column="BANK_DETAIL"  property="bankDetail"/>
		<result column="CREATED_BY"  property="createdBy"/>
		<result column="CREATED_DATE"  property="createdDate"/>
		<result column="UPDATED_BY"  property="updatedBy"/>
		<result column="UPDATED_DATE"  property="updatedDate"/>
		<result column="MODIFY_INFO"  property="modifyInfo"/>
		<result column="SERIAL_NO"  property="serialNo"/>
		<result column="ORGANIZE_CODE"  property="organizeCode"/>
		<result column="COMPENSATE_NO"  property="compensateNo"/>
		<result column="PAY_DATE"  property="payDate"/>
		<result column="PAY_BACK_DATE"  property="payBackDate"/>
		<result column="FINANCIAL_ID"  property="financialId"/>
		<result column="COMPENSATE_NO"  property="compensateNo"/>
		<result column="PAY_DATE"  property="payDate"/>
		<result column="PAY_BACK_DATE"  property="payBackDate"/>
		<result column="FINANCIAL_ID"  property="financialId"/>
		<result column="CLIENT_RELATION"  property="clientRelation"/>
		<result column="BANK_DETAIL_CODE"  property="bankDetailCode"/>
		<result column="OPEN_ID" property="openId"/>
		<result column="PAY_TYPE" property="payType"/>
		<result column="FEE_TYPE"  property="feeType"/>
		<result column="COINSURANCE_MARK"  property="coinsuranceMark"/>
		<result column="ACCEPT_INSURANCE_FLAG"  property="acceptInsuranceFlag"/>
		<result column="COINSURANCE_COMPANY_CODE"  property="coinsuranceCompanyCode"/>
		<result column="COINSURANCE_COMPANY_NAME"  property="coinsuranceCompanyName"/>
		<result column="COINSURANCE_RATIO"  property="coinsuranceRatio"/>
		<result column="IS_FULL_PAY"  property="isFullPay"/>
		<result column="COINSURANCE_ACTUAL_AMOUNT"  property="coinsuranceActualAmount"/>
		<result column="FINANCE_PAYMENT_AMOUNT" property="financePaymentAmount"/>
		<result column="PAYMENT_VOUCHER_URL" property="paymentVoucherUrl"/>
		<result column="WHOLE_CASE_STATUS" property="wholeCaseStatus"/>
		<result column="PRODUCT_CODE" property="productCode"/>
	</resultMap>

	<resultMap id="mergePaymentResultMap" type="com.paic.ncbs.claim.model.dto.pay.BatchPaymentDetailInfo">
		<id column="id_clm_payment_item" property="businessNo"/>
		<id column="report_no" property="reportNo"/>
		<id column="case_times" property="caseTimes"/>
		<result column="payment_amount" property="planFee"/>
		<result column="plan_code" property="riskCode"/>
		<result column="kind_code" property="kindCode"/>
		<result column="product_code" property="productCode"/>
		<result column="product_line_code" property="productLineCode"/>
	</resultMap>

	<sql id="Base_Column_List">
			ID_CLM_PAYMENT_ITEM,POLICY_NO,REPORT_NO,CASE_NO,ID_CLM_BATCH,CASE_TIMES,CLAIM_TYPE,SUB_TIMES,PAYMENT_TYPE,ID_CLM_PAYMENT_INFO,
			COLLECT_PAY_SIGN,PAYMENT_AMOUNT,PAYMENT_CURRENCY_CODE,CLIENT_NAME,CLIENT_CERTIFICATE_TYPE,CLIENT_CERTIFICATE_NO,
			CLIENT_BANK_CODE,CLIENT_BANK_NAME,CLIENT_BANK_ACCOUNT,CLIENT_MOBILE,CLIENT_TYPE,PROVINCE_NAME,CITY_NAME,REGION_CODE,
			COLLECT_PAY_APPROACH,BANK_ACCOUNT_ATTRIBUTE,MERGE_SIGN,PAYMENT_ITEM_STATUS,EFFECTIVE_DATE,INVALIDATE_DATE,
            REMARK,IS_COINSURE,MIGRATE_FROM,COLLECT_PAY_NO,HIS_ID_CLM_PAYMENT_NOTICE,ID_CLM_SUBROGATION_SETTLE,EXCHANGE_RATE,CONVERT_AMOUNT,
			EXTEND_INFO ,BANK_DETAIL,CREATED_BY,CREATED_DATE,UPDATED_BY,UPDATED_DATE,MODIFY_INFO,PAY_DATE,PAY_BACK_DATE,FINANCIAL_ID,
			COMPENSATE_NO,SERIAL_NO,ORGANIZE_CODE,CLIENT_RELATION,BANK_DETAIL_CODE,OPEN_ID,PAY_TYPE,FEE_TYPE,
			COINSURANCE_MARK,ACCEPT_INSURANCE_FLAG,COINSURANCE_COMPANY_CODE,COINSURANCE_COMPANY_NAME,COINSURANCE_RATIO,IS_FULL_PAY,COINSURANCE_ACTUAL_AMOUNT,
			FINANCE_PAYMENT_AMOUNT,PAYMENT_VOUCHER_URL
	</sql>

	<insert id="addPaymentItem" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		INSERT INTO CLM_PAYMENT_ITEM(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_CLM_PAYMENT_ITEM,
			POLICY_NO,
			REPORT_NO,
			CASE_NO,
			ID_CLM_BATCH,
			CASE_TIMES,
			CLAIM_TYPE,
			SUB_TIMES,
			PAYMENT_TYPE,
			ID_CLM_PAYMENT_INFO,
			COLLECT_PAY_SIGN,
			PAYMENT_AMOUNT,
			PAYMENT_CURRENCY_CODE,
			CLIENT_NAME,
			CLIENT_CERTIFICATE_TYPE,
			CLIENT_CERTIFICATE_NO,
			CLIENT_BANK_CODE,
			CLIENT_BANK_NAME,
			CLIENT_BANK_ACCOUNT,
			CLIENT_MOBILE,
			CLIENT_TYPE,
			PROVINCE_NAME,
			CITY_NAME,
			REGION_CODE,
			COLLECT_PAY_APPROACH,
			BANK_ACCOUNT_ATTRIBUTE,
			MERGE_SIGN,
			PAYMENT_ITEM_STATUS,
			EFFECTIVE_DATE,
			INVALIDATE_DATE,
			REMARK,
			IS_COINSURE,
			MIGRATE_FROM,
			COLLECT_PAY_NO,
			HIS_ID_CLM_PAYMENT_NOTICE,
			ID_CLM_SUBROGATION_SETTLE,
			EXCHANGE_RATE,
			CONVERT_AMOUNT,
			EXTEND_INFO,
		    BANK_DETAIL,
		    COMPENSATE_NO,
			SERIAL_NO,
			ORGANIZE_CODE,
			CLIENT_RELATION,
			BANK_DETAIL_CODE,
			AGENCY_TYPE,
			CUSTOMER_NO,
			COMPANY_CARD_TYPE,
			FEE_TYPE,
			PAY_TYPE,
			OPEN_ID,
			COINSURANCE_MARK,
			ACCEPT_INSURANCE_FLAG,
			COINSURANCE_COMPANY_CODE,
			COINSURANCE_COMPANY_NAME,
			COINSURANCE_RATIO,
			IS_FULL_PAY,
			COINSURANCE_ACTUAL_AMOUNT,
			FINANCE_PAYMENT_AMOUNT,
			PAYMENT_VOUCHER_URL)
		VALUES(
			#{createdBy,jdbcType=VARCHAR},
			now(),
			#{updatedBy,jdbcType=VARCHAR},
			now(),
			#{idClmPaymentItem,jdbcType=VARCHAR},
			#{policyNo,jdbcType=VARCHAR},
			#{reportNo,jdbcType=VARCHAR},
			#{caseNo,jdbcType=VARCHAR},
			#{idClmBatch,jdbcType=VARCHAR},
			#{caseTimes,jdbcType=NUMERIC},
			#{claimType,jdbcType=VARCHAR},
			#{subTimes,jdbcType=NUMERIC},
			#{paymentType,jdbcType=VARCHAR},
			#{idClmPaymentInfo,jdbcType=VARCHAR},
			#{collectPaySign,jdbcType=VARCHAR},
			#{paymentAmount,jdbcType=DECIMAL},
			#{paymentCurrencyCode,jdbcType=VARCHAR},
			#{clientName,jdbcType=VARCHAR},
			#{clientCertificateType,jdbcType=VARCHAR},
			#{clientCertificateNo,jdbcType=VARCHAR},
			#{clientBankCode,jdbcType=VARCHAR},
			#{clientBankName,jdbcType=VARCHAR},
			#{clientBankAccount,jdbcType=VARCHAR},
			#{clientMobile,jdbcType=VARCHAR},
			#{clientType,jdbcType=VARCHAR},
			#{provinceName,jdbcType=VARCHAR},
			#{cityName,jdbcType=VARCHAR},
			#{regionCode,jdbcType=VARCHAR},
			#{collectPayApproach,jdbcType=VARCHAR},
			#{bankAccountAttribute,jdbcType=VARCHAR},
			#{mergeSign,jdbcType=VARCHAR},
			#{paymentItemStatus,jdbcType=VARCHAR},
			#{effectiveDate,jdbcType=TIMESTAMP},
			#{invalidateDate,jdbcType=TIMESTAMP},
			#{remark,jdbcType=VARCHAR},
			#{isCoinsure,jdbcType=VARCHAR},
			#{migrateFrom,jdbcType=VARCHAR},
			#{collectPayNo,jdbcType=VARCHAR},
			#{hisIdClmPaymentNotice,jdbcType=VARCHAR},
			#{idClmSubrogationSettle,jdbcType=VARCHAR},
			#{exchangeRate,jdbcType=VARCHAR},
			#{convertAmount,jdbcType=VARCHAR},
			#{extendInfo,jdbcType=VARCHAR},
			#{bankDetail,jdbcType=VARCHAR},
			#{compensateNo,jdbcType=VARCHAR},
			#{serialNo,jdbcType=NUMERIC},
			#{organizeCode,jdbcType=VARCHAR},
			#{clientRelation,jdbcType=VARCHAR},
			#{bankDetailCode,jdbcType=VARCHAR},
			#{agencyType,jdbcType=VARCHAR},
			#{customerNo,jdbcType=VARCHAR},
			#{companyCardType,jdbcType=VARCHAR},
			#{feeType,jdbcType=VARCHAR},
			#{payType,jdbcType=VARCHAR},
			#{openId,jdbcType=VARCHAR},
			#{coinsuranceMark,jdbcType=VARCHAR},
			#{acceptInsuranceFlag,jdbcType=VARCHAR},
			#{coinsuranceCompanyCode,jdbcType=VARCHAR},
			#{coinsuranceCompanyName,jdbcType=VARCHAR},
			#{coinsuranceRatio,jdbcType=DECIMAL},
			#{isFullPay,jdbcType=VARCHAR},
			#{coinsuranceActualAmount,jdbcType=DECIMAL},
			#{financePaymentAmount,jdbcType=DECIMAL},
			#{paymentVoucherUrl,jdbcType=VARCHAR}
			  )
	</insert>

	<select id="getPaymentItem" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		SELECT
		<include refid="Base_Column_List"/>
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS not in  ('90','20')
		<if test="reportNo != null">
			AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="caseTimes != null">
			AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		</if>
		<if test="clientName != null">
			AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
		</if>
		<if test="clientBankAccount != null">
			AND CLIENT_BANK_ACCOUNT = #{clientBankAccount}
		</if>
		<if test=" serialNo != null ">
			AND SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
		</if>
		<if test="compensateNo != null">
			AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentInfo != null">
			AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentItem != null">
			AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
		</if>
		<if test="caseNo != null">
			AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null">
			AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="claimType != null">
			AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		</if>
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="subTimes != null">
			AND SUB_TIMES = #{subTimes,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceCompanyCode != null">
			AND COINSURANCE_COMPANY_CODE = #{coinsuranceCompanyCode,jdbcType=VARCHAR}
		</if>
		order by POLICY_NO,REPORT_NO,CASE_TIMES

	</select>

	<select id="getHisPaymentItem" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		SELECT
		<include refid="Base_Column_List"/>
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS not in  ('90','20','10')
		<if test="reportNo != null">
			AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="caseTimes != null">
			AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		</if>
		<if test="clientName != null">
			AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
		</if>
		<if test="clientBankAccount != null">
			AND CLIENT_BANK_ACCOUNT = #{clientBankAccount}
		</if>
		<if test=" serialNo != null ">
			AND SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
		</if>
		<if test="compensateNo != null">
			AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentInfo != null">
			AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentItem != null">
			AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
		</if>
		<if test="caseNo != null">
			AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null">
			AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="claimType != null">
			AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		</if>
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="subTimes != null">
			AND SUB_TIMES = #{subTimes,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceCompanyCode != null">
			AND COINSURANCE_COMPANY_CODE = #{coinsuranceCompanyCode,jdbcType=VARCHAR}
		</if>
		order by POLICY_NO,REPORT_NO,CASE_TIMES

	</select>

	<select id="getPaymentItemByReportNo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		SELECT
		<include refid="Base_Column_List"/>
		<![CDATA[
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS not in  ('90','20')
			AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
			AND CASE_TIMES != #{caseTimes,jdbcType=NUMERIC}
		]]>
	</select>

	<select id="getPaymentItemPayByReportNoAndPayType" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		SELECT
		<include refid="Base_Column_List"/>
		FROM CLM_PAYMENT_ITEM
		WHERE  REPORT_NO = #{reportNo,jdbcType=VARCHAR}
			AND PAYMENT_TYPE = '13'
			<if test="caseTimes != null">
				AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
			</if>
			order by pay_date desc
	</select>

	<update id="updatePaymentItem" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		UPDATE CLM_PAYMENT_ITEM
		SET UPDATED_DATE = now(),
		UPDATED_BY = #{updatedBy,jdbcType=VARCHAR}
		<if test="serialNo != null">
			, SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
		</if>
		<if test="organizeCode != null">
			, ORGANIZE_CODE = #{organizeCode,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			, PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="clientName != null">
			, CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
		</if>
		<if test="paymentAmount != null">
			, PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL}
		</if>
		<if test="policyNo != null">
			, POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="hisIdClmPaymentNotice != null">
			, HIS_ID_CLM_PAYMENT_NOTICE = #{hisIdClmPaymentNotice,jdbcType=VARCHAR}
		</if>
		<if test="extendInfo != null">
			, EXTEND_INFO = #{extendInfo,jdbcType=VARCHAR}
		</if>
		<if test="bankDetail != null">
			, BANK_DETAIL = #{bankDetail,jdbcType=VARCHAR}
		</if>
		<if test="clientName != null">, CLIENT_NAME = #{clientName,jdbcType=VARCHAR} </if>
		<if test="effectiveDate != null">, EFFECTIVE_DATE = #{effectiveDate,jdbcType=TIMESTAMP} </if>
		<if test="clientCertificateType != null">, CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR} </if>
		<if test="clientCertificateNo != null">, CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR} </if>
		<if test="clientBankCode != null">, CLIENT_BANK_CODE = #{clientBankCode,jdbcType=VARCHAR} </if>
		<if test="clientBankName != null">, CLIENT_BANK_NAME = #{clientBankName,jdbcType=VARCHAR} </if>
		<if test="clientBankAccount != null">, CLIENT_BANK_ACCOUNT = #{clientBankAccount,jdbcType=VARCHAR} </if>
		<if test="clientMobile != null">, CLIENT_MOBILE = #{clientMobile,jdbcType=VARCHAR} </if>
		<if test="clientType != null">, CLIENT_TYPE = #{clientType,jdbcType=VARCHAR} </if>
		<if test="provinceName != null">, PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR} </if>
		<if test="cityName != null">, CITY_NAME = #{cityName,jdbcType=VARCHAR} </if>
		<if test="regionCode != null">, REGION_CODE = #{regionCode,jdbcType=VARCHAR} </if>
		<if test="invalidateDate != null">, INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP} </if>
		<if test="remark != null">, REMARK = #{remark,jdbcType=VARCHAR} </if>
		<if test="archiveDate != null">, ARCHIVE_DATE = #{archiveDate,jdbcType=TIMESTAMP} </if>
		<if test="modifyInfo != null">, MODIFY_INFO = #{modifyInfo,jdbcType=VARCHAR} </if>
		<if test="payDate != null">, PAY_DATE = #{payDate,jdbcType=TIMESTAMP} </if>
		<if test="payBackDate != null">, PAY_BACK_DATE = #{payBackDate,jdbcType=TIMESTAMP} </if>
		<if test="financialId != null">, FINANCIAL_ID = #{financialId,jdbcType=VARCHAR} </if>
		<if test="compensateNo != null">, COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR} </if>
		<if test="bankDetailCode != null">, BANK_DETAIL_CODE = #{bankDetailCode,jdbcType=VARCHAR}</if>
		<if test="clientRelation != null">, CLIENT_RELATION = #{clientRelation,jdbcType=VARCHAR}</if>
		<if test="customerNo != null">, customer_no = #{customerNo}</if>
		<if test="companyCardType != null and companyCardType!=''">
			,COMPANY_CARD_TYPE=#{companyCardType,jdbcType=VARCHAR}
		</if>
		<if test="feeType != null and feeType!=''">
			,FEE_TYPE=#{feeType,jdbcType=VARCHAR}
		</if>
		<if test="payType != null and payType!=''">
			,pay_type=#{payType,jdbcType=VARCHAR}
		</if>
		<if test="collectPayApproach != null and collectPayApproach!=''">
			,COLLECT_PAY_APPROACH=#{collectPayApproach,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceMark != null and coinsuranceMark!=''">
			,COINSURANCE_MARK=#{coinsuranceMark,jdbcType=VARCHAR}
		</if>
		<if test="acceptInsuranceFlag != null and acceptInsuranceFlag!=''">
			,ACCEPT_INSURANCE_FLAG=#{acceptInsuranceFlag,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceCompanyCode != null and coinsuranceCompanyCode!=''">
			,COINSURANCE_COMPANY_CODE=#{coinsuranceCompanyCode,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceCompanyName != null and coinsuranceCompanyName!=''">
			,COINSURANCE_COMPANY_NAME=#{coinsuranceCompanyName,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceRatio != null">
			,COINSURANCE_RATIO=#{coinsuranceRatio,jdbcType=DECIMAL}
		</if>
		<if test="isFullPay != null and isFullPay!=''">
			,IS_FULL_PAY=#{isFullPay,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceActualAmount != null">
			,COINSURANCE_ACTUAL_AMOUNT=#{coinsuranceActualAmount,jdbcType=DECIMAL}
		</if>
		<if test="financePaymentAmount != null">
			, FINANCE_PAYMENT_AMOUNT = #{financePaymentAmount,jdbcType=DECIMAL}
		</if>
		<if test="paymentVoucherUrl != null">
			, PAYMENT_VOUCHER_URL = #{paymentVoucherUrl,jdbcType=VARCHAR}
		</if>
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		<if test="idClmPaymentInfo != null">
		AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentItem != null">
			AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
		</if>
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		<if test="caseNo != null">
			AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
		</if>

	</update>

	<delete id="delPaymentItem"  parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		delete from CLM_PAYMENT_ITEM
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		<if test="caseNo != null">
			AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
		</if>
		<if test="claimType != null">
			AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		</if>
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		<if test="subTimes != null">
			AND SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
		</if>

	</delete>

	<insert id="addPaymentItemList" parameterType="java.util.List">
		INSERT INTO CLM_PAYMENT_ITEM(
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_CLM_PAYMENT_ITEM,
		POLICY_NO,
		REPORT_NO,
		CASE_NO,
		ID_CLM_BATCH,
		CASE_TIMES,
		CLAIM_TYPE,
		SUB_TIMES,
		PAYMENT_TYPE,
		ID_CLM_PAYMENT_INFO,
		COLLECT_PAY_SIGN,
		PAYMENT_AMOUNT,
		PAYMENT_CURRENCY_CODE,
		CLIENT_NAME,
		CLIENT_CERTIFICATE_TYPE,
		CLIENT_CERTIFICATE_NO,
		CLIENT_BANK_CODE,
		CLIENT_BANK_NAME,
		CLIENT_BANK_ACCOUNT,
		CLIENT_MOBILE,
		CLIENT_TYPE,
		PROVINCE_NAME,
		CITY_NAME,
		REGION_CODE,
		COLLECT_PAY_APPROACH,
		BANK_ACCOUNT_ATTRIBUTE,
		MERGE_SIGN,
		PAYMENT_ITEM_STATUS,
		EFFECTIVE_DATE,
		INVALIDATE_DATE,
		REMARK,
		IS_COINSURE,
		MIGRATE_FROM,
		COLLECT_PAY_NO,
		HIS_ID_CLM_PAYMENT_NOTICE,
		ID_CLM_SUBROGATION_SETTLE,
		EXCHANGE_RATE,
		CONVERT_AMOUNT,
		EXTEND_INFO,BANK_DETAIL,COMPENSATE_NO,SERIAL_NO,ORGANIZE_CODE,CLIENT_RELATION,BANK_DETAIL_CODE,
		AGENCY_TYPE,CUSTOMER_NO,COMPANY_CARD_TYPE,OPEN_ID,PAY_TYPE,FEE_TYPE,
		COINSURANCE_MARK,
		ACCEPT_INSURANCE_FLAG,
		COINSURANCE_COMPANY_CODE,
		COINSURANCE_COMPANY_NAME,
		COINSURANCE_RATIO,
		IS_FULL_PAY,
		COINSURANCE_ACTUAL_AMOUNT,
		FINANCE_PAYMENT_AMOUNT,
		PAYMENT_VOUCHER_URL)
		VALUES
		<foreach collection="paymentItemList" item="item" index="index" separator="," >
			(#{item.createdBy,jdbcType=VARCHAR},
			now(),
			#{item.updatedBy,jdbcType=VARCHAR},
			now(),
			#{item.idClmPaymentItem,jdbcType=VARCHAR},
			#{item.policyNo,jdbcType=VARCHAR},
			#{item.reportNo,jdbcType=VARCHAR},
			#{item.caseNo,jdbcType=VARCHAR},
			#{item.idClmBatch,jdbcType=VARCHAR},
			#{item.caseTimes,jdbcType=NUMERIC},
			#{item.claimType,jdbcType=VARCHAR},
			#{item.subTimes,jdbcType=NUMERIC},
			#{item.paymentType,jdbcType=VARCHAR},
			#{item.idClmPaymentInfo,jdbcType=VARCHAR},
			#{item.collectPaySign,jdbcType=VARCHAR},
			#{item.paymentAmount,jdbcType=DECIMAL},
			#{item.paymentCurrencyCode,jdbcType=VARCHAR},
			#{item.clientName,jdbcType=VARCHAR},
			#{item.clientCertificateType,jdbcType=VARCHAR},
			#{item.clientCertificateNo,jdbcType=VARCHAR},
			#{item.clientBankCode,jdbcType=VARCHAR},
			#{item.clientBankName,jdbcType=VARCHAR},
			#{item.clientBankAccount,jdbcType=VARCHAR},
			#{item.clientMobile,jdbcType=VARCHAR},
			#{item.clientType,jdbcType=VARCHAR},
			#{item.provinceName,jdbcType=VARCHAR},
			#{item.cityName,jdbcType=VARCHAR},
			#{item.regionCode,jdbcType=VARCHAR},
			#{item.collectPayApproach,jdbcType=VARCHAR},
			#{item.bankAccountAttribute,jdbcType=VARCHAR},
			#{item.mergeSign,jdbcType=VARCHAR},
			#{item.paymentItemStatus,jdbcType=VARCHAR},
			#{item.effectiveDate,jdbcType=TIMESTAMP},
			#{item.invalidateDate,jdbcType=TIMESTAMP},
			#{item.remark,jdbcType=VARCHAR},
			#{item.isCoinsure,jdbcType=VARCHAR},
			#{item.migrateFrom,jdbcType=VARCHAR},
			#{item.collectPayNo,jdbcType=VARCHAR},
			#{item.hisIdClmPaymentNotice,jdbcType=VARCHAR},
			#{item.idClmSubrogationSettle,jdbcType=VARCHAR},
			#{item.exchangeRate,jdbcType=VARCHAR},
			#{item.convertAmount,jdbcType=VARCHAR},
			#{item.extendInfo,jdbcType=VARCHAR},#{item.bankDetail,jdbcType=VARCHAR},#{item.compensateNo,jdbcType=VARCHAR},
			#{item.serialNo,jdbcType=NUMERIC},#{item.organizeCode,jdbcType=VARCHAR},
			#{item.clientRelation,jdbcType=NUMERIC},
			#{item.bankDetailCode,jdbcType=NUMERIC},
			#{item.agencyType,jdbcType=VARCHAR},
			#{item.customerNo,jdbcType=VARCHAR},
			#{item.companyCardType,jdbcType=VARCHAR},
			#{item.openId,jdbcType=VARCHAR},
			#{item.payType,jdbcType=VARCHAR},
			#{item.feeType,jdbcType=VARCHAR},
			#{item.coinsuranceMark,jdbcType=VARCHAR},
			#{item.acceptInsuranceFlag,jdbcType=VARCHAR},
			#{item.coinsuranceCompanyCode,jdbcType=VARCHAR},
			#{item.coinsuranceCompanyName,jdbcType=VARCHAR},
			#{item.coinsuranceRatio,jdbcType=DECIMAL},
			#{item.isFullPay,jdbcType=VARCHAR},
			#{item.coinsuranceActualAmount,jdbcType=DECIMAL},
			#{item.financePaymentAmount,jdbcType=DECIMAL},
			#{item.paymentVoucherUrl,jdbcType=VARCHAR})
		</foreach>
	</insert>

	<update id="updatePaymentItemStatus" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		update CLM_PAYMENT_ITEM
		set UPDATED_DATE = now(),
		UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
		PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND PAYMENT_ITEM_STATUS != '90'
		<if test="caseNo != null">
			AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
		</if>
		<if test="claimType != null">
			AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		</if>
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		<if test="subTimes != null">
			AND SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
		</if>
	</update>

	<select id="getPrePaymentItem" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		SELECT
		<include refid="Base_Column_List"/>
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS != '90'
		<if test="reportNo != null">
			AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="caseTimes != null">
			AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		</if>
		<if test="clientName != null">
			AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
		</if>
		<if test=" serialNo != null ">
			AND SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
		</if>
		<if test="compensateNo != null">
			AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentInfo != null">
			AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentItem != null">
			AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
		</if>
		<if test="caseNo != null">
			AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null">
			AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="claimType != null">
			AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		</if>
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="subTimes != null">
			AND SUB_TIMES = #{subTimes,jdbcType=VARCHAR}
		</if>
		order by POLICY_NO

	</select>

	<select id="getCompensateNoByPayment" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultType="java.lang.String">
		SELECT
			COMPENSATE_NO
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS not in  ('90','20')
		  AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		  AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		  AND PAYMENT_TYPE in ('13','1J')
		LIMIT 1
	</select>

	<select id="getSumAdvancePay" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultType="java.math.BigDecimal">
		SELECT IFNULL( sum( PAYMENT_AMOUNT) , 0)
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS not in  ('90','20')
		  AND REPORT_NO=#{reportNo}
		  AND CASE_TIMES=#{caseTimes}
		  AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		  AND PAYMENT_TYPE in('C13','C1J','P13','P1J')
	</select>

	<select id="getSumPrePay" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultType="java.math.BigDecimal">
		SELECT IFNULL( sum( PAYMENT_AMOUNT) , 0)
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS = '10'
		  AND REPORT_NO=#{reportNo}
		  AND CASE_TIMES=#{caseTimes}
		  AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		  AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
		  AND PAYMENT_TYPE not in('C13','C1J','P13','P1J')
	</select>

	<select id="getSumBeforePrePay" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultType="java.math.BigDecimal">
		SELECT IFNULL( sum( PAYMENT_AMOUNT) , 0)
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS = '80'
		  AND REPORT_NO=#{reportNo}
		  AND CASE_TIMES=#{caseTimes}
		  AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		  AND PAYMENT_TYPE not in('C13','C1J','P13','P1J')
	</select>

	<select id="getPaymentItemCount" resultType="java.lang.Integer">
		select count(1) from CLM_PAYMENT_ITEM
		where ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		and PAYMENT_ITEM_STATUS != '90'
		and CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		limit 1
	</select>
	<select id="getNoPayItemByCompensateNo" resultType="java.lang.Integer">
		select count(1) from CLM_PAYMENT_ITEM
		where COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
		and PAYMENT_ITEM_STATUS not in  ('90','20','80')
		AND PAYMENT_TYPE not in('C13','C1J','P13','P1J')
	</select>


	<select id="getNoPayItemByReportNo" resultType="java.lang.Integer">
		select count(1) from CLM_PAYMENT_ITEM
		where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  AND CASE_TIMES=#{caseTimes}
		  and PAYMENT_ITEM_STATUS not in  ('90','20','80')
		  AND PAYMENT_TYPE in('13','11','C13','P13')
	</select>

	<insert id="insertJobInfo" parameterType="com.paic.ncbs.claim.model.dto.other.AsynchronousCompensationJobExtDTO">
		insert into job_asynchronous_compensation(
		ID,
		JOB_TYPE,
		RETRY_TIMES,
		REQUEST_PARAM,
		STATUS,
		URL,
		SIGNATURE,
		BUSINESS_TYPE,
		RESPONSE_PARAM,
		BUSINESS_NO,
		BUSINESS_NO_SUB,
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE
		)
		values (
		#{id,jdbcType=VARCHAR},
		#{jobType,jdbcType=VARCHAR},
		#{retryTimes},
		#{requestParam,jdbcType=VARCHAR},
		#{status,jdbcType=VARCHAR},
		#{url,jdbcType=VARCHAR},
		#{signature,jdbcType=VARCHAR},
		#{businessType,jdbcType=VARCHAR},
		#{responseParam,jdbcType=VARCHAR},
		#{businessNo,jdbcType=VARCHAR},
		#{businessNoSub,jdbcType=VARCHAR},
		#{createdBy,jdbcType=VARCHAR},
		#{createdDate},
		#{updatedBy,jdbcType=VARCHAR},
		#{updatedDate}
		)
	</insert>


	<select id="getLastPaymentAmount" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultType="java.math.BigDecimal">
		SELECT IFNULL(sum(PAYMENT_AMOUNT), 0)
		FROM CLM_PAYMENT_ITEM
		WHERE BANK_ACCOUNT_ATTRIBUTE = 1
		  AND PAYMENT_TYPE = '13'
		  AND REPORT_NO = #{reportNo}
		  AND CASE_TIMES = #{caseTimes}
		  AND CLIENT_NAME = #{clientName}
		  AND CLIENT_BANK_ACCOUNT = #{clientBankAccount}
	</select>


	<select id="getCompensationIntermediateData" resultType="com.paic.ncbs.claim.model.dto.pay.CompensationIntermediateData">
		select
		    item.policy_no policyNo,
			item.report_no reportNo,
			item.case_no caseNo,
			item.case_times caseTimes,
			(CASE
				WHEN item.payment_type='1J' THEN 'P1'
				WHEN item.payment_type='13' THEN 'P2'
				ELSE NULL
				END ) AS payFlag,
			item.client_name clientName,
			item.client_certificate_no clientCertificateNo,
		    item.created_date  payDate,
			plan.plan_code planCode,
			duty.duty_code dutyCode,
			duty.duty_pay_amount payAmount
		from
			clm_payment_item item,
			clms_payment_plan plan,
			clms_payment_duty duty
		where
		item.report_no = #{reportNo}
		and item.case_times = #{caseTimes}
		and item.id_clm_payment_item = plan.id_clm_payment_item
		and plan.id_clms_payment_plan = duty.id_clms_payment_plan
	</select>

	<select id="queryCustomerNoToSupplementedFromItem" resultMap="resultDTO">
		select
		id_clm_payment_item,
		report_no,
		case_times,
		client_certificate_no,
		client_certificate_type,
		bank_account_attribute,
		client_mobile,
		client_name,
		customer_no
		from clm_payment_item
		where customer_no is null
	</select>

	<update id="batchUpdate" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		<foreach collection="paymentItemList" item="item" separator=";">
			update clm_payment_item
			set UPDATED_DATE = now(),
			UPDATED_BY = #{item.updatedBy}
			<if test="item.customerNo != null and item.customerNo!=''">
				, customer_no = #{item.customerNo}
			</if>
			WHERE REPORT_NO = #{item.reportNo}
			AND CASE_TIMES = #{item.caseTimes}
			<if test="item.idClmPaymentItem != null">
				AND ID_CLM_PAYMENT_ITEM = #{item.idClmPaymentItem}
			</if>
		</foreach>
	</update>
	<select id="getPayDateByReportNo" parameterType="java.lang.String" resultType="java.lang.String">
		select a.pay_date from clm_payment_item a ,clm_whole_case_base b
		where a.REPORT_NO=b.REPORT_NO and b.WHOLE_CASE_STATUS='0'
		and b.REPORT_NO=#{reportNo} limit 1
	</select>

	<select id="getOrderPayMentInfo"  resultType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" >
		select ID_CLM_PAYMENT_ITEM idClmPaymentItem,
		CASE_TIMES caseTimes,
		CASE_NO caseNo,
		REPORT_NO reportNo from clm_payment_item
		where REPORT_NO=#{reportNo}
		and CASE_TIMES='1'
		and COLLECT_PAY_APPROACH='217'
		and PAYMENT_ITEM_STATUS ='11'
	</select>

	<select id="getPrePayCount" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM clm_payment_item
		WHERE report_no = #{reportNo}
		AND case_times = #{caseTimes}
		AND payment_item_status != '90'
		AND payment_type = '11'
	</select>
	<select id="getBatchOrderPayDataInfo" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		select ID_CLM_PAYMENT_ITEM idClmPaymentItem,
		CASE_TIMES caseTimes,
		CASE_NO caseNo,
		REPORT_NO reportNo from clm_payment_item
		where  COLLECT_PAY_APPROACH='217'
		and PAYMENT_ITEM_STATUS ='11'
		and updated_date <![CDATA[<]]>#{updatedDate}
	</select>

	<select id="getSettleAmount" resultType="java.math.BigDecimal">
		select PAYMENT_AMOUNT from clm_payment_item where
		report_no=#{reportNo}
		and case_times=#{caseTimes}
		and PAYMENT_TYPE='13'
		and PAYMENT_ITEM_STATUS ='10'
	</select>
	<select id="getTPAPaymentItemDTO" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentItemComData">
		SELECT
		<include refid="Base_Column_List"/>
		FROM CLM_PAYMENT_ITEM
		WHERE  REPORT_NO = #{reportNo}
		AND CASE_TIMES = #{caseTimes}
	</select>
	<update id="updatePaymentItemVoucherUrl" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
		update CLM_PAYMENT_ITEM
		set UPDATED_DATE = now(),
		UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
			payment_voucher_url = #{paymentVoucherUrl,jdbcType=VARCHAR}
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
	</update>
	<select id="getPaymentItemList" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		<choose>
			<when test="reportNo != null or paymentItemStatusList != null or paymentItemStatus != null or caseTimes != null or clientName != null or serialNo != null or compensateNo != null or idClmPaymentInfo != null or idClmPaymentItem != null or caseNo != null or policyNo != null or claimType != null or paymentType != null or subTimes != null">
				SELECT
				<include refid="Base_Column_List"/>
				FROM CLM_PAYMENT_ITEM
				WHERE 1 = 1
				<if test="reportNo != null">
					AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
				</if>
				<if test="paymentItemStatusList != null and paymentItemStatusList.size() > 0">
					AND PAYMENT_ITEM_STATUS in
					<foreach collection="paymentItemStatusList" item="item" open="(" close=")" separator=",">
						#{item,jdbcType=VARCHAR}
					</foreach>
				</if>
				<if test="paymentItemStatus != null">
					AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
				</if>
				<if test="caseTimes != null">
					AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
				</if>
				<if test="clientName != null">
					AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
				</if>
				<if test="serialNo != null">
					AND SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
				</if>
				<if test="compensateNo != null">
					AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
				</if>
				<if test="idClmPaymentInfo != null">
					AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
				</if>
				<if test="idClmPaymentItem != null">
					AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
				</if>
				<if test="caseNo != null">
					AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
				</if>
				<if test="policyNo != null">
					AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
				</if>
				<if test="claimType != null">
					AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
				</if>
				<if test="paymentType != null">
					AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
				</if>
				<if test="subTimes != null">
					AND SUB_TIMES = #{subTimes,jdbcType=VARCHAR}
				</if>
				ORDER BY POLICY_NO
			</when>
			<otherwise>
				<!-- 如果没有条件，可以选择返回空结果集或抛出异常 -->
				SELECT * FROM CLM_PAYMENT_ITEM WHERE 1 = 0
			</otherwise>
		</choose>
	</select>
	<select id="getAllFeeByPolicyNo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		select
			'0' as whole_case_status,
			sum(cpi.payment_amount) payment_amount
		from clm_whole_case_base cwcb
		inner join clm_payment_item cpi on cwcb.report_no = cpi.report_no
		and cwcb.case_times = cpi.case_times and cpi.payment_type in ('1J','11J')
		where cpi.policy_no = #{policyNo,jdbcType=VARCHAR}
		and cwcb.whole_case_status = '0'
		having payment_amount is not null
	</select>
	<select id="getNewEstimateFeeByPolicyNo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		select '1' as whole_case_status,
				(ifnull(sum( edr.ARBITRAGE_FEE ), 0 ) +
				ifnull( sum( edr.LAWSUIT_FEE ), 0 ) +
				ifnull( sum( edr.COMMON_ESTIMATE_FEE ), 0 ) +
				ifnull( sum( edr.LAWYER_FEE ), 0 ) +
				ifnull( sum( edr.EXECUTE_FEE ), 0 ) +
				ifnull( sum( edr.VERIFY_APPRAISE_FEE ), 0 ) +
				ifnull( sum( edr.INQUIRE_FEE ), 0 ) +
				ifnull( sum( edr.OTHER_FEE ), 0 ) +
				ifnull( sum( edr.SPECIAL_SURVEY_FEE ), 0 )) payment_amount
		from clm_case_base ccb
		inner join CLMS_ESTIMATE_DUTY_RECORD edr on ccb.case_no = edr.case_no
		and ccb.case_times = edr.case_times
		where ccb.CASE_STATUS <![CDATA[ <> ]]> '0'
		and edr.estimate_type = '02'
		and edr.is_effective = 'Y'
		and ccb.policy_no = #{policyNo,jdbcType=VARCHAR}
	</select>
	<select id="getNewPaymentByPolicyNo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		select '0' as whole_case_status,
				sum(cpi.payment_amount) payment_amount
		from clm_payment_item cpi
		inner join (select cwcb.report_no,
						cwcb.case_times,
						cwcb.whole_case_status,
						ROW_NUMBER() OVER (
						PARTITION BY cwcb.report_no
						ORDER BY cwcb.case_times DESC
						) AS rn
					from clm_whole_case_base cwcb
					inner join clms_policy_info cpinfo on  cwcb.report_no = cpinfo.report_no
					where cpinfo.policy_no = #{policyNo,jdbcType=VARCHAR}
					order by cwcb.report_no desc) tt on cpi.report_no = tt.report_no
		and cpi.case_times = tt.case_times
		where cpi.payment_type = '13'
		and cpi.policy_no = #{policyNo,jdbcType=VARCHAR}
		and tt.rn = 1
		and tt.whole_case_status = '0'
		and cpi.payment_amount is not null
		order by tt.report_no desc
	</select>
	<select id="getNewEstimatePaymentByPolicyNo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		select '1' as whole_case_status,
		ifnull(sum( edr.ESTIMATE_AMOUNT ), 0 ) as payment_amount
		from clm_case_base ccb
		inner join CLMS_ESTIMATE_DUTY_RECORD edr on ccb.case_no = edr.case_no
		and ccb.case_times = edr.case_times
		where ccb.CASE_STATUS <![CDATA[ <> ]]> '0'
		and edr.estimate_type = '02'
		and edr.is_effective = 'Y'
		and ccb.policy_no = #{policyNo,jdbcType=VARCHAR}
	</select>

	<update id="updatePaymentItemByInfo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		UPDATE CLM_PAYMENT_ITEM
		SET UPDATED_DATE = now(),
		UPDATED_BY = #{updatedBy,jdbcType=VARCHAR}
		<if test="collectPayApproach != null">
			, COLLECT_PAY_APPROACH = #{collectPayApproach,jdbcType=VARCHAR}
		</if>
		<if test="clientBankAccount != null">
			, CLIENT_BANK_ACCOUNT = #{clientBankAccount,jdbcType=VARCHAR}
		</if>
		<if test="clientBankCode != null">
			, CLIENT_BANK_CODE = #{clientBankCode,jdbcType=VARCHAR}
		</if>
		<if test="clientBankName != null">
			, CLIENT_BANK_NAME = #{clientBankName,jdbcType=VARCHAR}
		</if>
		<if test="clientCertificateNo != null">
			, CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR}
		</if>
		<if test="clientCertificateType != null">
			, CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR}
		</if>
		<if test="clientMobile != null">
			, CLIENT_MOBILE = #{clientMobile,jdbcType=VARCHAR}
		</if>
		<if test="clientName != null">
			, CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
		</if>
		<if test="clientType != null">
			, CLIENT_TYPE = #{clientType,jdbcType=VARCHAR}
		</if>
		<if test="provinceName != null">
			, PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR}
		</if>
		<if test="cityName != null">
			, CITY_NAME = #{cityCode,jdbcType=VARCHAR}
		</if>
		<if test="organizeCode != null">
			, ORGANIZE_CODE = #{organizeCode,jdbcType=VARCHAR}
		</if>
		<if test="bankDetail != null">
			, BANK_DETAIL = #{bankDetail,jdbcType=VARCHAR}
		</if>
		<if test="regionCode != null">
			, REGION_CODE = #{regionCode,jdbcType=VARCHAR}
		</if>
		<if test="bankAccountAttribute != null">
			, BANK_ACCOUNT_ATTRIBUTE = #{bankAccountAttribute,jdbcType=VARCHAR}
		</if>
		<if test="remark != null">
			, remark = #{remark,jdbcType=VARCHAR}
		</if>
		<if test="bankDetailCode != null">
			, BANK_DETAIL_CODE = #{bankDetailCode,jdbcType=VARCHAR}
		</if>
		<if test="clientRelation != null">
			, CLIENT_RELATION = #{clientRelation,jdbcType=VARCHAR}
		</if>
		<if test="agencyType != null and agencyType!=''">
			, AGENCY_TYPE = #{agencyType,jdbcType=VARCHAR}
		</if>
		<if test="customerNo != null and customerNo!=''">
			, CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
		</if>
		<if test="companyCardType != null and companyCardType!=''">
			, COMPANY_CARD_TYPE = #{companyCardType,jdbcType=VARCHAR}
		</if>
		<if test="payType != null and payType!=''">
			, PAY_TYPE = #{payType,jdbcType=VARCHAR}
		</if>
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
	</update>

	<delete id="delPaymentItemByInfo"  parameterType="java.lang.String">
		delete from CLM_PAYMENT_ITEM
		WHERE ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		AND PAYMENT_ITEM_STATUS = '10'
	</delete>
	<select id="isPay" resultType="java.lang.Integer">
		select count(1)
		from clm_payment_item cpi
		where cpi.PAYMENT_ITEM_STATUS not in  ('90','20')
		AND cpi.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND cpi.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND cpi.PAYMENT_TYPE in ('13','C13','11','P13')
		AND cpi.finance_payment_amount > 0
	</select>

	<select id="getMergePayAccountList" resultMap="resultDTO">
		select
			distinct
			cpp.product_code,
			cpi.client_bank_account
		from clm_case_base ccb
		left join clm_payment_item cpi on ccb.report_no = cpi.report_no
		and ccb.case_times = cpi.case_times
		left join clms_payment_plan cpp on cpi.id_clm_payment_item = cpp.id_clm_payment_item
		where 1=1
		and DATE(ccb.end_case_date) = date(#{yesterday,jdbcType=DATE})
		and cpp.product_code in('01P00001','01P00004')
		and cpi.collect_pay_approach = '215'
		and cpi.id is null
	</select>

	<select id="getPaymentMainInfo" resultType="com.paic.ncbs.claim.model.dto.pay.BatchPaymentMainInfo">
		select
			cip.client_no as parterCode,
			cpi.client_name as parterName,
			cpi.client_name as payeeName,
			sum(cpi.payment_amount) as sumAmount
		from clm_case_base ccb
		left join clm_payment_item cpi on ccb.report_no = cpi.report_no
		and ccb.case_times = cpi.case_times
		left join clms_payment_plan cpp on cpi.id_clm_payment_item = cpp.id_clm_payment_item
		left join clms_policy_info pi on cpi.report_no = pi.report_no
		left join clms_insured_person cip on pi.id_ahcs_policy_info = cip.id_ahcs_policy_info
		where 1=1
		and DATE(ccb.end_case_date) = date(#{yesterday,jdbcType=DATE})
		and cpp.product_code = #{productCode,jdbcType=VARCHAR}
		and cpi.client_bank_account = #{clientBankAccount,jdbcType=VARCHAR}
		and cpi.collect_pay_approach = '215'
		and cpi.id is null
	</select>


	<select id="getMergePaymentList" resultMap="mergePaymentResultMap">
		select
			cpi.id_clm_payment_item,
			cpi.report_no,
			cpi.case_times,
			cpi.payment_amount,
			cpp.plan_code,
			cpp.kind_code,
			cpp.product_code,
			cpp.product_line_code
		from clm_case_base ccb
		left join clm_payment_item cpi on ccb.report_no = cpi.report_no
		and ccb.case_times = cpi.case_times
		left join clms_payment_plan cpp on cpi.id_clm_payment_item = cpp.id_clm_payment_item
		where 1=1
		and DATE(ccb.end_case_date) = date(#{yesterday,jdbcType=DATE})
		and cpp.product_code = #{productCode,jdbcType=VARCHAR}
		and cpi.client_bank_account = #{clientBankAccount,jdbcType=VARCHAR}
		and cpi.collect_pay_approach = '215'
		and cpi.id is null
	</select>

	<select id="getMergePayCompensateId" resultType="String">
		select
			distinct
			cpi.ID_CLM_PAYMENT_ITEM
		from clm_case_base ccb
		left join clm_payment_item cpi on ccb.report_no = cpi.report_no
		and ccb.case_times = cpi.case_times
		left join clms_payment_plan cpp on cpi.id_clm_payment_item = cpp.id_clm_payment_item
		left join clms_merge_payment cmp on cpi.id = cmp.id
		left join clms_send_merge_payment_record csmpr on cmp.batch_no = csmpr.batch_no
		where 1=1
		and DATE(ccb.end_case_date) = date(#{failDate,jdbcType=DATE})
		and csmpr.is_success = 'N'
		and cpp.product_code in('01P00001','01P00004')
		and cpi.collect_pay_approach = '215'
	</select>
	<update id="updateMergePaymentId">
		update clm_payment_item cpi
		set cpi.id = null
		where cpi.ID_CLM_PAYMENT_ITEM in
		<foreach collection="list" index="index" item="item" open="("
				 separator="," close=")">
			#{item}
		</foreach>
	</update>

	<insert id="addClmsMergePayment"
			parameterType="com.paic.ncbs.claim.model.dto.pay.MergePaymentDTO"
			useGeneratedKeys="true" keyProperty="id">
		INSERT INTO clms_merge_payment(
			batch_no,
			parter_code,
			parter_name,
			payee_name,
			sum_amount,
			sum_count,
			merge_payment_status,
			settlement_status,
			created_by,
			updated_by,
			sys_ctime,
			sys_utime)
		VALUES(
			#{batchNo,jdbcType=VARCHAR},
			#{parterCode,jdbcType=VARCHAR},
			#{parterName,jdbcType=VARCHAR},
			#{payeeName,jdbcType=VARCHAR},
			#{sumAmount},
			#{sumCount,jdbcType=INTEGER},
			#{mergePaymentStatus,jdbcType=VARCHAR},
			#{settlementStatus,jdbcType=VARCHAR},
			#{createdBy,jdbcType=VARCHAR},
			#{updatedBy,jdbcType=VARCHAR},
			now(),
			now())
	</insert>

	<update id="updatePaymentItemId">
		update CLM_PAYMENT_ITEM
		set UPDATED_DATE = now(),
		ID = #{id,jdbcType=INTEGER}
		WHERE ID_CLM_PAYMENT_ITEM in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item.businessNo,jdbcType=VARCHAR}
		</foreach>
	</update>

	<update id="updateMergePaymentItemStatus">
		update CLM_PAYMENT_ITEM cpi
		set cpi.UPDATED_DATE = now(),
		cpi.PAYMENT_ITEM_STATUS = #{mergePaymentStatus,jdbcType=VARCHAR}
		where PAYMENT_ITEM_STATUS != '90'
		and cpi.id = (select cmp.id from clms_merge_payment cmp
						where cpi.id = cmp.id
						and cmp.batch_no = #{batchNo,jdbcType=VARCHAR})
	</update>

	<update id="updateMergePaymentStatus">
		update clms_merge_payment cmp
		set cmp.sys_utime = now(),
		cmp.merge_payment_status = #{mergePaymentStatus,jdbcType=VARCHAR}
		where cmp.batch_no = #{batchNo,jdbcType=VARCHAR}
	</update>
	<update id="updateMergeSettlementStatus">
		update clms_merge_payment cmp
		set cmp.sys_utime = now(),
		cmp.settlement_status = #{settlementStatus,jdbcType=VARCHAR},
		cmp.error_msg = #{errorMsg,jdbcType=VARCHAR}
		where cmp.batch_no = #{batchNo,jdbcType=VARCHAR}
	</update>

	<select id="getSumPreFeeByReportNo"
			parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO"
			resultType="java.math.BigDecimal">
		select sum(ifnull(cpi.payment_amount,0))
		from CLM_PAYMENT_ITEM cpi
		where cpi.payment_type = '11J'
		and cpi.report_no = #{reportNo,jdbcType=VARCHAR}
		and cpi.case_times = #{caseTimes,jdbcType=NUMERIC}
		and cpi.sub_times = (select max(sub_times)
							from CLM_PAYMENT_ITEM
							where report_no = #{reportNo,jdbcType=VARCHAR}
							and case_times = #{caseTimes,jdbcType=NUMERIC})
	</select>

	<select id="getPaymentItemNoDraft" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
		SELECT
		<include refid="Base_Column_List"/>
		FROM CLM_PAYMENT_ITEM
		WHERE PAYMENT_ITEM_STATUS not in  ('90','20','10')
		<if test="reportNo != null">
			AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="caseTimes != null">
			AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		</if>
		<if test="clientName != null">
			AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
		</if>
		<if test="clientBankAccount != null">
			AND CLIENT_BANK_ACCOUNT = #{clientBankAccount}
		</if>
		<if test=" serialNo != null ">
			AND SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
		</if>
		<if test="compensateNo != null">
			AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentInfo != null">
			AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		</if>
		<if test="idClmPaymentItem != null">
			AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
		</if>
		<if test="caseNo != null">
			AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null">
			AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="claimType != null">
			AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		</if>
		<if test="paymentType != null">
			AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
		</if>
		<if test="paymentItemStatus != null">
			AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
		</if>
		<if test="subTimes != null">
			AND SUB_TIMES = #{subTimes,jdbcType=VARCHAR}
		</if>
		<if test="coinsuranceCompanyCode != null">
			AND COINSURANCE_COMPANY_CODE = #{coinsuranceCompanyCode,jdbcType=VARCHAR}
		</if>
		order by POLICY_NO,REPORT_NO,CASE_TIMES

	</select>
</mapper>