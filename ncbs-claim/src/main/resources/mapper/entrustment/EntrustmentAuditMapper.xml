<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO" id="result">
        <id property="idEntrustmentAudit" column="ID_ENTRUSTMENT_AUDIT" />
        <result property="idEntrustment" column="ID_ENTRUSTMENT" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="policyNo" column="POLICY_NO" />
        <result property="insuredName" column="INSURED_NAME" />
        <result property="thirdPartyType" column="THIRD_PARTY_TYPE" />
        <result property="entrustmentDpmCode" column="ENTRUSTMENT_DPM_CODE" />
        <result property="entrustmentDpmName" column="ENTRUSTMENT_DPM_NAME" />
        <result property="initiatorUm" column="INITIATOR_UM" />
        <result property="auditorUm" column="AUDITOR_UM" />
        <result property="initiatorUmName" column="INITIATOR_UM_NAME" />
        <result property="auditorUmName" column="AUDITOR_UM_NAME" />
        <result property="auditOpinion" column="AUDIT_OPINION" />
        <result property="examineTime" column="EXAMINE_TIME" />
        <result property="remark" column="REMARK" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="sysCtime" column="SYS_CTIME" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="sysUtime" column="SYS_UTIME" />
        <result property="validFlag" column="VALID_FLAG" />
    </resultMap>

    <!-- 插入委托审批记录 -->
    <insert id="insertEntrustmentAudit" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO">
        INSERT INTO CLMS_ENTRUSTMENT_AUDIT (
            ID_ENTRUSTMENT_AUDIT, ID_ENTRUSTMENT, REPORT_NO, CASE_TIMES,
            POLICY_NO, INSURED_NAME, THIRD_PARTY_TYPE, ENTRUSTMENT_DPM_CODE,
            ENTRUSTMENT_DPM_NAME, INITIATOR_UM, AUDITOR_UM, INITIATOR_UM_NAME,
            AUDITOR_UM_NAME, AUDIT_OPINION, EXAMINE_TIME, REMARK, VALID_FLAG,
            CREATED_BY, SYS_CTIME, UPDATED_BY, SYS_UTIME
        ) VALUES (
            #{idEntrustmentAudit}, #{idEntrustment}, #{reportNo}, #{caseTimes},
            #{policyNo}, #{insuredName}, #{thirdPartyType}, #{entrustmentDpmCode},
            #{entrustmentDpmName}, #{initiatorUm}, #{auditorUm}, #{initiatorUmName},
            #{auditorUmName}, #{auditOpinion}, #{examineTime}, #{remark}, #{validFlag},
            #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        )
    </insert>

    <!-- 更新委托审批记录 -->
    <update id="updateEntrustmentAudit" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO">
        UPDATE CLMS_ENTRUSTMENT_AUDIT
        <set>
            <if test="idEntrustment != null">ID_ENTRUSTMENT = #{idEntrustment},</if>
            <if test="reportNo != null">REPORT_NO = #{reportNo},</if>
            <if test="caseTimes != null">CASE_TIMES = #{caseTimes},</if>
            <if test="policyNo != null">POLICY_NO = #{policyNo},</if>
            <if test="insuredName != null">INSURED_NAME = #{insuredName},</if>
            <if test="thirdPartyType != null">THIRD_PARTY_TYPE = #{thirdPartyType},</if>
            <if test="entrustmentDpmCode != null">ENTRUSTMENT_DPM_CODE = #{entrustmentDpmCode},</if>
            <if test="entrustmentDpmName != null">ENTRUSTMENT_DPM_NAME = #{entrustmentDpmName},</if>
            <if test="initiatorUm != null">INITIATOR_UM = #{initiatorUm},</if>
            <if test="auditorUm != null">AUDITOR_UM = #{auditorUm},</if>
            <if test="initiatorUmName != null">INITIATOR_UM_NAME = #{initiatorUmName},</if>
            <if test="auditorUmName != null">AUDITOR_UM_NAME = #{auditorUmName},</if>
            <if test="auditOpinion != null">AUDIT_OPINION = #{auditOpinion},</if>
            <if test="examineTime != null">EXAMINE_TIME = #{examineTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            <if test="sysUtime != null">SYS_UTIME = #{sysUtime},</if>
        </set>
        WHERE ID_ENTRUSTMENT_AUDIT = #{idEntrustmentAudit}
    </update>

    <!-- 根据主键查询委托审批记录 -->
    <select id="selectById" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT_AUDIT
        WHERE ID_ENTRUSTMENT_AUDIT = #{idEntrustmentAudit}
    </select>

    <!-- 根据委托主表ID查询审批记录 -->
    <select id="selectByEntrustmentId" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT_AUDIT
        WHERE ID_ENTRUSTMENT = #{idEntrustment}
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据报案号查询审批记录 -->
    <select id="selectByReportNo" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT_AUDIT
        WHERE REPORT_NO = #{reportNo}
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据审批人查询待审批列表 -->
    <select id="selectPendingAuditList" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT_AUDIT
        WHERE AUDITOR_UM = #{auditorUm}
        AND AUDIT_OPINION IS NULL
        AND VALID_FLAG = 'Y'
        ORDER BY SYS_CTIME DESC
    </select>

</mapper>