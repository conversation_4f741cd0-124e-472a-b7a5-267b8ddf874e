<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper">
    
    <resultMap type="com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO" id="result">
        <id property="idEntrustment" column="ID_ENTRUSTMENT" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="thirdPartyType" column="THIRD_PARTY_TYPE" />
        <result property="insuredApplyStatus" column="INSURED_APPLY_STATUS" />
        <result property="accidentScene" column="ACCIDENT_SCENE" />
        <result property="accidentSceneName" column="ACCIDENT_SCENE_NAME" />
        <result property="other" column="OTHER" />
        <result property="entrustmentDpmCode" column="ENTRUSTMENT_DPM_CODE" />
        <result property="entrustmentDpmName" column="ENTRUSTMENT_DPM_NAME" />
        <result property="contactName" column="CONTACT_NAME" />
        <result property="contactPhone" column="CONTACT_PHONE" />
        <result property="entrustmentDescription" column="ENTRUSTMENT_DESCRIPTION" />
        <result property="entrustmentObject" column="ENTRUSTMENT_OBJECT" />
        <result property="litigationStrategy" column="LITIGATION_STRATEGY" />
        <result property="feeStandard" column="FEE_STANDARD" />
        <result property="approverUmCode" column="APPROVER_UM_CODE" />
        <result property="approverUmName" column="APPROVER_UM_NAME" />
        <result property="approverDpmCode" column="APPROVER_DPM_CODE" />
        <result property="approverDpmName" column="APPROVER_DPM_NAME" />
        <result property="entrustmentStatus" column="ENTRUSTMENT_STATUS" />
        <result property="processStatus" column="PROCESS_STATUS" />
        <result property="processFile" column="PROCESS_FILE" />
        <result property="operate" column="OPERATE" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="sysCtime" column="SYS_CTIME" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="sysUtime" column="SYS_UTIME" />
        <result property="validFlag" column="VALID_FLAG" />
    </resultMap>

    <!-- 插入委托信息 -->
    <insert id="insertEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO">
        INSERT INTO CLMS_ENTRUSTMENT (
            ID_ENTRUSTMENT, REPORT_NO, CASE_TIMES, THIRD_PARTY_TYPE,
            INSURED_APPLY_STATUS, ACCIDENT_SCENE, ACCIDENT_SCENE_NAME, OTHER,
            ENTRUSTMENT_DPM_CODE, ENTRUSTMENT_DPM_NAME, CONTACT_NAME, CONTACT_PHONE,
            ENTRUSTMENT_DESCRIPTION, ENTRUSTMENT_OBJECT, LITIGATION_STRATEGY, FEE_STANDARD,
            APPROVER_UM_CODE, APPROVER_UM_NAME, APPROVER_DPM_CODE, APPROVER_DPM_NAME,
            ENTRUSTMENT_STATUS, PROCESS_STATUS, PROCESS_FILE, OPERATE, VALID_FLAG,
            CREATED_BY, SYS_CTIME, UPDATED_BY, SYS_UTIME
        ) VALUES (
            #{idEntrustment}, #{reportNo}, #{caseTimes}, #{thirdPartyType},
            #{insuredApplyStatus}, #{accidentScene}, #{accidentSceneName}, #{other},
            #{entrustmentDpmCode}, #{entrustmentDpmName}, #{contactName}, #{contactPhone},
            #{entrustmentDescription}, #{entrustmentObject}, #{litigationStrategy}, #{feeStandard},
            #{approverUmCode}, #{approverUmName}, #{approverDpmCode}, #{approverDpmName},
            #{entrustmentStatus}, #{processStatus}, #{processFile}, #{operate}, #{validFlag},
            #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        )
    </insert>

    <!-- 更新委托信息 -->
    <update id="updateEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO">
        UPDATE CLMS_ENTRUSTMENT
        <set>
            <if test="reportNo != null">REPORT_NO = #{reportNo},</if>
            <if test="caseTimes != null">CASE_TIMES = #{caseTimes},</if>
            <if test="thirdPartyType != null">THIRD_PARTY_TYPE = #{thirdPartyType},</if>
            <if test="insuredApplyStatus != null">INSURED_APPLY_STATUS = #{insuredApplyStatus},</if>
            <if test="accidentScene != null">ACCIDENT_SCENE = #{accidentScene},</if>
            <if test="accidentSceneName != null">ACCIDENT_SCENE_NAME = #{accidentSceneName},</if>
            <if test="other != null">OTHER = #{other},</if>
            <if test="entrustmentDpmCode != null">ENTRUSTMENT_DPM_CODE = #{entrustmentDpmCode},</if>
            <if test="entrustmentDpmName != null">ENTRUSTMENT_DPM_NAME = #{entrustmentDpmName},</if>
            <if test="contactName != null">CONTACT_NAME = #{contactName},</if>
            <if test="contactPhone != null">CONTACT_PHONE = #{contactPhone},</if>
            <if test="entrustmentDescription != null">ENTRUSTMENT_DESCRIPTION = #{entrustmentDescription},</if>
            <if test="entrustmentObject != null">ENTRUSTMENT_OBJECT = #{entrustmentObject},</if>
            <if test="litigationStrategy != null">LITIGATION_STRATEGY = #{litigationStrategy},</if>
            <if test="feeStandard != null">FEE_STANDARD = #{feeStandard},</if>
            <if test="approverUmCode != null">APPROVER_UM_CODE = #{approverUmCode},</if>
            <if test="approverUmName != null">APPROVER_UM_NAME = #{approverUmName},</if>
            <if test="approverDpmCode != null">APPROVER_DPM_CODE = #{approverDpmCode},</if>
            <if test="approverDpmName != null">APPROVER_DPM_NAME = #{approverDpmName},</if>
            <if test="entrustmentStatus != null">ENTRUSTMENT_STATUS = #{entrustmentStatus},</if>
            <if test="processStatus != null">PROCESS_STATUS = #{processStatus},</if>
            <if test="processFile != null">PROCESS_FILE = #{processFile},</if>
            <if test="operate != null">OPERATE = #{operate},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            <if test="sysUtime != null">SYS_UTIME = #{sysUtime},</if>
        </set>
        WHERE ID_ENTRUSTMENT = #{idEntrustment}
    </update>

    <!-- 根据报案号和赔付次数查询 -->
    <select id="selectByReportNoAndCaseTime" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        ORDER BY SYS_CTIME DESC
        LIMIT 1
    </select>

    <!-- 根据报案号查询历史记录 -->
    <select id="selectHistoryByReportNo" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT
        WHERE REPORT_NO = #{reportNo}
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据审批人查询待审批列表 -->
    <select id="selectPendingApprovalList" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT
        WHERE APPROVER_UM_CODE = #{approverUmCode}
        AND ENTRUSTMENT_STATUS = '1'
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据主键查询 -->
    <select id="selectById" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT
        WHERE ID_ENTRUSTMENT = #{idEntrustment}
    </select>

    <!-- 查询用于打印的委托信息 -->
    <select id="selectForPrint" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT
        WHERE REPORT_NO = #{reportNo}
        AND THIRD_PARTY_TYPE = '01'
        AND ENTRUSTMENT_STATUS = '3'
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 查询未审批完成的第三方委托任务 -->
    <select id="selectUnapprovedEntrustments" resultType="com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO">
        SELECT * FROM CLMS_ENTRUSTMENT
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND ENTRUSTMENT_STATUS IN ('1', '2')  <!-- 假设 '1' 为草稿状态，'2' 为待审批状态 -->
        AND VALID_FLAG = 'Y'
    </select>
    
    <!-- 获取未提交的委托数据 -->
    <select id="getNoCommitData" resultMap="result">
        SELECT * FROM CLMS_ENTRUSTMENT
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND ENTRUSTMENT_STATUS = '1' <!-- 草稿状态 -->
        AND VALID_FLAG = 'Y'
        ORDER BY SYS_CTIME DESC
        LIMIT 1
    </select>

</mapper>