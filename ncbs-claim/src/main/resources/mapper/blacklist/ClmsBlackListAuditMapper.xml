<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListAudit">
        <id column="id" property="id" />
        <result column="black_list_id" property="blackListId" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="operate_type" property="operateType" />
        <result column="submit_by" property="submitBy" />
        <result column="submit_time" property="submitTime" />
        <result column="audit_status" property="auditStatus" />
        <result column="audit_by" property="auditBy" />
        <result column="audit_time" property="auditTime" />
        <result column="audit_result" property="auditResult" />
        <result column="audit_desc" property="auditDesc" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <!-- 查询待审批黑名单 -->
    <select id="getPendingAudits" resultType="com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListAuditVO">
        SELECT
        audit.id,
        audit.report_no,
        audit.case_times,
        audit.black_list_id,
        audit.operate_type,
        audit.submit_by,
        audit.submit_time,
        audit.audit_status,
        audit.audit_by,
        audit.audit_time,
        record.party_type,
        record.party_name,
        record.id_type,
        record.id_num,
        record.phone_num,
        record.risk_type,
        record.black_source
        FROM
        clms_black_list_audit AS audit
        INNER JOIN
        clms_black_list_record AS record
        ON
        audit.black_list_id = record.black_list_id
        WHERE
        audit.audit_status = '1'
        ORDER BY
        audit.submit_time DESC
    </select>

    <!-- 根据id查询 -->
    <select id="getBlackListAuditById" resultMap="BaseResultMap">
        SELECT
        id,
        black_list_id,
        report_no,
        case_times,
        operate_type,
        submit_by,
        submit_time,
        audit_status,
        audit_by,
        audit_time,
        audit_result,
        audit_desc,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        FROM clms_black_list_audit
        WHERE black_list_id = #{blackListId}
        and audit_status = '1'
    </select>

    <!-- 新增审批记录 -->
    <insert id="saveClmsBlackListAudit" parameterType="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListAudit">
        INSERT INTO clms_black_list_audit (
        id,
        black_list_id,
        report_no,
        case_times,
        operate_type,
        submit_by,
        submit_time,
        audit_status,
        audit_by,
        audit_time,
        audit_result,
        audit_desc,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        replace(UUID(), '-', ''),
        #{blackListId},
        #{reportNo},
        #{caseTimes},
        #{operateType},
        #{submitBy},
        NOW(),
        #{auditStatus},
        #{auditBy},
        #{auditTime},
        #{auditResult},
        #{auditDesc},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW()
        )
    </insert>

    <!-- 更新审批状态 -->
    <update id="updateAuditStatus" parameterType="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListAudit">
        UPDATE clms_black_list_audit
        SET
        audit_status = #{auditStatus},
        audit_by = #{auditBy},
        audit_time = NOW(),
        audit_result = #{auditResult},
        audit_desc = #{auditDesc},
        updated_by = #{auditBy},
        sys_utime = NOW()
        WHERE black_list_id = #{blackListId}
        ORDER BY submit_time DESC LIMIT 1
    </update>

    <!-- 根据 black_list_id 查询待审批黑名单 -->
    <select id="getPendingAuditByBlackListId" resultType="com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListAuditVO">
        SELECT
        audit.id,
        audit.report_no,
        audit.case_times,
        audit.black_list_id,
        audit.operate_type,
        audit.submit_by,
        audit.submit_time,
        audit.audit_status,
        audit.audit_by,
        audit.audit_time,
        record.party_type,
        record.party_name,
        record.id_type,
        record.id_num,
        record.phone_num,
        record.risk_type,
        record.black_source
        FROM
        clms_black_list_audit AS audit
        INNER JOIN
        clms_black_list_record AS record
        ON
        audit.black_list_id = record.black_list_id
        WHERE
        audit.audit_status = '1'
        AND audit.black_list_id = #{blackListId}
        ORDER BY
        audit.submit_time DESC
    </select>

</mapper>
