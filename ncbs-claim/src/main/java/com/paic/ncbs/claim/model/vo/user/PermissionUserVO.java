package com.paic.ncbs.claim.model.vo.user;

import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;

import java.util.List;

public class PermissionUserVO {
    private String userId;
    private String userName;
    private String typeCode;
    private String comCode;
    private List<GradeVO> gradeList;
    private Integer grade;
    private String contains;
    private List<SystemComInfoDTO> departmentList;
    private List<PermissionTypeVO> typeList;
    private List<String> queryDeptCodeList;
    private Pager pager;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public List<GradeVO> getGradeList() {
        return gradeList;
    }

    public void setGradeList(List<GradeVO> gradeList) {
        this.gradeList = gradeList;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getContains() {
        return contains;
    }

    public void setContains(String contains) {
        this.contains = contains;
    }

    public List<SystemComInfoDTO> getDepartmentList() {
        return departmentList;
    }

    public void setDepartmentList(List<SystemComInfoDTO> departmentList) {
        this.departmentList = departmentList;
    }

    public List<PermissionTypeVO> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<PermissionTypeVO> typeList) {
        this.typeList = typeList;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public List<String> getQueryDeptCodeList() {
        return queryDeptCodeList;
    }

    public void setQueryDeptCodeList(List<String> queryDeptCodeList) {
        this.queryDeptCodeList = queryDeptCodeList;
    }

    public Pager getPager() {
        return pager;
    }

    public void setPager(Pager pager) {
        this.pager = pager;
    }
}
