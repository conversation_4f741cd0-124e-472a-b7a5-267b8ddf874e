package com.paic.ncbs.claim.model.dto.ocr;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单OCR记录表
 */
@Data
public class BillOcrRecordDTO implements Serializable {


    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 付款人姓名
     */
    private String payerName;

    /**
     * 发票号
     */
    private String receiptNo;

    /**
     * 发票金额
     */
    private BigDecimal receiptAmount;

    /**
     * 就诊日期
     */
    private Date inHospitalDate;

    /**
     * 医保类型,1.社保；2.普通
     */
    private String billType;

    /**
     * 就诊人性别,F:女；M:男
     */
    private String gender;

    /**
     * 第三方支付金额
     */
    private BigDecimal thirdPayAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 个人账户金额
     */
    private BigDecimal selfAccountAmount;

    /**
     * 个人现金支付
     */
    private BigDecimal selfCashAmount;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 数据渠道
     */
    private String channel;

    /**
     * 文件地址
     */
    private String uploadPath;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 发票分类，01-门诊，02-住院，03-药房，04-废票
     */
    private String billClassify;

    /**
     * 风险提示信息代码
     */
    private String riskInfoCodeList;

    /**
     * 医疗机构类型
     */
    private String hospitalType;


    private static final long serialVersionUID = 1L;
}