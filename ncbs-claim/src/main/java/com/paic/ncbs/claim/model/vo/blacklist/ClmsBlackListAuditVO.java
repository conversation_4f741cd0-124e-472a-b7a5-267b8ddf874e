package com.paic.ncbs.claim.model.vo.blacklist;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("审批记录vo")
public class ClmsBlackListAuditVO{

    @ApiModelProperty("")
    private String id;

    @ApiModelProperty("关联的黑名单ID")
    private String blackListId;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("提交人")
    private String submitBy;

    @ApiModelProperty("提交时间")
    private Date submitTime;

    @ApiModelProperty("审批状态")
    private String auditStatus;

    @ApiModelProperty("审批人")
    private String auditBy;

    @ApiModelProperty("审批时间")
    private Date auditTime;

    @ApiModelProperty("审批结果")
    private String auditResult;

    @ApiModelProperty("审批描述")
    private String auditDesc;

    @ApiModelProperty("黑名单类型")
    private String partyType;
    @ApiModelProperty("黑名单类型中文描述")
    private String partyTypeName;

    @ApiModelProperty("姓名/名称")
    private String partyName;

    @ApiModelProperty("证件类型")
    private String idType;
    @ApiModelProperty("证件类型中文描述")
    private String idTypeName;

    @ApiModelProperty("证件号码")
    private String idNum;

    @ApiModelProperty("风险类型")
    private String riskType;
    @ApiModelProperty("风险类型中文描述")
    private String riskTypeName;

    @ApiModelProperty("电话号码")
    private String phoneNum;

    @ApiModelProperty("来源")
    private String blackSource;
    @ApiModelProperty(value = "页码")
    private Integer currentPage;
    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;

}

