package com.paic.ncbs.claim.dao.entity.clms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 理赔二核申请记录表(ClmsSecondUnderwritingEntity)实体类
 *
 * <AUTHOR>
 * @since 2023-09-08 15:15:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ClmsSecondUnderwritingEntity实体类")
public class ClmsSecondUnderwritingEntity implements Serializable {
    private static final long serialVersionUID = 964523950609098843L;
    /**
     * 主键
     */
    @ApiModelProperty(" 主键")
    private String id;
    /**
     * 报案号
     */
    @ApiModelProperty(" 报案号")
    private String reportNo;
    /**
     * 赔付次数
     */
    @ApiModelProperty(" 赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("核保任务号")
    private String manualInfoId;

    @ApiModelProperty("序号")
    private String serialNo;
    /**
     * 诊断信息：code-name-date组合逗号分隔
     */
    @ApiModelProperty("诊断信息：code_name_date#code_name_date 组合#分隔")
    private String diseaseInfo;
    /**
     * 证据材料类型：1-就诊病历，2-检查报告，3-医保记录，4-客户笔录，5-公检法证明，多个逗号分隔
     */
    @ApiModelProperty("证据材料类型：1-就诊病历，2-检查报告，3-医保记录，4-客户笔录，5-公检法证明，多个逗号分隔")
    private String evidenceMaterialType;
    /**
     * 材料的文件id
     */
    @ApiModelProperty("材料的文件id")
    private String materialFileId;

    /**
     * 送核说明
     */
    @ApiModelProperty("送核说明")
    private String underwritingExplain;
    /**
     * 送核状态：01-送核审批中，02-审核通过
     */
    @ApiModelProperty("任务业务节点：OC_REPORT_TRACK-报案跟踪，OC_CHECK_DUTY-收单，OC_MANUAL_SETTLE-理算")
    private String taskCode;
    /**
     * 环节号
     */
    @ApiModelProperty("送核状态：01-送核审批中，02-核保通过,03-核保退回")
    private String underwritingStatus;
    /**
     * 创建人员
     */
    @ApiModelProperty(" 创建人员")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(" 创建时间")
    private Date createdDate;
    /**
     * 事故日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("事故日期")
    private Date accidentDate;
    /**
     * 修改人员
     */
    @ApiModelProperty(" 修改人员")
    private String updatedBy;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("二核发起时间")
    private Date uwStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("二核完成时间")
    private Date uwCompleteDate;

    @ApiModelProperty("核保意见")
    private String uwAdvice;
    @ApiModelProperty("整案核保结论报案号维度")
    private String conclusion;

    @ApiModelProperty("核保人员")
    private String uwOperator;



    @ApiModelProperty("回销状态：01-无函件无需回销，02-待回销，03-回销已完成")
    private String lettersCancelStatus;
    /**
         *  等待期天数 理赔专用(用于处理自动二核数据)
         */
    @ApiModelProperty("等待期天数")
    private Integer waitingPeriodDays;

}

