package com.paic.ncbs.claim.replevy.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表clms_replevy_charge的VO对象<br/>
 * 对应表名：clms_replevy_charge,备注：追偿费用信息表
 *
 */
@Data
public class ClmsReplevyChargeVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	private String id;
	/** 对应字段：invoice_info_id,备注：发票明细id */
	private String invoiceInfoId;
	/** 对应字段：payment_info_id,备注：领款人信息表id */
	private String paymentInfoId;
	/** 费用信息表id */
	private String idClmPaymentItem;
	/** 对应字段：report_no,备注：报案号 */
	private String reportNo;
	/** 对应字段：replevy_no,备注：追偿案件号 */
	private String replevyNo;
	/** 对应字段：replevy_times,备注：追偿次数 */
	private Integer replevyTimes;
	/** 对应字段：case_times,备注：赔付次数 */
	private Integer caseTimes;
	/**
	 * 险种
	 */
	private String planCode;
	/**
	 * 责任
	 */
	private String dutyCode;
	/** 对应字段：charge_type,备注：直接理赔费用类型 */
	private String chargeType;
	/** 对应字段：pay_object,备注：支付对象 */
	private String payObject;
	/** 对应字段：currency,备注：币别 */
	private String currency;
	/** 对应字段：charge_money,备注：费用金额 */
	private BigDecimal chargeMoney;
	/** 对应字段：apply_link,备注：申请环节 */
	private String applyLink;
	/** 对应字段：apply_person,备注：申请人 */
	private String applyPerson;
	/** 对应字段：apply_time,备注：申请时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date applyTime;
	/** 对应字段：apply_reason,备注：申请原因 */
	private String applyReason;
	/** 对应字段：charge_describe,备注：费用说明 */
	private String chargeDescribe;
	/** 对应字段：client_name,备注：收款人姓名 */
	private String clientName;
	/** 对应字段：client_account,备注：收款人账号 */
	private String clientAccount;
	/** 费用审核状态 1-已申请，2-待审核，3-审核通过 4-退回 */
	private String approveFlag;
	/** 有效标志Y-有效，N-失效 */
	private String validFlag;
	/**标志字段0-进行中，1-任务完成 */
	private String flag;
	/** 序号，用于送收付次数 */
	private Integer serialNo;
	/** 任务完成时间 */
	private Date finishDate;
	/** 对应字段：created_by,备注：创建人 */
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	private Date sysCtime;
	/** 对应字段：updated_by,备注：修改人员 */
	private String updatedBy;
	/** 对应字段：sys_utime,备注：修改时间 */
	private Date sysUtime;

}
