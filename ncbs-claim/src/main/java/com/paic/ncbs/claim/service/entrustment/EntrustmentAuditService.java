package com.paic.ncbs.claim.service.entrustment;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

import java.util.List;

public interface EntrustmentAuditService {

    /**
     * 根据委托ID查询审批历史
     * @param idEntrustment 委托ID
     * @return 审批历史列表
     */
    List<EntrustmentAuditDTO> getAuditHistoryByEntrustmentId(String idEntrustment);
}
