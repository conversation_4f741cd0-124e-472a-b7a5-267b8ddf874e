package com.paic.ncbs.claim.model.dto.estimate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.vo.duty.LossEstimationVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeApplyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@ApiModel("预估保单表单dto")
public class EstimateChangePolicyFormDTO extends EntityDTO {

    @ApiModelProperty("列表排序序号")
    private Integer sortNum;
    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("赔款立案金额总和")
    private BigDecimal sumDutyPayAmount;
    @ApiModelProperty("费用立案金额总和")
    private BigDecimal sumDutyFeeAmount;

    @ApiModelProperty("立案人")
    private String registerUm;

    @ApiModelProperty("立案人姓名")
    private String registerName;

    @ApiModelProperty("立案时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerDate;

    @ApiModelProperty("修正原因")
    private String estimateChangeReason;

    @ApiModelProperty("意键险保单预估列表")
    private List<EstimateChangePolicyDTO> estimatePolicyList;

    @ApiModelProperty(value = "TPA公司ID")
    private String companyId;

    @ApiModelProperty("未决修正审批信息id")
    private String idEstimateChangeApply;

    @ApiModelProperty(value = "未决审批记录")
    private List<EstimateChangeApplyVO> estimateChangeApplyVOList;
    @ApiModelProperty(value = "未决历史history和change关联id")
    private String idFlagHistoryChange;
    @ApiModelProperty("估损类型 3-人伤；4-健康；5-财产；6-其他")
    private String lossClass;
    @ApiModelProperty(value="估损信息")
    private Map<String,List<LossEstimationVO>> lossEstimationVos;

}
