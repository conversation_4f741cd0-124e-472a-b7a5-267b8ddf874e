package com.paic.ncbs.claim.controller.secondunderwriting;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.Transform;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsSecondUnderwritingVO;
import com.paic.ncbs.claim.model.vo.senconduw.LetterSellBackSubmitDTO;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUnderwritingService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUwLetterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 理赔二核申请记录表(ClmsSecondUnderwritingEntity)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-08 15:15:58
 */
@RestController
@RequestMapping("/second/underwriting")
public class ClmsSecondUnderwritingController {
    /**
     * 服务对象
     */
    @Autowired
    private ClmsSecondUnderwritingService clmsSecondUnderwritingService;

    @Autowired
    private ClmsSecondUwLetterService clmsSecondUwLetterService;
    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;


    /**
     * 新增数据
     * {
     *     "accidentDate": "2023-09-09 00:00:00",
     *     "reportNo": "90011000000000012769",
     *     "caseTimes":1,
     *     "diseaseInfo": "code_name_date#code_name_date",
     *     "evidenceMaterialType": "1,3",
     *     "materialFileId": "123,456",
     *     "taskCode": "OC_MANUAL_SETTLE"
     * }
     * @param clmsSecondUnderwritingEntity 实体
     * @return 新增结果
     */
    @PostMapping("/sendTask")
    public ResponseResult<Object> sendTask(@RequestBody ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity) {
        clmsSecondUnderwritingService.sendTask(clmsSecondUnderwritingEntity);
        //发起二核后 通知TPA中台 案件状态发生了变化（只有线上的会通知，线下的单子不会）
        if(Objects.equals(BpmConstants.OC_REPORT_TRACK,clmsSecondUnderwritingEntity.getTaskCode())){
            claimSendTpaMqInfoService.sendTpaMq(clmsSecondUnderwritingEntity.getReportNo(),clmsSecondUnderwritingEntity.getCaseTimes(), Transform.getUWCaseProcessStatus(clmsSecondUnderwritingEntity.getTaskCode()));
        }
        return ResponseResult.success();
    }

    /**
     * 历史数据列表查询
     *
     * @param
     * @return 历史数据列表
     */
    @GetMapping("/getRecords/{reportNo}/{caseTimes}")
    public ResponseResult<List<ClmsSecondUnderwritingVO>> getRecords(@PathVariable("reportNo") String reportNo,
                                                                      @PathVariable("caseTimes") Integer caseTimes) {
        List<ClmsSecondUnderwritingVO> clmsSecondUnderwritingVOS = clmsSecondUnderwritingService.getUWRecords(reportNo,null);
        return ResponseResult.success(clmsSecondUnderwritingVOS);
    }

    /**
     * 理赔核保按钮的飘红显示查询
     *
     * @param
     * @return 二核次数
     */
    @GetMapping("/getCount/{reportNo}/{caseTimes}")
    public ResponseResult<Integer> getCount(@PathVariable("reportNo") String reportNo,
                                       @PathVariable("caseTimes") Integer caseTimes) {
        Integer uwCount = clmsSecondUnderwritingService.getUWCount(reportNo, null);
        return ResponseResult.success(uwCount);
    }

    /**
     * 根据id查询核保详情
     * @param id
     * @return
     */
    @GetMapping("/getUwDetailInfo/{id}")
    public ResponseResult<ClmsSecondUnderwritingVO> getUwDetailInfo(@PathVariable("id") String id){
        return ResponseResult.success(clmsSecondUnderwritingService.getUwDetailInfo(id));
    }

    /**
     * 函件回销提交
     * @param letterSellBackSubmitDTO
     * @return
     */
    @PostMapping("/letterSellBack")
    public ResponseResult<Object> letterSellBack(@RequestBody LetterSellBackSubmitDTO letterSellBackSubmitDTO) {
        return clmsSecondUwLetterService.letterSellBack(letterSellBackSubmitDTO);
    }

}

