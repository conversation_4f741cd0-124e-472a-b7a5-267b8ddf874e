package com.paic.ncbs.claim.model.dto.print;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 公估委托书打印DTO
 */
@Data
public class AppraisalCommissionDTO {

    /**
     * 调查信息表主键
     */
    private String idAhcsInvestigate;
    /**
     * 主键-委托信息表
     */
    private String idEntrustment;
    /**
     * printFlag 1-提调 2-委托
     */
    private String printFlag;
    /**
     * 委托日期
     */
    private String entrustedDate;
    /**
     * 受委托方-名称
     */
    private String entrustedPartyName;
    /**
     * 受委托方-联系人
     */
    private String entrustedPartyContainer;
    /**
     * 受委托方-Tel
     */
    private String entrustedPartyContactTel;
    /**
     * 受委托方-Mobile
     */
    private String entrustedPartyContactMobile;
    /**
     * 受委托方-Email
     */
    private String entrustedPartyContactEmail;

    /**
     * 被保险人-名称
     */
    private String insuredName;
    /**
     * 被保险人-联系人
     */
    private String insuredContainer;
    /**
     * 被保险人-Tel
     */
    private String insuredTel;
    /**
     * 被保险人-Mobile
     */
    private String insuredMobile;
    /**
     * 被保险人-Email
     */
    private String insuredEmail;
    /**
     * 出险险种
     */
    private String riskCode;
    /**
     * 我司案号
     */
    private String reportNo;
    /**
     * 事故简要
     */
    private String accidentDetail;
    /**
     * 提调说明
     */
    private String investigateItems;
    /**
     * 调查内容-事故场景name
     */
    private String accidentScene;
    /**
     * 委托方-联系人
     */
    private String clientContainer;
    /**
     * 委托方-Tel
     */
    private String clientTel;
    /**
     * 委托方-Mobile
     */
    private String clientMobile;
    /**
     * 委托方-Email
     */
    private String clientEmail;


}
