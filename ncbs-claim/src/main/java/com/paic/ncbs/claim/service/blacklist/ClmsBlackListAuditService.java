package com.paic.ncbs.claim.service.blacklist;

import com.paic.ncbs.claim.model.vo.blacklist.BlackListAuditDetailVO;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListAuditVO;
import com.paic.ncbs.claim.utils.PageResult;

import java.util.List;

/**
 * <p>
 * 黑名单审批记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ClmsBlackListAuditService {

    PageResult<ClmsBlackListAuditVO> getPendingAudits(ClmsBlackListAuditVO clmsBlackListAuditVO);

    BlackListAuditDetailVO getBlackListAuditById(String id) throws Exception;

    void blacklistApprovalProcess(ClmsBlackListAuditVO clmsBlackListAuditVO) throws  Exception;

    ClmsBlackListAuditVO getPendingAuditByBlackListId(String blackListId);



}
