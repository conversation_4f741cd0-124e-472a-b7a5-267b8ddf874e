package com.paic.ncbs.claim.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 产品大类
 */
@Getter
public enum ProductClassEnum {

    PRODUCT_CLASS_01("01", "其它险"),
    PRODUCT_CLASS_02("02", "健康险"),
    PRODUCT_CLASS_03("03", "意外险"),
    PRODUCT_CLASS_04("04", "责任险"),
    PRODUCT_CLASS_05("05", "家财险"),
    PRODUCT_CLASS_22("22", "企财险"),
    PRODUCT_CLASS_23("23", "保证险");

    private final String type;
    private final String name;

    ProductClassEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getName(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }

        for (ProductClassEnum productClass : ProductClassEnum.values()) {
            if (productClass.getType().equals(type)) {
                return productClass.getName();
            }
        }
        return null;
    }
}
