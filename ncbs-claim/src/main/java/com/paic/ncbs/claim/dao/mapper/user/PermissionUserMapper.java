package com.paic.ncbs.claim.dao.mapper.user;

import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.user.PermissionUserVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface PermissionUserMapper {

    void addPermissionUser(PermissionUserDTO permissionUserDTO);

    List<PermissionUserDTO> getPermissionUserList(PermissionUserVO permissionUserVO);

    Integer getUserGrade(@Param("typeCode") String typeCode,@Param("comCode") String comCode,@Param("userId") String userId);

    List<String> getUserList(@Param("typeCode") String typeCode,@Param("comCode") String comCode,@Param("grade") Integer grade);

    void updatePermissionUser(PermissionUserDTO permissionUser);

    void removePermissionUser(@Param("idClmsPermissionUser") String idClmsPermissionUser);

    Integer getUserGradeCount(@Param("typeCode") String typeCode,@Param("comCode") String comCode,@Param("userId") String userId);

    Integer getGradeByComCode(@Param("typeCode") String typeCode,@Param("comCode") String comCode,@Param("grade") Integer grade);

    String getParentComCode(@Param("comCode") String comCode);

    Integer getSamePermissionUserCount(@Param("grade") Integer grade,@Param("idClmsPermissionUser") String idClmsPermissionUser);

    List<PermissionUserDTO> getVerifyUserList(@Param("userId") String userId,
                                              @Param("list") List<String> list,
                                              @Param("typeCode") String typeCode);

    String getComCodeByUserId(@Param("userId") String userId);

}