package com.paic.ncbs.claim.sao.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.AmlCodeEnum;
import com.paic.ncbs.claim.common.enums.BankAccountTypeEnum;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.util.AmlUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.AmlRequest;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.*;
import com.paic.ncbs.claim.sao.AbstractBaseSAO;
import com.paic.ncbs.claim.sao.AntiMoneyLaunderingSAO;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsAmlInfoService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.policy.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.BaseConstant.*;
import static com.paic.ncbs.claim.common.constant.SettleConst.BUSINESS_TYPE_PERSON;

@Slf4j
@Service
public class AntiMoneyLaunderingSAOImpl extends AbstractBaseSAO implements AntiMoneyLaunderingSAO {

    @Autowired
    private OcasRequest ocasRequest;

    @Autowired
    private AmlRequest amlRequest;

    @Autowired
    private ClmsAmlInfoService clmsAmlInfoService;

    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @Autowired
    private ReportAccidentMapper reportAccidentMapper;

    @Value("${env}")
    private String env;

    @Override
    public void reportAmlTarget(List<PaymentTransAmlData> paymentTransAmlDataList) {
        log.info("AntiMoneyLaunderingSAO.reportAmlTarget, start, amlDataList={}", JSON.toJSONString(paymentTransAmlDataList));
        if (CollectionUtils.isEmpty(paymentTransAmlDataList)){
            throw new GlobalBusinessException("领款人支付信息不能为空！");
        }
        if (!"samsung".equals(env)) {
            paymentTransAmlDataList.forEach(p->{
                p.setCode(NcbsConstant.RESULT_SUCCESS);
                p.setMessage("上报可疑数据成功");
                p.setSerialNo(UuidUtil.getUUID());
            });
            return;
        }
        // 根据保单号分组，以节省调用批改时间，实际场景不会很多
        Map<String, List<PaymentTransAmlData>> groupingByPolicyNo = paymentTransAmlDataList.stream().collect(Collectors.groupingBy(PaymentTransAmlData::getPolicyNo));
        Map<String, ContractDTO> contractDTOMap = getContractDTOMap(groupingByPolicyNo);
        groupingByPolicyNo.forEach((k,v)->{
            ContractDTO contractDTO = contractDTOMap.get(k);
            int payDtmSeqNum = 1;
            for (PaymentTransAmlData paymentTransAmlData : v) {
                //AntimoneyKeyiParam antimoneyKeyiParam = getAmlParam(contractDTO,paymentTransAmlData,payDtmSeqNum);
                AmlReportVO amlReportVO = getAmlReport(contractDTO,paymentTransAmlData,String.valueOf(payDtmSeqNum));
                String paraJson = "";
                try {
                    paraJson = JsonUtils.toJsonString(amlReportVO);
                    log.info("AntiMoneyLaunderingSAO.reportAmlTarget, params: {}", paraJson);
                } catch (Exception e) {
                    log.error("AntiMoneyLaunderingSAO.reportAmlTarget-入参转换异常",e);
                    return;
                }
                String response = amlRequest.reportGlobalAmlTarget(paraJson);
                if (response == null){
                    log.info("接口调用异常");
                    return;
                }
                log.info("AntiMoneyLaunderingSAO.reportAmlTarget, response: {}", response);
                AntimoneyKeyiResponse antimoneyKeyiResponse = null;
                try {
                    antimoneyKeyiResponse = JsonUtils.toObject(response, AntimoneyKeyiResponse.class);
                } catch (Exception e) {
                    log.error("AntiMoneyLaunderingSAO.reportAmlTarget-返回结果转换异常",e);
                    return;
                }
                if (antimoneyKeyiResponse == null || !"200".equals(antimoneyKeyiResponse.getCode())){
                    return;
                }
                paymentTransAmlData.setCode(NcbsConstant.RESULT_SUCCESS);
                paymentTransAmlData.setMessage("上报可疑数据成功");
                paymentTransAmlData.setSerialNo(antimoneyKeyiResponse.getDealNo());
                payDtmSeqNum ++;
            }
        });
        log.info("AntiMoneyLaunderingSAO.reportAmlTarget, end, amlDataList={}", JSON.toJSONString(paymentTransAmlDataList));
    }

    /**
      *
      * @Description 根据保单号调用批改获取contractDTO保单详细信息
      * <AUTHOR>
      * @Date 2023/10/17 9:52
      **/
    private Map<String, ContractDTO> getContractDTOMap(Map<String, List<PaymentTransAmlData>> groupingByPolicyNo) {
        Map<String,ContractDTO> contractDTOMap = new HashMap<>();

        Set<String> policyNoSet = groupingByPolicyNo.keySet();
        for (String policyNo : policyNoSet) {
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(policyNo,null);
            String result = ocasRequest.getPolicyInfoByPolicyNoContract(queryVO);
            if (StringUtils.isEmptyStr(result)){
                throw new GlobalBusinessException("调用批改抄单接口异常！");
            }
            JSONObject ocasResult = JSON.parseObject(result);
            ContractDTO contractDTO = ocasResult.getJSONObject("data").getJSONObject("contractDTO").toJavaObject(ContractDTO.class);
            contractDTOMap.put(policyNo,contractDTO);
        }
        return contractDTOMap;
    }

    /**
     *
     * @Description 组装反洗钱数据
     * @Date 2024/01/15
     **/
    private AmlReportVO getAmlReport(ContractDTO contractDTO, PaymentTransAmlData paymentTransAmlData, String payDtmSeqNum) {
        //反洗钱上报主体
        AmlReportVO amlReportVO = new AmlReportVO();
        //可疑交易对象
        String stcr = AmlCodeEnum.AML_CODE_00008.getType().equals(paymentTransAmlData.getAntimoneyCd()) ? "STR0002":"STR0012";
        AmlBIsMainDto amlBIsMainDto = getamlBisMain(contractDTO,payDtmSeqNum,stcr);
        //交易主体联系方式
        List<AmlBContactDtos> amlBContactDtos = getamlBcontact(contractDTO);
        //可疑交易明细
        List<AmlBIsDetailDtos> amlBIsDetailDtos = getamlBisDetail(contractDTO,paymentTransAmlData);
        amlReportVO.setAmlBIsMainDto(amlBIsMainDto);
        amlReportVO.setAmlBContactDtos(amlBContactDtos);
        amlReportVO.setAmlBIsDetailDtos(amlBIsDetailDtos);
        return amlReportVO;
    }

    private List<AmlBIsDetailDtos> getamlBisDetail(ContractDTO contractDTO, PaymentTransAmlData paymentTransAmlData) {
        BaseInfoDTO baseInfo = contractDTO.getBaseInfo();
        SaleDTO saleInfo = contractDTO.getSaleInfo();
        ReportAccidentEntity reportAccident = reportAccidentMapper.getReportAccident(paymentTransAmlData.getReportNo());
        String transDate = DateUtils.dateFormat(new Date(),DateUtils.SIMPLE_DATE_STR);

        List<ApplicantDTO> applicantInfoList = contractDTO.getApplicantInfoList();
        List<PayInfoDTO> payInfoDTOList = contractDTO.getPayInfoList();
        List<RiskGroupDTO> riskGroupInfoList = contractDTO.getRiskGroupInfoList();
        ApplicantDTO applicantDTO = applicantInfoList.get(0);
        RiskGroupDTO riskGroupDTO = riskGroupInfoList.get(0);
        List<RiskPersonDTO> riskPersonInfoList = riskGroupDTO.getRiskPersonInfoList();

        //可疑交易明细列表
        List<AmlBIsDetailDtos> amlBIsDetailDtos = new ArrayList<>();
        AmlBIsDetailDtos amlBIsDetailDto = new AmlBIsDetailDtos();
        amlBIsDetailDto.setTicd(paymentTransAmlData.getCaseNo());
        amlBIsDetailDto.setIcnm(baseInfo.getPolicyNo());
        amlBIsDetailDto.setTstm(transDate);
        amlBIsDetailDto.setProvince(reportAccident.getProvinceCode());
        amlBIsDetailDto.setCity(reportAccident.getAccidentCityCode());
        amlBIsDetailDto.setCounty(reportAccident.getAccidentCountyCode());
        amlBIsDetailDto.setCrat(paymentTransAmlData.getPaymentAmount());
        amlBIsDetailDto.setCaoi(paymentTransAmlData.getClientBankName());
        amlBIsDetailDto.setTcan(paymentTransAmlData.getClientBankAccount());

        //可疑交易合同信息
        AmlBIsContractDto amlBIsContractDto = new AmlBIsContractDto();
        amlBIsContractDto.setIcnm(baseInfo.getPolicyNo());
        amlBIsContractDto.setFinc(baseInfo.getDepartmentCode());

        amlBIsContractDto.setAlnm(applicantDTO.getName());
        amlBIsContractDto.setAitp(AmlUtils.toCertificateNo(applicantDTO.getCertificateType()));
        amlBIsContractDto.setAlid(applicantDTO.getCertificateNo());
        amlBIsContractDto.setIsnm(baseInfo.getProductName());
        amlBIsContractDto.setRiskCode(riskGroupDTO.getPlanInfoList().get(0).getPlanCode());
        amlBIsContractDto.setEffectiveDate(DateUtil.formatDate(baseInfo.getInsuranceBeginDate()));
        amlBIsContractDto.setExpirDate(DateUtil.formatDate(baseInfo.getInsuranceEndDate()));
        amlBIsContractDto.setItnm(Long.valueOf(riskPersonInfoList.size()));
        amlBIsContractDto.setIsat(String.valueOf(baseInfo.getTotalInsuredAmount()));
        amlBIsContractDto.setIsfe(String.valueOf(baseInfo.getTotalActualPremium()));
        amlBIsContractDto.setAltp(applicantDTO.getApplicantType());
        amlBIsContractDto.setSaleChanel(AmlUtils.toTeamType(saleInfo.getTeamType()));
        if("1".equals(applicantDTO.getApplicantType())){
            amlBIsContractDto.setAltp("07");
        }else if("2".equals(applicantDTO.getApplicantType())){
            amlBIsContractDto.setAltp("03");
        }
        if(null != payInfoDTOList && payInfoDTOList.size() > 0){
            String installmentType = payInfoDTOList.get(0).getInstallmentType();
            if("0".equals(installmentType)){
                amlBIsContractDto.setIspt("02");
            }else{
                amlBIsContractDto.setIspt("01");
            }
        }

        //可疑交易被保险人信息
        List<AmlBIsInsuredDto> amlBIsInsuredDtoList = new ArrayList<>();


        if(riskPersonInfoList.size() > 0){
            for(RiskPersonDTO riskPersonDTO : riskPersonInfoList){
                if(Arrays.asList("200", "020").contains(riskPersonDTO.getPersonnelAttribute())){
                    log.info("反洗钱参数组装，虚拟被保人跳过，policyNo：{}", riskPersonDTO.getPolicyNo());
                    continue;
                }
                AmlBIsInsuredDto amlBIsInsuredDto = new AmlBIsInsuredDto();
                amlBIsInsuredDto.setIcnm(riskPersonDTO.getPolicyNo());
                amlBIsInsuredDto.setInsuredNo(riskPersonDTO.getClientNo());
                amlBIsInsuredDto.setIstn(riskPersonDTO.getName());
                amlBIsInsuredDto.setIitp(AmlUtils.toCertificateNo(riskPersonDTO.getCertificateType()));
                amlBIsInsuredDto.setIsid(riskPersonDTO.getCertificateNo());
                amlBIsInsuredDto.setRltp(AmlUtils.RelationToAppntMap.get(riskPersonDTO.getRelationshipWithApplicant()));
                if(null != riskPersonDTO.getBeneficaryInfoList() && riskPersonDTO.getBeneficaryInfoList().size() > 0){
                    List<BeneficaryDTO> beneficaryInfoList = riskPersonDTO.getBeneficaryInfoList();
                    amlBIsInsuredDto.setBntn(beneficaryInfoList.size());
                    //受益人信息
                    List<AmlBIsBenefitDto> amlBisBenfitList = new ArrayList<>();
                    for(BeneficaryDTO beneficaryDTO : beneficaryInfoList){
                        AmlBIsBenefitDto amlBIsBenefitDto = new AmlBIsBenefitDto();
                        amlBIsBenefitDto.setIcnm(beneficaryDTO.getPolicyNo());
                        amlBIsBenefitDto.setInsuredNo(riskPersonDTO.getClientNo());
                        amlBIsBenefitDto.setBnfNo(beneficaryDTO.getClientNo());
                        amlBIsBenefitDto.setBnnm(beneficaryDTO.getName());
                        amlBIsBenefitDto.setBitp(beneficaryDTO.getCertificateType());
                        amlBIsBenefitDto.setBnid(beneficaryDTO.getCertificateNo());
                        amlBisBenfitList.add(amlBIsBenefitDto);
                    }
                    amlBIsInsuredDto.setAmlBIsBenefitDtos(amlBisBenfitList);
                }
                amlBIsInsuredDtoList.add(amlBIsInsuredDto);
            }
        }

        amlBIsDetailDto.setAmlBIsContractDto(amlBIsContractDto);
        amlBIsDetailDto.setAmlBIsInsuredDtos(amlBIsInsuredDtoList);
        amlBIsDetailDtos.add(amlBIsDetailDto);

        return amlBIsDetailDtos;
    }

    private List<AmlBContactDtos> getamlBcontact(ContractDTO contractDTO) {
        List<AmlBContactDtos> amlBContactDtosList = new ArrayList<>();
        List<RiskGroupDTO> riskGroupInfoList = contractDTO.getRiskGroupInfoList();
        if(null != riskGroupInfoList && riskGroupInfoList.size() > 0){
            RiskGroupDTO riskGroupDTO = riskGroupInfoList.get(0);
            List<RiskPersonDTO> riskPersonInfoList = riskGroupDTO.getRiskPersonInfoList();
            for(RiskPersonDTO riskPersonDTO : riskPersonInfoList){
                AmlBContactDtos amlBContactDto = new AmlBContactDtos();
                amlBContactDto.setCsnm(riskPersonDTO.getClientNo());
                amlBContactDto.setNationality("CHN");
                amlBContactDto.setLinkNumber(null == riskPersonDTO.getMobileTelephone() ?"@N":riskPersonDTO.getMobileTelephone());
                amlBContactDto.setAddress(null == riskPersonDTO.getAddress() ?"@N":riskPersonDTO.getAddress());
                amlBContactDto.setCusothContact(null == riskPersonDTO.getHomeTelephone() ?"@N":riskPersonDTO.getHomeTelephone());
                amlBContactDtosList.add(amlBContactDto);
            }
        }

        return amlBContactDtosList;
    }

    private AmlBIsMainDto getamlBisMain(ContractDTO contractDTO,String payDtmSeqNum,String stcr) {
        BaseInfoDTO baseInfo = contractDTO.getBaseInfo();
        AmlBIsMainDto amlBIsMainDto = new AmlBIsMainDto();
        amlBIsMainDto.setRicd(baseInfo.getDepartmentCode());
        amlBIsMainDto.setRpnc(AmlUtils.dptToBranchCd(baseInfo.getDepartmentCode()));
        amlBIsMainDto.setManageCom(AmlUtils.dptToBranchCd(baseInfo.getDepartmentCode()));
        String transDate = DateUtils.dateFormat(new Date(),DateUtils.SIMPLE_DATE_STR);
        // 投保人 默认有且只应该有一个人
        List<ApplicantDTO> applicantInfoList = contractDTO.getApplicantInfoList();
        ApplicantDTO applicantDTO = applicantInfoList.get(0);
        amlBIsMainDto.setBaseline(transDate);
        //amlBIsMainDto.setDetr();
        //amlBIsMainDto.setStcb();
        amlBIsMainDto.setTorp(payDtmSeqNum);
        amlBIsMainDto.setDorp(STRING_01);
        amlBIsMainDto.setTptr(STRING_06);
        //amlBIsMainDto.setAosp();
        amlBIsMainDto.setStcr(stcr);
        amlBIsMainDto.setRpnm(WebServletContext.getUserId());
        amlBIsMainDto.setSetn(Long.valueOf(1));
        amlBIsMainDto.setStnm(Long.valueOf(1));
        amlBIsMainDto.setCsnm(applicantDTO.getClientNo());
        amlBIsMainDto.setSenm(applicantDTO.getName());
        amlBIsMainDto.setSeid(applicantDTO.getCertificateNo());
        amlBIsMainDto.setSetp(AmlUtils.toCertificateNo(applicantDTO.getCertificateType()));
        amlBIsMainDto.setSystemCode("CORE_CLAIM");
        return amlBIsMainDto;
    }

    /**
      *
      * @Description 组装反洗钱数据
      * <AUTHOR>
      * @Date 2023/10/12 14:37
      **/
    private AntimoneyKeyiParam getAmlParam( ContractDTO contractDTO,PaymentTransAmlData paymentTransAmlData,int payDtmSeqNum) {
        String transDate = DateUtils.dateFormat(new Date(),DateUtils.DATE_FORMAT_YYYYMMDD);
        BaseInfoDTO baseInfo = contractDTO.getBaseInfo();
        String policyNo = paymentTransAmlData.getPolicyNo();
        String departmentCode = baseInfo.getDepartmentCode();
        // 投保人 默认有且只应该有一个人
        List<ApplicantDTO> applicantInfoList = contractDTO.getApplicantInfoList();
        ApplicantDTO applicantDTO = applicantInfoList.get(0);

        List<RiskGroupDTO> riskGroupInfoList = contractDTO.getRiskGroupInfoList();
        int beneficarySeq = 1;
        List<AntimoneyKeyiInsured> insuredList = new ArrayList<>();
        int applyRiskNum = 0;
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(paymentTransAmlData.getReportNo());
        List<RiskPersonDTO> riskPersonInfoList = new ArrayList<>();
        for (RiskGroupDTO riskGroupDTO : riskGroupInfoList) {
            riskPersonInfoList.addAll(riskGroupDTO.getRiskPersonInfoList());
            applyRiskNum += riskGroupDTO.getApplyRiskNum();
        }

        RiskPersonDTO riskPersonDTO = riskPersonInfoList.stream().filter(person -> customerInfo.getClientNo().equals(person.getClientNo())).findFirst().orElseThrow(() -> new GlobalBusinessException("被保险人信息异常！"));
        List<BeneficaryDTO> beneficaryInfoList = riskPersonDTO.getBeneficaryInfoList();
        // 如果受益人为空，默认就给当前被保险人
        if (CollectionUtils.isEmpty(beneficaryInfoList)){
            AntimoneyKeyiInsured antimoneyKeyiInsured = new AntimoneyKeyiInsured();
            antimoneyKeyiInsured.setSerialNo(String.valueOf(beneficarySeq));
            antimoneyKeyiInsured.setSeqNum(beneficarySeq);
            antimoneyKeyiInsured.setFkPlcyNo(policyNo);
            antimoneyKeyiInsured.setIabiSeqNum(1);
            antimoneyKeyiInsured.setInsuredName(riskPersonDTO.getName());
            antimoneyKeyiInsured.setInsuredIdentifyCls1(CertificateTypeEnum.getGlobalType(riskPersonDTO.getCertificateType()));
            antimoneyKeyiInsured.setInsuredIdentifyNo(riskPersonDTO.getCertificateNo());
            antimoneyKeyiInsured.setRelationOfContractor(riskPersonDTO.getRelationshipWithApplicant());
            antimoneyKeyiInsured.setBenifitSeqNum(beneficarySeq);
            antimoneyKeyiInsured.setBenifitName(riskPersonDTO.getName());
            antimoneyKeyiInsured.setBenifitIdentifyCls1(CertificateTypeEnum.getGlobalType(riskPersonDTO.getCertificateType()));
            antimoneyKeyiInsured.setBenifitIdentifyNo(riskPersonDTO.getCertificateNo());
            antimoneyKeyiInsured.setInputClerk(baseInfo.getInputBy());
            antimoneyKeyiInsured.setLastUpdateClerk(baseInfo.getInputBy());
            insuredList.add(antimoneyKeyiInsured);
        } else {
            for (BeneficaryDTO beneficaryDTO : beneficaryInfoList) {
                AntimoneyKeyiInsured antimoneyKeyiInsured = new AntimoneyKeyiInsured();
                antimoneyKeyiInsured.setSerialNo(String.valueOf(beneficarySeq));
                antimoneyKeyiInsured.setSeqNum(beneficarySeq);
                antimoneyKeyiInsured.setFkPlcyNo(policyNo);
                antimoneyKeyiInsured.setIabiSeqNum(1);
                antimoneyKeyiInsured.setInsuredName(riskPersonDTO.getName());
                antimoneyKeyiInsured.setInsuredIdentifyCls1(CertificateTypeEnum.getGlobalType(riskPersonDTO.getCertificateType()));
                antimoneyKeyiInsured.setInsuredIdentifyNo(riskPersonDTO.getCertificateNo());
                antimoneyKeyiInsured.setRelationOfContractor(riskPersonDTO.getRelationshipWithApplicant());
                antimoneyKeyiInsured.setBenifitSeqNum(beneficarySeq);
                antimoneyKeyiInsured.setBenifitName(beneficaryDTO.getName());
                if (StringUtils.isNotEmpty(beneficaryDTO.getCertificateType())){
                    antimoneyKeyiInsured.setBenifitIdentifyCls1(CertificateTypeEnum.getGlobalType(beneficaryDTO.getCertificateType()));
                    antimoneyKeyiInsured.setBenifitIdentifyNo(beneficaryDTO.getCertificateNo());
                }
                antimoneyKeyiInsured.setInputClerk(baseInfo.getInputBy());
                antimoneyKeyiInsured.setLastUpdateClerk(baseInfo.getInputBy());
                insuredList.add(antimoneyKeyiInsured);
                beneficarySeq++;
            }
        }


        List<AntimoneyKeyiDetail> antimoneyKeyiDetailList = new ArrayList<>();
        AntimoneyKeyiDetail antimoneyKeyiDetail = new AntimoneyKeyiDetail();
        antimoneyKeyiDetail.setTransDate(transDate);
        antimoneyKeyiDetail.setBranchCd(AmlUtils.dptToBranchCd(departmentCode));
        antimoneyKeyiDetail.setSerialNo("1");
        antimoneyKeyiDetail.setSeqNum(1);
        // 投保单号
        antimoneyKeyiDetail.setQuotationNo(baseInfo.getApplyPolicyNo());
        antimoneyKeyiDetail.setFkPlcyNo(policyNo);
        if (StringUtils.isNotEmpty(baseInfo.getEndorseNo())){
            antimoneyKeyiDetail.setEventNo(baseInfo.getEndorseNo());
        }
        antimoneyKeyiDetail.setClaimNo(paymentTransAmlData.getCaseNo());
        antimoneyKeyiDetail.setPmtDtmnSer(paymentTransAmlData.getCaseTimes());
        antimoneyKeyiDetail.setPayDtmSeqNum(payDtmSeqNum);
        antimoneyKeyiDetail.setClientCd(paymentTransAmlData.getClientNo());
        antimoneyKeyiDetail.setClientName(paymentTransAmlData.getClientName());
        SaleDTO saleInfo = contractDTO.getSaleInfo();
        if (saleInfo!=null && StringUtils.isNotEmpty(saleInfo.getChannelSourceCode())){
            antimoneyKeyiDetail.setPartnerCd(saleInfo.getChannelSourceCode());
            antimoneyKeyiDetail.setPartnerName(saleInfo.getChannelSourceName());
        }
        antimoneyKeyiDetail.setTransAmount(paymentTransAmlData.getPaymentAmount());
        antimoneyKeyiDetail.setPlcyStartDate(DateUtils.dateFormat(baseInfo.getInsuranceBeginDate(),DateUtils.DATE_FORMAT_YYYYMMDD));
        antimoneyKeyiDetail.setPlcyEndDate(DateUtils.dateFormat(baseInfo.getInsuranceEndDate(),DateUtils.DATE_FORMAT_YYYYMMDD));
        // 反洗钱的险种代码 传 核心的产品编码
        antimoneyKeyiDetail.setProdCd(baseInfo.getProductCode());
        antimoneyKeyiDetail.setProdName(baseInfo.getProductName());
        antimoneyKeyiDetail.setInsuredAmount(BigDecimal.valueOf(baseInfo.getTotalInsuredAmount()));
        antimoneyKeyiDetail.setPremium(BigDecimal.valueOf(baseInfo.getTotalActualPremium()));
        // 缴费信息
        List<PayInfoDTO> payInfoList = contractDTO.getPayInfoList();
        if (CollectionUtils.isEmpty(payInfoList)){
            throw new GlobalBusinessException("保单缴费信息异常！");
        }
        // 缴费方式（-1-不定期；0-趸交；1-月缴；3-季缴；6-半年缴；12-年缴；）
        String installmentType = payInfoList.get(0).getInstallmentType();
        if ("0".equals(installmentType)){
            antimoneyKeyiDetail.setPremiumReceiveCd("02");
        }else if ("99".equals(installmentType)){
            antimoneyKeyiDetail.setPremiumReceiveCd("99-其他");
        } else {
            antimoneyKeyiDetail.setPremiumReceiveCd("01");
        }
        antimoneyKeyiDetail.setInsuredCount(applyRiskNum);
        // 投保人代码和名称 证件
        antimoneyKeyiDetail.setContractorCd(applicantDTO.getClientNo());
        antimoneyKeyiDetail.setContractorName(applicantDTO.getName());
        antimoneyKeyiDetail.setContractorIdentifyCls1(applicantDTO.getCertificateType());
        // businessType 业务类型：P-个险，G-团险
        String businessType = baseInfo.getBusinessType();
        antimoneyKeyiDetail.setContractorType(BUSINESS_TYPE_PERSON.equals(businessType) ? "07" : "02");
        antimoneyKeyiDetail.setContractorIdentifyNo(applicantDTO.getCertificateNo());
        antimoneyKeyiDetail.setTstm(DateUtils.dateFormat(new Date(),DateUtils.SIMPLE_DATE_STR));
        antimoneyKeyiDetail.setCaoi(paymentTransAmlData.getClientBankName());
        antimoneyKeyiDetail.setTcan(paymentTransAmlData.getClientBankAccount());
        antimoneyKeyiDetail.setInsuredList(insuredList);
        antimoneyKeyiDetailList.add(antimoneyKeyiDetail);
        AntimoneyKeyiTarget antimoneyKeyiTarget =new AntimoneyKeyiTarget();
        antimoneyKeyiTarget.setTransDate(transDate);
        antimoneyKeyiTarget.setBranchCd(AmlUtils.dptToBranchCd(departmentCode));
        antimoneyKeyiTarget.setContractorCd(applicantDTO.getClientNo());
        antimoneyKeyiTarget.setContractorName(applicantDTO.getName());
        antimoneyKeyiTarget.setAntimoneyCd(paymentTransAmlData.getAntimoneyCd());
        antimoneyKeyiTarget.setSenm(paymentTransAmlData.getClientName());
        antimoneyKeyiTarget.setSetp(CertificateTypeEnum.getGlobalType(paymentTransAmlData.getClientCertificateType()));
        antimoneyKeyiTarget.setSeid(paymentTransAmlData.getClientCertificateNo());
        antimoneyKeyiTarget.setSctl(paymentTransAmlData.getClientMobile());
        // 查询反洗钱表
        ClmsAntiMoneyLaunderingInfoDto dto = new ClmsAntiMoneyLaunderingInfoDto();
        dto.setReportNo(paymentTransAmlData.getReportNo());
        dto.setCaseTimes(paymentTransAmlData.getCaseTimes());
        dto.setCustomerNo(paymentTransAmlData.getClientNo());
        dto.setBankAccountAttribute(paymentTransAmlData.getBankAccountAttribute());
        ClmsAntiMoneyLaunderingInfoDto reultDto = clmsAmlInfoService.getClmsAntiMoneyLaunderingInfo(dto);
        if (reultDto!=null && (StringUtils.isNotEmpty(reultDto.getIdClmsAmlCompanyInfo()) || StringUtils.isNotEmpty(reultDto.getIdClmsAntiMoneyLaunderingInfo()))){
            if (Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),dto.getBankAccountAttribute())){
                // 公司账户
                antimoneyKeyiTarget.setSear(StringUtils.isEmptyStr(reultDto.getAmlAddress())?AML_DEFAULT_VALUE:reultDto.getAmlAddress());
                antimoneyKeyiTarget.setSrnm(StringUtils.isEmptyStr(reultDto.getLegalRepresentName())?AML_DEFAULT_VALUE:reultDto.getLegalRepresentName());
                antimoneyKeyiTarget.setSrit(StringUtils.isEmptyStr(reultDto.getLegalRepresentCardType())?AML_DEFAULT_VALUE:CertificateTypeEnum.getGlobalType(reultDto.getLegalRepresentCardType()));
                antimoneyKeyiTarget.setSrid(StringUtils.isEmptyStr(reultDto.getLegalRepresentCardNo())?AML_DEFAULT_VALUE:reultDto.getLegalRepresentCardNo());
            } else {
                //个人账户
                antimoneyKeyiTarget.setSear(StringUtils.isEmptyStr(reultDto.getAddress())?AML_DEFAULT_VALUE:reultDto.getAddress());
            }
        }
        antimoneyKeyiTarget.setAntimoneyKeyiDetailList(antimoneyKeyiDetailList);
        AntimoneyKeyiParam antimoneyKeyiParam = new AntimoneyKeyiParam();
        antimoneyKeyiParam.setAntimoneyKeyiTarget(antimoneyKeyiTarget);
        return antimoneyKeyiParam;
    }

}
