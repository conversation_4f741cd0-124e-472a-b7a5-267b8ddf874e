package com.paic.ncbs.claim.service.bpm.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.taskdeal.ClmsTaskConflictDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClaimQueryDataSourceService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.other.ConfigService;
import com.paic.ncbs.claim.service.report.ReportInfoExService;
import com.paic.ncbs.claim.service.taskdeal.TaskConflictService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import com.paic.ncbs.um.service.UserCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.BpmConstants.*;

@Slf4j
@RefreshScope
@Service("bpmService")
public class BpmServiceImpl implements BpmService {

    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private CacheService cacheService ;
    @Autowired
    private UserCommonService userCommonService ;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper ;
    @Autowired
    DepartmentDefineService departmentDefineService;
    @Autowired
    private UserInfoService userInfoService ;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private TaskConflictService taskConflictService;
    @Autowired
    private ClaimQueryDataSourceService claimQueryDataSourceService;
    @Autowired
    private ReportInfoExService reportInfoExService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private IOperationRecordService operationRecordService;
    @Value("${tpa.account}")
    private String tpaAccountId;
    @Value("${switch.dispatch:false}")
    private Boolean switchDispatch;

    @Override
    public void startProcess_oc(String reportNo, Integer caseTimes, String defKey) throws GlobalBusinessException {
        /*  delete by zjtang 注释旧流程冲突判断逻辑
        // 判断是否有其它在途任务 根据报案号查询是否有状态为0的待处理案件 如果则抛出异常
        String bpmKey = taskInfoMapper.getPendingTaskCount(reportNo, caseTimes);
        if (StringUtils.isNotEmpty(bpmKey)){
            throw new GlobalBusinessException("该案件还有其它待处理的任务！");
        }
         */
        this.startProcessOc(reportNo,caseTimes,defKey,null,null,null);

    }

    @Override
    public void startProcessOc(String reportNo, Integer caseTimes, String defKey, String taskId, String userCode, String departmentCode) throws GlobalBusinessException {
        log.info("启动工作流1-报案号：{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = getTaskInfoDTO(reportNo, caseTimes, defKey, taskId);
        dealAssignee(userCode,departmentCode, dto);
    }

    public TaskInfoDTO getTaskInfoDTO(String reportNo, Integer caseTimes, String defKey, String taskId) {
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        if (StringUtils.isEmptyStr(taskId)) {
            dto.setTaskId(UuidUtil.getUUID());
        } else {
            dto.setTaskId(taskId);
        }
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setAssigneeTime(new Date());
        dto.setStatus(BpmConstants.TASK_STATUS_PENDING);
        if (BpmConstants.OC_PAY_BACK_MODIFY.equals(defKey)){
            dto.setApplyer("SYSTEM");
            dto.setApplyerName("支付退回");
        } else if (BpmConstants.OC_FEE_INVOICE_MODIFY.equals(defKey)){
            dto.setApplyer("SYSTEM");
            dto.setApplyerName("费用发票退回");
        }else {
            dto.setApplyer(WebServletContext.getUserId());
            dto.setApplyerName(WebServletContext.getUserName());
            if(ConstValues.SYSTEM_UM.equals(dto.getApplyer())
                    && (BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT.equals(defKey) || BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS.equals(defKey))) {
                List<ReportInfoExEntity> reportInfoexs = reportInfoExService.getReportInfoEx(reportNo);
                ReportInfoExEntity reportInfoEx = reportInfoexs.get(0);
                TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerName(reportNo,caseTimes,BpmConstants.OC_REPORT_TRACK);
                if(reportInfoEx != null && "1".equals(reportInfoEx.getClaimDealWay()) ) {
                    dto.setApplyer(tdto.getAssigner());
                    dto.setApplyerName(tdto.getAssigneeName());
                }
            }
        }
        dto.setCreatedBy(ConstValues.SYSTEM);
        dto.setUpdatedBy(ConstValues.SYSTEM);
        return dto;
    }

    public void dealAssignee(String userCode, String departmentCode, TaskInfoDTO dto){
        // 沟通 是固定处理人
        //根据报案号查询保单机构
        String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(dto.getReportNo());
        if (StringUtils.isNotEmpty(userCode) || StringUtils.isNotEmpty(departmentCode)) {
            dto.setAssigner(userCode);
            dto.setDepartmentCode(departmentCode);
            dto.setAssigneeName(getUserName(userCode));
        } else if (BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS.equals(dto.getTaskDefinitionBpmKey())){
            // 暂时不存在无用户发起客户补材场景，先不考虑
            UserInfoDTO user  = WebServletContext.getUser();
            if (!ConstValues.SYSTEM_UM.equals(user.getUserCode())) {
                dto.setAssigner(user.getUserCode());
                dto.setAssigneeName(user.getUserName());
            }
            dto.setDepartmentCode(acceptDepartmentCode);
        } else if (BpmConstants.OC_FEE_INVOICE_MODIFY.equals(dto.getTaskDefinitionBpmKey())){
            //如果是收付费发票退回，案件分配至原案件理算岗 若原理算岗账号已不存在、或为system、或案件未经过理算，则将案件随机分配至案件归属地机构的理算岗
            TaskInfoDTO taskInfoDTO=  taskInfoMapper.getTaskAssignerName(dto.getReportNo(), dto.getCaseTimes(), BpmConstants.OC_MANUAL_SETTLE);
            UserInfoDTO user = new UserInfoDTO();
            if (taskInfoDTO == null || ConstValues.SYSTEM_UM.equalsIgnoreCase(taskInfoDTO.getAssigner())){
                user.setUserCode(ConstValues.SYSTEM_UM);
                user.setUserName(ConstValues.SYSTEM_UM);
            }else {
                user.setUserCode(taskInfoDTO.getAssigner());
                user.setUserName(taskInfoDTO.getAssigneeName());
            }
            this.autoDispatchToUserNew(dto,acceptDepartmentCode,user);
        } else if (checkBpmKey(dto.getTaskDefinitionBpmKey())){
            UserInfoDTO user = new UserInfoDTO();
            //判断是否是线上的单子：线上的单子WebServletContext.getUser()是取不到值的，线上的单子取上一个岗位的操作人
            if(claimQueryDataSourceService.isOnLineData(dto.getReportNo())){
                /**
                 * 线上报案的案子：生成下一个任务时，下一个任务的操作人先取上一个节点的操作人来判断是否有下一个岗位的权限，如果有就分配给他
                 * ：开启的是收单任务就查询报案跟踪的操作人，开启的是理算任务 就查询收单的操作人
                 */
                List<ReportInfoExEntity> reportInfoexs = reportInfoExService.getReportInfoEx(dto.getReportNo());
                String queryCondition="";
                if(Objects.equals(dto.getTaskDefinitionBpmKey(),BpmConstants.OC_CHECK_DUTY)){
                    queryCondition= BpmConstants.OC_REPORT_TRACK;
                    TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerName(dto.getReportNo(),dto.getCaseTimes(),queryCondition);
                    //如果在报案跟踪就分配给了TPA的 收单默认也分配给TPA
                    log.info("案件{}报案跟踪处理人：{}，配置TPA账号集：{}",dto.getReportNo(),tdto.getAssigner(),tpaAccountId);
                    List<String> accountList = Arrays.stream(tpaAccountId.split(",")).collect(Collectors.toList());

                    if(accountList.contains(tdto.getAssigner())
                            || accountList.contains(tdto.getAssigner().substring(0,3))
                            || "3".equals(reportInfoexs.get(0).getClaimDealWay())
                            || "AiModel".equals(reportInfoexs.get(0).getCompanyId())) {
                        dto.setAssigner(tdto.getAssigner());
                        dto.setAssigneeName(tdto.getAssigneeName());
                        dto.setDepartmentCode(acceptDepartmentCode);
                    }else{
                        user.setUserCode(tdto.getAssigner());
                        user.setUserName(tdto.getAssigneeName());
                        this.autoDispatchToUserNew(dto,acceptDepartmentCode,user);
                    }
                } else if (StringUtils.isEqualStr(dto.getTaskDefinitionBpmKey(), OC_MANUAL_SETTLE)
                        && CollectionUtils.isNotEmpty(reportInfoexs) && ("1".equals(reportInfoexs.get(0).getClaimDealWay()) || "3".equals(reportInfoexs.get(0).getClaimDealWay()))) {
                    queryCondition = BpmConstants.OC_CHECK_DUTY;
                    TaskInfoDTO tdto = taskInfoMapper.getTaskAssignerName(dto.getReportNo(), dto.getCaseTimes(), queryCondition);
                    // TPA全包理算节点和上个节点一致
                    dto.setAssigner(tdto.getAssigner());
                    dto.setAssigneeName(tdto.getAssigneeName());
                    dto.setDepartmentCode(acceptDepartmentCode);
                } else {
                    // 半包理算：默认分配给非TPA的用户
                    queryCondition = BpmConstants.OC_CHECK_DUTY;
                    TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerName(dto.getReportNo(),dto.getCaseTimes(),queryCondition);
                    user.setUserCode(tdto.getAssigner());
                    user.setUserName(tdto.getAssigneeName());
                    this.autoDispatchToUserNew(dto,acceptDepartmentCode,user);
                }

            }else{
                //线下
                if(StringUtils.isEqualStr(dto.getTaskDefinitionBpmKey(), BpmConstants.OC_CHECK_DUTY)){//线下收单可能立案审批通过直接收单
                    TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerName(dto.getReportNo(),dto.getCaseTimes(),BpmConstants.OC_REPORT_TRACK);
                    user.setUserCode(tdto.getAssigner());
                    user.setUserName(tdto.getAssigneeName());
                }else{
                    user = WebServletContext.getUser();
                }
                this.autoDispatchToUserNew(dto,acceptDepartmentCode,user);
            }

        }else if(OC_HUMAN_INJURY_TRACKING.equals(dto.getTaskDefinitionBpmKey())){
            //人伤跟踪处理人逻辑处理
            //1 自动人伤，与报案跟踪处理人一致
            //2 手动人伤，与当前登录人一致（发起该流程的主流程节点的处理人）
            TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerNameNew(dto.getReportNo(),dto.getCaseTimes(),BpmConstants.OC_REPORT_TRACK);
            if(tdto == null){
                tdto = taskInfoMapper.getTaskAssignerNameNew(dto.getReportNo(),dto.getCaseTimes(), BpmConstants.OC_CHECK_DUTY);
            }
            if(tdto != null) {
                dto.setAssigner(tdto.getAssigner());
                dto.setAssigneeName(tdto.getAssigneeName());
                dto.setDepartmentCode(acceptDepartmentCode);
            }
        } else {
            dto.setAssigner("");
            dto.setDepartmentCode(acceptDepartmentCode);
        }
        taskInfoService.addTaskInfo(dto);
        if(StringUtils.isNotEmpty(dto.getAssigner())) {
            operationRecordService.insertDispatchRecord(dto.getReportNo(), dto.getTaskDefinitionBpmKey(), true, dto.getAssigner(), ConstValues.SYSTEM_UM);
        }
    }

    private boolean checkBpmKey(String bpmKey) {
        return OC_REPORT_TRACK.equals(bpmKey) || BpmConstants.OC_CHECK_DUTY.equals(bpmKey) || BpmConstants.OC_MANUAL_SETTLE.equals(bpmKey);
    }


    /**
     * （1）	报案人有权限，调度到报案人名下；
     * （2）	报案人无权限，
     *          报案跟踪 按保单归属机构逐级向上分配调度直至总公司。若被调度机构不止1名报案跟踪权限人员，按人员名字首字母顺序轮询调度。
     *          其余流程直接放到案件池
     *
     */
    private void autoDispatchToUser(TaskInfoDTO dto, String acceptDepartmentCode,UserInfoDTO user) {
//        UserInfoDTO user = WebServletContext.getUser();
        String bpmKey = dto.getTaskDefinitionBpmKey();
        List<String> gradeNames = new ArrayList<>();
        try {
            log.info("autoDispatchToUser-用户-={}", user.getUserCode());
            log.info("autoDispatchToUser-报案环节-={}", dto.getReportNo() + ":" + dto.getTaskDefinitionBpmKey());
            log.info("autoDispatchToUser-保单机构-={}", acceptDepartmentCode);
            // 获取当前用户拥有的 角色 集合
            List<UserGradeInfoDTO> userGradeInfos = cacheService.queryUserGradeList(user.getUserCode(), acceptDepartmentCode);
            gradeNames = userGradeInfos.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取岗位信息异常", e);
        }
        String gradeName = NcbsConstant.GRADE_MAP.get(bpmKey);
        if (!gradeNames.isEmpty() && CollectionUtils.containsAny(gradeNames, Arrays.asList(gradeName.split(",")))) {
            dto.setAssigner(user.getUserCode());
            dto.setAssigneeName(user.getUserName());
            dto.setDepartmentCode(acceptDepartmentCode);
        } else {
            if (OC_REPORT_TRACK.equals(bpmKey) || OC_FEE_INVOICE_MODIFY.equals(bpmKey)) {
                // 报案跟踪或费用修改 走自动调度流程
                initDepartCodeAndAssigner(dto, gradeName, acceptDepartmentCode);
            } else {
                if(OC_MANUAL_SETTLE.equals(bpmKey)){
                    //查询一下 看收单的assigner是不是TPA的人，如果是TPA的人
                    List<String> accountList = Arrays.stream(tpaAccountId.split(",")).collect(Collectors.toList());
                    TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerName(dto.getReportNo(),dto.getCaseTimes(),BpmConstants.OC_CHECK_DUTY);
                    if(accountList.contains(tdto.getAssigner()) || accountList.contains(tdto.getAssigner().substring(0,3))){
                        //TPA的数据默认分配时排查TPA的人
                        initDepartCodeAndAssigner(dto, gradeName, acceptDepartmentCode);
                    }else{
                      //非tPA的数据 默认放到 案件池
                        dto.setAssigner("");
                        dto.setDepartmentCode(acceptDepartmentCode);
                    }
                }else{
                    // 其它环节如果本人没有权限则 放到案件池
                    dto.setAssigner("");
                    dto.setDepartmentCode(acceptDepartmentCode);
                }

            }
        }
        //发送自动调度邮件
//        sendDispatchCaseMail(dto.getReportNo(),dto.getAssigner());
    }

    private void initDepartCodeAndAssigner(TaskInfoDTO dto, String gradeName, String acceptDepartmentCode) {
        List<String> gradeCodeList = new ArrayList<>();
        dto.setAssigner("");
        dto.setDepartmentCode(acceptDepartmentCode);
        try {
            List<UserGradeInfoDTO> userGradeInfoDTOS = cacheService.querySystemGradeList();
            if (ListUtils.isNotEmpty(userGradeInfoDTOS)) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(gradeName)) {
                    List<String> gradeList = Arrays.asList(gradeName.split(","));
                    for (UserGradeInfoDTO gradeInfo:userGradeInfoDTOS) {
                        for (String grade : gradeList) {
                            if (gradeInfo.getGradeName().equals(grade)){
                                gradeCodeList.add(gradeInfo.getGradeCode());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("initDepartCodeAndAssigner-获取角色列表异常",e);
            return;
        }
        if (ListUtils.isNotEmpty(gradeCodeList)){
            return;
        }
        Set<String> userCodeAll = new HashSet<>();
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setComCode(acceptDepartmentCode);
        for (String gradeCode : gradeCodeList) {
            // 递归 根据机构号和岗位号获取人员userCode
            List<String> userCodes = this.getUserBycomCodeAndGradeCode(userInfoDTO, gradeCode);
            if (ListUtils.isNotEmpty(userCodes)) {
                userCodeAll.addAll(userCodes);
            }
        }
        LogUtil.audit("根据机构号和岗位号获取userCode:{}",JSON.toJSONString(userCodeAll));
        if (userCodeAll.isEmpty()) {
            //如果一直递归到总公司 还是没有找到该岗位的
            dto.setDepartmentCode(ConfigConstValues.HQ_DEPARTMENT);
            return;
        }
        List<String> userCodes = new ArrayList<>(userCodeAll);
        SecureRandom random = new SecureRandom();
        int i = random.nextInt(userCodes.size());
        String userCode = userCodes.get(i);
        dto.setAssigner(userCode);
        dto.setAssigneeName(getUserName(userCode));
        dto.setDepartmentCode(userInfoDTO.getComCode());
        LogUtil.audit("根据机构号和岗位号获取处理人：{}",JSON.toJSONString(dto));
    }

    public void autoDispatchToUserNew(TaskInfoDTO dto, String acceptDepartmentCode,UserInfoDTO user) {
        if(!switchDispatch){
            LogUtil.audit("新派工规则未打开,走旧派工规则！");
            autoDispatchToUser(dto,acceptDepartmentCode,user);
            return;
        }

        String bpmKey = dto.getTaskDefinitionBpmKey();
        String gradeName = NcbsConstant.GRADE_MAP.get(bpmKey);
        List<String> gradeNames = new ArrayList<>();
        if (!(OC_REPORT_TRACK.equals(bpmKey) || ConstValues.SYSTEM_UM.equals(user.getUserCode()) && Arrays.asList(OC_FEE_INVOICE_MODIFY, OC_MANUAL_SETTLE).contains(bpmKey))) {
            // 报案跟踪不走这个规则
            try {
                log.info("autoDispatchToUser-用户-={}", user.getUserCode());
                log.info("autoDispatchToUser-报案环节-={}", dto.getReportNo() + ":" + dto.getTaskDefinitionBpmKey());
                log.info("autoDispatchToUser-保单机构-={}", acceptDepartmentCode);
                if(StringUtils.isNotEmpty(user.getUserCode())) {
                    // 获取当前用户拥有的 角色 集合
                    List<UserGradeInfoDTO> userGradeInfos = cacheService.queryUserGradeList(user.getUserCode(), acceptDepartmentCode);
                    gradeNames = userGradeInfos.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toList());
                }
            } catch (Exception e) {
                log.error("获取岗位信息异常", e);
            }

            if (!gradeNames.isEmpty() && CollectionUtils.containsAny(gradeNames, Arrays.asList(gradeName.split(",")))) {
                LogUtil.audit("提交人：{}，有权限：{}直接分配！",user.getUserCode(),gradeName);
                dto.setAssigner(user.getUserCode());
                dto.setAssigneeName(user.getUserName());
                dto.setDepartmentCode(acceptDepartmentCode);
                return;
            }
        }

        if (OC_REPORT_TRACK.equals(bpmKey)) {
            // 报案跟踪走新地分配逻辑
            autoDispatchRuleToUser(dto);
        } else if (OC_MANUAL_SETTLE.equals(bpmKey)) {
            // 查询一下 看收单的assigner是不是TPA的人，如果是TPA的人
            List<String> accountList = Arrays.stream(tpaAccountId.split(",")).collect(Collectors.toList());
            TaskInfoDTO tdto = taskInfoMapper.getTaskAssignerName(dto.getReportNo(), dto.getCaseTimes(),
                    BpmConstants.OC_CHECK_DUTY);
            List<ReportInfoExEntity> reportInfoexs = reportInfoExService.getReportInfoEx(dto.getReportNo());
            if (StringUtils.isNotEmpty(tdto.getAssigner()) && (accountList.contains(tdto.getAssigner()) || accountList.contains(tdto.getAssigner().substring(0, 3)))) {
                // TPA的数据默认分配时排查TPA的人
                autoDispatchRuleToUser(dto);
            }else if(CollectionUtils.isNotEmpty(reportInfoexs) && "AiModel".equals(reportInfoexs.get(0).getCompanyId())){
                autoDispatchRuleToUser(dto);
            } else {
                // 非tPA的数据 默认放到 案件池
                dto.setAssigner("");
                dto.setDepartmentCode(acceptDepartmentCode);
            }

        } else if (OC_FEE_INVOICE_MODIFY.equals(bpmKey)) {
            // 先不改走老逻辑
            initDepartCodeAndAssigner(dto, gradeName, acceptDepartmentCode);
        } else {
            // 其它环节如果本人没有权限则 放到案件池
            dto.setAssigner("");
            dto.setDepartmentCode(acceptDepartmentCode);
        }
        //发送自动调度邮件
        //sendDispatchCaseMail(dto.getReportNo(),dto.getAssigner());
    }

    public void autoDispatchRuleToUser(TaskInfoDTO dto) {

        List<AhcsPolicyInfoEntity> policyInfoEntities = ahcsPolicyInfoMapper.selectByReportNo(dto.getReportNo());
        AhcsPolicyInfoEntity policyInfoEntity = policyInfoEntities.get(0);
        /* 维度机构、利润中心、产品代码、方案代码 */
        String departmentCode = policyInfoEntity.getDepartmentCode();
        String profitCenter = StringUtils.cancelNull(policyInfoEntity.getProfitCenter());
        String productCode =StringUtils.cancelNull(policyInfoEntity.getProductCode());
        String productGroup = StringUtils.cancelNull(policyInfoEntity.getProductPackageType());
        // 派工组ID
        String dispatchCode = null;
        List<Integer> departmentCodeList = Arrays.asList(Integer.valueOf(departmentCode),
                Integer.valueOf(ConfigConstValues.HQ_DEPARTMENT));
        for (Integer item : departmentCodeList) {
            dispatchCode = configService.getDispatchConfigPriority(item, profitCenter, productCode, productGroup);
            if(StringUtils.isNotEmpty(dispatchCode)){
                // 先机构、后总公司，第一次命中后返回 结束循环
                break;
            }
        }

        // 第一次获取到
        List<UserInfoDTO> userInfoDTOS = null;
        if(StringUtils.isNotEmpty(dispatchCode)){
            userInfoDTOS = configService.getGroupUser(dispatchCode);
        }

        if (CollectionUtils.isEmpty(userInfoDTOS)) {
            // 上一步可能有用户组或者用户失效的情况,再走一遍总公司补㑽
            LogUtil.audit("再次执行总公司规则补偿!");
            dispatchCode = configService.getDispatchConfigPriority(Integer.valueOf(ConfigConstValues.HQ_DEPARTMENT),
                    profitCenter,productCode,
                    productGroup);
            userInfoDTOS = configService.getGroupUser(dispatchCode);
        }
        LogUtil.audit("最终分配用户：{}!",JSON.toJSONString(userInfoDTOS));
        if (CollectionUtils.isNotEmpty(userInfoDTOS)) {
            SecureRandom random = new SecureRandom();
            int i = random.nextInt(userInfoDTOS.size());
            String userCode = userInfoDTOS.get(i).getUserCode();
            String userName = userInfoDTOS.get(i).getUserName();
            dto.setAssigner(userCode);
            dto.setAssigneeName(userName);
            dto.setDepartmentCode(departmentCode);
        } else {
            // 其它环节如果本人没有权限则 放到案件池
            dto.setAssigner("");
            dto.setDepartmentCode(departmentCode);
        }
    }

    /**
     *
     * 递归 根据机构号和岗位号获取人员userCode
     * @return userCodes
     */
    private List<String> getUserBycomCodeAndGradeCode(UserInfoDTO userInfoDTO,String gradeCode){
        List<String> userCodes = new ArrayList<>();
        try {
            LogUtil.audit("获取处理人入参：userInfoDTO:{},gradeCode:{}", JSON.toJSONString(userInfoDTO),gradeCode);
            List<UserInfoDTO> userInfoDTOS = userCommonService.queryUserInfoList(userInfoDTO.getComCode(), gradeCode);
            LogUtil.audit("获取处理人集合：userInfoDTOS:{}", JSON.toJSONString(userInfoDTOS));
            if (CollectionUtils.isNotEmpty(userInfoDTOS)){
                userCodes = userInfoDTOS.stream().map(UserInfoDTO::getUserCode).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
        }
        if (userCodes.isEmpty()) {
            //因为顶级机构的上级机构是其本身 无法递归终止 要排除
            if (userInfoDTO.getComCode() != null && !ConfigConstValues.HQ_DEPARTMENT.equals(userInfoDTO.getComCode())){
                // 获取上级机构
                DepartmentDTO parentDep = departmentDefineService.queryParentDepartmentInfoByDeptCode(userInfoDTO.getComCode());
                userInfoDTO.setComCode(parentDep.getDepartmentCode());
                userCodes = getUserBycomCodeAndGradeCode(userInfoDTO,gradeCode);
            }
        }
        List<String> accountList = Arrays.stream(tpaAccountId.split(",")).collect(Collectors.toList());
        return userCodes.stream().filter(userCode->!(accountList.contains(userCode) || accountList.contains(userCode.substring(0,3)))).collect(Collectors.toList());
    }

    @Override
    public void completeTask_oc(String reportNo, Integer caseTimes, String defKey) throws GlobalBusinessException {
        if (!taskInfoService.hasNotFinishTaskByTaskKey(reportNo,caseTimes,defKey,null) ){
            throw new GlobalBusinessException("任务已完成，请勿重复提交！");
        }
        log.info("完成工作流-报案号：{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setStatus(BpmConstants.TASK_STATUS_COMPLETED);
        taskInfoService.modifyTaskInfoByDefKey(dto);

    }

    /**
     * 客户补材状态回调接口专用
     * @param reportNo
     * @param caseTimes
     * @param defKey
     * @throws GlobalBusinessException
     */
    @Override
    public void completeTask_oc_css(String reportNo, Integer caseTimes, String defKey) throws GlobalBusinessException {
        log.info("完成工作流-报案号：{}，任务节点：{}",reportNo,defKey);
        if (!taskInfoService.hasNotFinishTaskByTaskKey(reportNo,caseTimes,defKey,null) ){
            throw new GlobalBusinessException("任务已完成，请勿重复提交！");
        }

        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy("SYSTEM");
        dto.setStatus(BpmConstants.TASK_STATUS_COMPLETED);
        taskInfoService.modifyTaskInfoByDefKey(dto);

    }

    /**
     * 支付修改专用 根据 taskId complete任务
     */
    @Override
    public void completeTask_oc(String reportNo, Integer caseTimes, String defKey, String taskId) {
        if (!taskInfoService.hasNotFinishTaskByTaskKey(reportNo,caseTimes,defKey,taskId) ){
            throw new GlobalBusinessException("任务已完成，请勿重复提交！");
        }
        log.info("完成工作流-报案号：{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setStatus(BpmConstants.TASK_STATUS_COMPLETED);
        dto.setTaskId(taskId);
        taskInfoService.modifyTaskInfoByDefKey(dto);

    }

    /**
     * 挂起/重启流程
     * @param reportNo
     * @param caseTimes
     * @param defKey
     * @param flag true：挂起，false：重启
     */
    @Override
    public void suspendOrActiveTask_oc(String reportNo, Integer caseTimes,String defKey,boolean flag){
        /*delete by hanyfeng 取消挂起/重启流程
        log.info(flag?"暂停":"重启" + "工作流，报案号{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setStatus(flag ? BpmConstants.TASK_STATUS_SUSPEND : BpmConstants.TASK_STATUS_PENDING);
        taskInfoService.suspendOrActiveTask(dto);
        */

    }

    /**
     * 挂起流程(零注/拒赔使用)
     * @param reportNo
     * @param caseTimes
     * @param defKey
     */
    @Override
    public void newSuspendOrActiveTask_oc(String reportNo, Integer caseTimes,String defKey,boolean flag){
        log.info(flag?"暂停":"重启" + "工作流，报案号{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setStatus(flag ? BpmConstants.TASK_STATUS_SUSPEND : BpmConstants.TASK_STATUS_PENDING);
        taskInfoService.suspendOrActiveTask(dto);
    }

    /**
     * 客户补材状态回调接口专用
     * @param reportNo
     * @param caseTimes
     * @param defKey
     * @param flag
     */
    @Override
    public void suspendOrActiveTask_oc_css(String reportNo, Integer caseTimes,String defKey,boolean flag){
        /* delete by zjtang 取消挂起/重启流程
        log.info(flag?"暂停":"重启" + "工作流，报案号{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();

        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy("SYSTEM");
        dto.setStatus(flag ? BpmConstants.TASK_STATUS_SUSPEND : BpmConstants.TASK_STATUS_PENDING);
        taskInfoService.suspendOrActiveTask(dto);
         */

    }

    /**
     * 支付修改专用 根据 taskId 重启任务
     * 挂起/重启流程
     * @param reportNo
     * @param caseTimes
     * @param defKey
     * @param flag true：挂起，false：重启
     */
    @Override
    public void suspendOrActiveTask_oc(String reportNo, Integer caseTimes, String defKey, boolean flag, String taskId){
        log.info(flag?"暂停":"重启" + "工作流，报案号{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setStatus(flag ? BpmConstants.TASK_STATUS_SUSPEND : BpmConstants.TASK_STATUS_PENDING);
        dto.setTaskId(taskId);
        if (flag){
            taskInfoService.suspendOrActiveTask(dto);
        }else{
            taskInfoService.suspendNotUpdateDate(dto);
        }
    }

    /**
     *
     * @Description 调查专用 指定部门
     * <AUTHOR>
     * @Date 2023/8/11 18:19
     **/
    @Override
    public void startProcess_oc(String reportNo, Integer caseTimes, String defKey, String taskId, String depentmentCode, String initMode) throws GlobalBusinessException {
        log.info("启动工作流2-报案号：{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        if (StringUtils.isEmptyStr(taskId)) {
            dto.setTaskId(UuidUtil.getUUID());
        } else {
            dto.setTaskId(taskId);
        }
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setAssigneeTime(new Date());
        dto.setStatus(BpmConstants.TASK_STATUS_PENDING);
        dto.setCreatedBy(ConstValues.SYSTEM);
        dto.setUpdatedBy(ConstValues.SYSTEM);
        dto.setAssigner("");
        dto.setApplyer(WebServletContext.getUserId());
        dto.setApplyerName(WebServletContext.getUserName());
        if (BaseConstant.STRING_02.equals(initMode)){
            // 调查机构位外部时，机构设为总部
            dto.setDepartmentCode(ConfigConstValues.HQ_DEPARTMENT);
        } else {
            // 调查机构位内部
            dto.setDepartmentCode(depentmentCode);
        }
//        if ("stg2".equals(env)){
//            if (BpmConstants.APPROVETASK_CONTROL.contains(dto.getTaskDefinitionBpmKey())){
//                dto.setAssigner("HY1991");
//                dto.setAssigneeName("HY1991");
//                dto.setDepartmentCode(ConfigConstValues.HQ_DEPARTMENT);
//            }else {
//                dto.setAssigner("TEST");
//                dto.setAssigneeName("TEST");
//                dto.setDepartmentCode(ConfigConstValues.HQ_DEPARTMENT);
//            }
//        }
        taskInfoService.addTaskInfo(dto);
    }

    /**
     *
     * @Description 委托专用
     **/
    @Override
    public void startProcessEntrustment(String reportNo, Integer caseTimes, String defKey, String taskId) throws GlobalBusinessException {
        log.info("启动委托工作流-报案号：{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        if (StringUtils.isEmptyStr(taskId)) {
            dto.setTaskId(UuidUtil.getUUID());
        } else {
            dto.setTaskId(taskId);
        }
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setAssigneeTime(new Date());
        dto.setStatus(BpmConstants.TASK_STATUS_PENDING);
        dto.setCreatedBy(ConstValues.SYSTEM);
        dto.setUpdatedBy(ConstValues.SYSTEM);
        dto.setAssigner("");
        dto.setApplyer(WebServletContext.getUserId());
        dto.setApplyerName(WebServletContext.getUserName());
        taskInfoService.addTaskInfo(dto);
    }

    @Override
    public boolean isExistSuspendTask(String reportNo, Integer caseTimes,String defKey){
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        int count = taskInfoService.getSuspendTaskCount(dto);
        if (count == 1){
            return true;
        }else if (count == 0){
            return false;
        }
        else {
            log.info("案件["+reportNo+"]存在["+count+"]条挂起流程，请检查");
            throw new GlobalBusinessException("","当前案件挂起任务数量异常！");
        }

    }

    @Override
    public void startProcessOcBatch(String reportNo, String userId) {
        log.info("启动批量报案-报案号：{}，任务节点：{}",reportNo);
        String departCode  = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(1);
        dto.setTaskId(UuidUtil.getUUID());
        dto.setTaskDefinitionBpmKey(OC_REPORT_TRACK);
        dto.setAssigneeTime(new Date());
        dto.setStatus(BpmConstants.TASK_STATUS_PENDING);
        dto.setCreatedBy(ConstValues.SYSTEM);
        dto.setUpdatedBy(ConstValues.SYSTEM);
        UserInfoDTO user = new UserInfoDTO();
        user.setUserCode(userId);
        String name = userInfoService.getUserNameById(userId);
        user.setUserName(name);
        this.autoDispatchToUserNew(dto,departCode,user);
        dto.setApplyer(userId);
        dto.setApplyerName(name);
        taskInfoService.addTaskInfo(dto);
    }

//    private void sendDispatchCaseMail(String reportNo,String receiver){
//        if(StringUtils.isEmptyStr(receiver)){
//            return;
//        }
//
//        mailSendService.sendCaseMail(reportNo,receiver);
//    }

    private String getUserName(String userCode){
        if (BpmConstants.INVESTIGATE_PLATFORM.equals(userCode)) {
            return INVESTIGATE_PLATFORM_NAME;
        }

        if (StringUtils.isNotEmpty(userCode) ){
            try {
                UserInfoDTO userInfo = cacheService.queryUserInfo(userCode);
                if (userInfo != null){
                    return userInfo.getUserName();
                }
            }catch (NcbsException e){
                log.info("调用[根据用户编码查询用户信息]接口异常!");
            }
        }
        return "";
    }

    @Override
    public void startProcessTelReport(String reportNo, Integer caseTimes, String defKey, String departmentCode) throws GlobalBusinessException {
        TaskInfoDTO dto = getTaskInfoTelReport(reportNo, caseTimes, defKey);
        dealAssigneeTelReport(departmentCode, dto);
    }

    /**
     * 专门提供给案件重开流程使用
     * @param reportNo
     * @param caseTimes
     * @param isRestartReport
     * @param bpmKey
     */
    @Override
    public void startProcess_rc(String reportNo, Integer caseTimes, boolean isRestartReport, String bpmKey,String taskId) {
        log.info("启动工作流2-报案号：{}，赔付次数：{}",reportNo,caseTimes);
        TaskInfoDTO dto = new TaskInfoDTO();
        String assigner = "";
        String assigneeName = "";
        String departmentCode = "";
        if (isRestartReport) {
            // 重开时，获取重开申请人信息
            TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes,
                    BpmConstants.OC_RESTART_CASE_APPROVAL);
            if (taskInfoDTO != null){
                assigner = taskInfoDTO.getApplyer();
                assigneeName = taskInfoDTO.getApplyerName();
                departmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);

            }
        } else {
            // 支付修改时，获取原理算流程处理人信息
            TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes,
                    BpmConstants.OC_MANUAL_SETTLE);
            if (taskInfoDTO != null){
                assigner = taskInfoDTO.getAssigner();
                assigneeName = taskInfoDTO.getAssigneeName();
                departmentCode = taskInfoDTO.getDepartmentCode();
            }
        }
        //获取原处理人的角色信息
        List<String> gradeNames = new ArrayList<>();
        if(StringUtils.isNotEmpty(assigner)) {
            try {
                // 获取当前用户拥有的 角色 集合
                List<UserGradeInfoDTO> userGradeInfos = cacheService.queryUserGradeList(assigner, departmentCode);
                if (null != userGradeInfos) {
                    gradeNames = userGradeInfos.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toList());
                }
            } catch (Exception e) {
                log.error("获取岗位信息异常", e);
            }
        }
        String gradeName = NcbsConstant.GRADE_MAP.get(BpmConstants.OC_MANUAL_SETTLE);
        if (!gradeNames.isEmpty() && CollectionUtils.containsAny(gradeNames, Arrays.asList(gradeName.split(",")))) {
            dto.setAssigner(assigner);
            dto.setAssigneeName(assigneeName);
            dto.setDepartmentCode(departmentCode);
        } else {
            String departCode  = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
            // 复用报案跟踪，走自动调度流程
            initDepartCodeAndAssigner(dto, gradeName, departCode);
        }
        if (BpmConstants.OC_PAY_BACK_MODIFY.equals(bpmKey)){
            dto.setApplyer("SYSTEM");
            dto.setApplyerName("支付退回");
            dto.setCreatedBy(ConstValues.SYSTEM);
            dto.setUpdatedBy(ConstValues.SYSTEM);
        } else {
            dto.setApplyer(WebServletContext.getUserId());
            dto.setApplyerName(WebServletContext.getUserName());
            dto.setCreatedBy(WebServletContext.getUserId());
            dto.setUpdatedBy(WebServletContext.getUserId());
        }
        dto.setReportNo(reportNo);
        if (isRestartReport){
            if(StringUtils.isNotEmpty(bpmKey)){
                dto.setCaseTimes(caseTimes);
                dto.setTaskDefinitionBpmKey(bpmKey);
            } else {
                //案件重启通过的情况下，赔付次数要加1
                dto.setCaseTimes(caseTimes+1);
                dto.setTaskDefinitionBpmKey(BpmConstants.OC_MANUAL_SETTLE);
            }
        } else {
            dto.setCaseTimes(caseTimes);
            dto.setTaskDefinitionBpmKey(bpmKey);
        }
        if (StringUtils.isEmptyStr(taskId)){
            dto.setTaskId(UuidUtil.getUUID());
        } else {
            dto.setTaskId(taskId);
        }
        dto.setAssigneeTime(new Date());
        dto.setStatus(BpmConstants.TASK_STATUS_PENDING);
        taskInfoService.addTaskInfo(dto);
    }

    public TaskInfoDTO getTaskInfoTelReport(String reportNo, Integer caseTimes, String defKey) {
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskId(UuidUtil.getUUID());
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setAssigneeTime(new Date());
        dto.setStatus(BpmConstants.TASK_STATUS_PENDING);
        dto.setApplyer("SYSTEM");
        dto.setApplyerName("SYSTEM");
        dto.setCreatedBy(ConstValues.SYSTEM);
        dto.setUpdatedBy(ConstValues.SYSTEM);
        return dto;
    }

    public void dealAssigneeTelReport( String departmentCode, TaskInfoDTO dto){
        if(!switchDispatch){
            LogUtil.audit("新派工规则未打开,走旧派工规则！");
            this.initDepartCodeAndAssigner(dto, NcbsConstant.GRADE_MAP.get(OC_REPORT_TRACK), departmentCode);
        } else {
            this.autoDispatchRuleToUser(dto);
        }
        taskInfoService.addTaskInfo(dto);
    }

    /**
     * 重启理算
     * @param reportNo
     * @param caseTimes
     * @param defKey
     * @param flag true：挂起，false：重启
     */
    @Override
    public void activeManualSettle(String reportNo, Integer caseTimes,String defKey,boolean flag){
//        log.info(flag?"暂停":"重启" + "工作流，报案号{}，任务节点：{}",reportNo,defKey);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setTaskDefinitionBpmKey(defKey);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setStatus("0");
//        dto.setStatus(flag ? BpmConstants.TASK_STATUS_SUSPEND : BpmConstants.TASK_STATUS_PENDING);
        //微保的案件退回理算走自动分配
        List<ReportInfoExEntity> reportInfoexs = reportInfoExService.getReportInfoEx(dto.getReportNo());
        if (BpmConstants.OC_MANUAL_SETTLE.equals(defKey) && CollectionUtils.isNotEmpty(reportInfoexs) &&
                (("1".equals(reportInfoexs.get(0).getClaimDealWay()) && "channel".equals(reportInfoexs.get(0).getCompanyId()))
                        || "3".equals(reportInfoexs.get(0).getClaimDealWay()))) {
            TaskInfoDTO tdto = taskInfoMapper.getTaskAssignerName(dto.getReportNo(), dto.getCaseTimes(), BpmConstants.OC_REPORT_TRACK);
            if ("TencentHC".equals(tdto.getAssigner()) || Constants.SYSTEM_USER.equals(tdto.getAssigner())) {
                String departmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
                this.autoDispatchToUserNew(dto, departmentCode, WebServletContext.getUser());
            }
        }
        taskInfoService.suspendOrActiveTask(dto);
    }

    @Override
    public void processCheck(String reportNo, String bpmKey, String operateType){
        log.info("报案号{}，任务节点：{}，操作{}校验。",reportNo,bpmKey,operateType);

        //根据任务节点和操作类型查询互斥规则
        List<ClmsTaskConflictDTO> confilictList = taskConflictService.findConflictByBpmKeyAndOpr(bpmKey,operateType);
        StringBuffer reasons = new StringBuffer();
        if(CollectionUtils.isNotEmpty(confilictList)) {
            for (int i = 0; i < confilictList.size(); i++) {
                String confilictKey = confilictList.get(i).getConflictTaskKey();

                List<TaskInfoDTO> taskinfos = taskInfoMapper.findTaskByReportNoAndBpmKey(reportNo,confilictKey);
                if(taskinfos != null && taskinfos.size()>0){
                    reasons.append(confilictList.get(i).getConflictReason());
                    if(reasons.length() > 0 && i < confilictList.size()-1){
                        reasons.append("\n");
                    }
                }
            }
        }
        log.info(reasons.toString());
        if(reasons.length()>0){
            throw new GlobalBusinessException(reasons.toString());
        }
    }

    @Override
    public void closeAll_oc(String reportNo, Integer caseTimes) {
        log.info("案件结案，所有节点状态设置为已处理-报案号：{}",reportNo);
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setStatus(BpmConstants.TASK_STATUS_COMPLETED);
        taskInfoService.modifyTaskInfoByDefKey(dto);
    }
}
