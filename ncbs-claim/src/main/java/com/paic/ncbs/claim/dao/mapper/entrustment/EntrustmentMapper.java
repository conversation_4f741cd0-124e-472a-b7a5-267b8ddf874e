package com.paic.ncbs.claim.dao.mapper.entrustment;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface EntrustmentMapper extends BaseDao {

    void insertEntrustment(EntrustmentDTO entrustment);

    void updateEntrustment(EntrustmentDTO entrustment);

    EntrustmentDTO getEntrustmentData(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    // 删除草稿任务
    void deleteEntrustmentNoOperate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    // 原有方法保留
    EntrustmentDTO selectByReportNoAndCaseTime(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<EntrustmentDTO> selectHistoryByReportNo(@Param("reportNo") String reportNo);

    List<EntrustmentDTO> selectPendingApprovalList(@Param("approverUm") String approverUm);

    EntrustmentDTO selectById(@Param("idAhcsEntrustment") String idAhcsEntrustment);

    List<EntrustmentDTO> selectForPrint(@Param("reportNo") String reportNo);

    List<EntrustmentDTO> selectUnapprovedEntrustments(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
    
    /**
     * 获取未提交的委托数据
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托数据
     */
    EntrustmentDTO getNoCommitData(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}