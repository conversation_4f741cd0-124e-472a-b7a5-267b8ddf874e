package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyChargeVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 追偿费用信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@MapperScan
public interface ClmsReplevyChargeMapper extends BaseDao<ClmsReplevyCharge> {
    /**
     * 查询追偿费用信息
     * @return
     */
   List<ClmsReplevyChargeVo> selectClmsReplevyCharge(@Param("reportNo") String reportNo,@Param("replevyNo") String replevyNo);

   int insert(ClmsReplevyCharge clmsReplevyCharge);

   ClmsReplevyCharge selectById(@Param("id") String id);
   /**
     * 更新数据
     * @param clmsReplevyCharge
     * @return
     */
   int updateSelectiveByPrimaryKey(ClmsReplevyCharge clmsReplevyCharge);

    /**
     * 根据idClmPaymentItem查询追偿费用信息
     * @param idClmPaymentItem 支付项Id
     * @return
     */
   ClmsReplevyCharge getReplevyChargeByIdClmPaymentItem(String idClmPaymentItem);
   /**
     * 根据reportNo查询最大serialNo
     * @param reportNo 报案号
     * @return
     */
   Integer getMaxSerialNoByReportNo(String reportNo);
    /**
     * 根据id更新idClmPaymentItem
     *
     * @param idClmPaymentItem 支付项Id
     * @param id
     */
    void updateIdPaymentItemByPrimaryKey(@Param("id") String id ,@Param("idClmPaymentItem") String idClmPaymentItem);

    /**
     * 根据追偿号查询总追偿收入金额
     * @param replevyNo 追偿案件号
     * @return 总追偿收入金额
     */
    BigDecimal getTotalChargeMoney(@Param("replevyNo") String replevyNo);
    /**
     * 根据报案号和序号查询追偿数据信息
     * @param reportNo 报案号
     */
    ClmsReplevyChargeVo selectReplevyChargeByReportNoAndSerialNo(@Param("reportNo") String reportNo,@Param("serialNo") Integer serialNo);

}
