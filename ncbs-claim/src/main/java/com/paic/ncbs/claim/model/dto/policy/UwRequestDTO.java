package com.paic.ncbs.claim.model.dto.policy;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPlanConclusionEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPolicyConclusionEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 核保请求参数
 */
@Data
public class UwRequestDTO {
    /**
     * 报案号：报案号和赔付次数拼接
     */
    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    /**
     * 核保意见
     */
    @ApiModelProperty("核保意见")
    private String reason;
    @ApiModelProperty("核保任务号")
    private String manualInfoId;
    /**
     * 核保人员
     */
    @ApiModelProperty("核保人员")
    private String umCode;
    /**
     * reportNo层级的核保结论
     */
    @ApiModelProperty("核保结论")
    private String conclusion;

    @ApiModelProperty("函件信息集合")
    List<UwLetterDTO>  letterInfoList;

    @ApiModelProperty("保单信息集合")
    private List<PolicyConclusionDTO> policyList;

    /**
     * 报案号层级信息entity对象
     */
    @ApiModelProperty("报案号层级信息entity对象")
    private ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity;

    /**
     * 保单层级核保结论封装数据信息
     */
    @ApiModelProperty("保单层级核保结论封装数据信息")
    List<ClmsSeconduwPolicyConclusionEntity> policEntityList;
    @ApiModelProperty("险种层级封装数据信息")
    private  List<ClmsSeconduwPlanConclusionEntity> riskEntityList;

}
