package com.paic.ncbs.claim.model.dto.entrustment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@ApiModel("第三方委托DTO")
public class EntrustmentDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("委托表主键")
    private String idEntrustment;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("第三方类型：01-公估，02-律师，03-其他")
    private String thirdPartyType;

    @ApiModelProperty("事故者现状")
    private String insuredApplyStatus;

    @ApiModelProperty("事故场景编号，取自数据字典。如有多个，用英文逗号分隔")
    private String accidentScene;

    @ApiModelProperty("事故场景编号名称")
    private String accidentSceneName;

    @ApiModelProperty("其他")
    private String other;

    @ApiModelProperty("第三方公估公司代码")
    private String entrustmentDpmCode;

    @ApiModelProperty("第三方公估公司名称")
    private String entrustmentDpmName;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系方式")
    private String contactPhone;

    @ApiModelProperty("委托说明")
    private String entrustmentDescription;

    @ApiModelProperty("委托对象")
    private String entrustmentObject;

    @ApiModelProperty("诉讼策略")
    private String litigationStrategy;

    @ApiModelProperty("收费标准")
    private String feeStandard;

    @ApiModelProperty("审批人代码")
    private String auditorUmCode;

    @ApiModelProperty("审批人姓名")
    private String auditorUmName;

    @ApiModelProperty("审批人机构代码")
    private String auditorDpmCode;

    @ApiModelProperty("审批人机构名称")
    private String auditorDpmName;

    @ApiModelProperty("委托状态 0-草稿（暂存时为草稿）、1-待审批、2-不同意、3-同意")
    private String entrustmentStatus;

    @ApiModelProperty("委托打印状态 1-已完成 2-未完成")
    private String printStatus;

    @ApiModelProperty("打印文件id")
    private String fileId;

    @ApiModelProperty("打印中心文件地址")
    private String fileUrl;

    @ApiModelProperty("操作状态 0：暂存 1提交")
    private String operate;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sysCtime;

    @ApiModelProperty("修改人员")
    private String updatedBy;

    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sysUtime;

    @ApiModelProperty("有效标志 Y-有效 N-无效")
    private String validFlag;
}