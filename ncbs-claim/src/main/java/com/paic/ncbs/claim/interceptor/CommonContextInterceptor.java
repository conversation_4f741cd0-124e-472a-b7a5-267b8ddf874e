package com.paic.ncbs.claim.interceptor;


import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Slf4j
@Component
public class CommonContextInterceptor extends HandlerInterceptorAdapter {

    //cookie过期时间24小时
    private static final int COOKIE_TOKEN_EXPIRE_TIME = 60 * 60 * 24;

    @Autowired
    @Lazy
    private CacheService cacheService;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler){
      return loginInterceptor(request,response);
//        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception e) {
    }

    /**
     * 登录验证
     * 1.先从cookie获取理赔域token，
     * 如获取不到再从请求参数获取userCode,
     * 用userCode去redis换取token
     * 然后将token写入cookie
     * 2.根据token获取用户信息
     * 3.用户信息写入session
     * 如中间获取token或userCode为空,意味着登录失效，需要重新登录
     * @param request
     * @param response
     * @return
     */
    private boolean loginInterceptor(HttpServletRequest request, HttpServletResponse response){
        String uri = request.getRequestURI();

        if (active.equals("dev")) {
            initSystemUser(request);
            return true;
        }
        // 放行对外接口
        if (uri.startsWith("/claim/public")) {
            initSystemUser(request);
            return true;
        }
        if(uri.contains("/imgs") || uri.contains("/cookie") || uri.contains("/swagger") || uri.contains("/v2/api-docs") || uri.contains("/claimES")){
            return true;
        }
        if (uri.contains("/healthCheck") || uri.contains("/claimRecord")){
            //健康检查//保全调用 都是可信任的
            initSystemUser(request);
            return true;
        }
        if("C".equals(request.getHeader("authType"))){
            initSystemUser(request);
            return true;
        }
        String userCode = request.getHeader("X-WESURE-ENAME");
        if (org.apache.commons.lang3.StringUtils.equals(active, "dev") && org.apache.commons.lang3.StringUtils.equals("sylvialei", userCode)) {
            throw new GlobalBusinessException(ErrorCode.LOGIN_ERROR,"请先到架构组配置用户权限url");
        }
        UserInfoDTO userInfoDTO ;
        if(StringUtils.isNotEmpty(userCode)){
            try {
                userInfoDTO = cacheService.getUserInfoFromCacheByCode(userCode);
            } catch (NcbsException e) {
                throw new GlobalBusinessException(ErrorCode.LOGIN_ERROR,"调用用户查询接口异常");
            }
            if(userInfoDTO==null){
                throw new GlobalBusinessException(ErrorCode.LOGIN_ERROR,"未查到相关用户信息");
            }
        }else {
            throw new GlobalBusinessException(ErrorCode.LOGIN_ERROR,"请重新登录");
        }
        try {
            List<SystemComInfoDTO> systemComInfoDTOS = cacheService.queryUserSystemComList(userCode);
            if (CollectionUtils.isEmpty(systemComInfoDTOS)){
                throw new GlobalBusinessException(ErrorCode.LOGIN_ERROR,"未获取用户权限机构");
            }
            request.getSession().setAttribute(Constants.CURR_COMCODE,systemComInfoDTOS.get(0).getComCode());
        } catch (NcbsException e) {
            throw new GlobalBusinessException(ErrorCode.LOGIN_ERROR,"获取用户权限机构异常");
        }
        request.getSession().setAttribute(Constants.CURR_USER,userInfoDTO);
        MDC.put(BaseConstant.USER_ID, userInfoDTO.getUserCode());
        return true;
    }

    private void initSystemUser(HttpServletRequest request) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserCode("blueshen"); // blueshen
        userInfoDTO.setUserName("blueshen"); // jiezhang
        userInfoDTO.setComCode(ConfigConstValues.HQ_DEPARTMENT);
        request.getSession().setAttribute(Constants.CURR_USER,userInfoDTO);
        request.getSession().setAttribute(Constants.CURR_COMCODE,ConfigConstValues.HQ_DEPARTMENT);
        MDC.put(BaseConstant.USER_ID, "blueshen");
    }


}
