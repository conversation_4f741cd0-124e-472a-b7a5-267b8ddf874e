package com.paic.ncbs.claim.service.doc.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Maps;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.controller.report.QueryReportController;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.doc.PrintMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.indicators.ClmsCaseIndicatorMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.dao.mapper.print.PrintTemplateMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.NbsRequest;
import com.paic.ncbs.claim.model.dto.doc.*;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.print.*;
import com.paic.ncbs.claim.model.dto.report.PolicyNoNbsQueryDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO;
import com.paic.ncbs.claim.model.vo.doc.*;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePage;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePageResult;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.investigate.ContactInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.model.vo.nbs.EpolicyVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.openapi.OpenGlobalService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.utils.HttpUtils;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import com.paic.ncbs.um.util.Sha1SignUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.google.common.collect.Lists.newArrayList;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
import static com.paic.ncbs.print.util.PdfHelpUtils.xmlByFtl;


@Service("printService")
@Slf4j
@RefreshScope
public class PrintServiceImpl implements PrintService {

    @Autowired
    private TaskListService taskListService;
    /*********************newcode********************/
    @Autowired
    private PrintMapper printMapper;

    @Autowired
    private InvestigateMapper investigateMapper;

    @Autowired
    private InvestigateMapper investigateDao;

    @Autowired
    private EntrustmentMapper entrustmentMapper;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private ReportInfoMapper reportInfoMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private ReportAccidentMapper reportAccidentMapper;

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private QueryReportController queryReportController;

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private EstimatePolicyMapper estimatePolicyDAO;

    @Autowired
    private MaxPayService maxPayService;

    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Value("${samsung.print.url:http://30.83.239.234/print/}")
    private String printNewUrl;

    @Value("${samsung.print.sysid:icoreclaimncbs}")
    private String printSysid;

    @Value("${samsung.print.userName:qianhaiclaim}")
    private String printUserName;

    @Value("${samsung.print.password:aiclaim12345}")
    private String printPassword;

    @Value("${samsung.print.isSign:N}")
    private String isSign;

    public static final String IDGXMLPRINTDTO = "IDGXmlPrintDTO";

    @Autowired
    private PrintCoreService printCoreService;

    @Autowired
    private PrintTemplateMapper printTemplateMapper;

    // 协议赔付
    public static final String PROTOCOL_MODEL = "5";

    /*********************newcode***************************/

    @Value("${samsung.payment.ca.createContractUrl:null}")
    private String createContractUrl;

    @Value("${zking.um.token:null}")
    private String token;

    @Value("${zking.um.clientCode:null}")
    private String clientCode;

    @Value("${samsung.payment.ca.loginName:null}")
    private String loginName;

    @Value("${samsung.payment.ca.keyWord:null}")
    private String keyWord;

    @Value("${samsung.payment.ca.keyWordOrigin:null}")
    private String keyWordOrigin;

    @Value("${samsung.payment.ca.keyWordXOffset:null}")
    private String keyWordXOffset;

    @Value("${samsung.payment.ca.keyWordYOffset:null}")
    private String keyWordYOffset;

    @Value("${samsung.payment.ca.contractDataNotifyUrl:null}")
    private String contractDataNotifyUrl;

    @Value("${samsung.payment.ca.orgName:null}")
    private String orgName;

    @Value("${samsung.payment.ca.templateId:null}")
    private String templateId;

    @Value("${claim.notice.ca.sealCodeOne:null}")
    private String sealCodeOne;

    @Value("${claim.notice.ca.sealCodeTwo:null}")
    private String sealCodeTwo;

    @Value("${claim.notice.ca.sealCodeThree:null}")
    private String sealCodeThree;

    @Value("${claim.notice.ca.sealCodeFour:null}")
    private String sealCodeFour;

    @Value("${claim.notice.ca.sealCodeFive:null}")
    private String sealCodeFive;

    @Value("${claim.notice.ca.sealCodeSix:null}")
    private String sealCodeSix;

    @Value("${claim.notice.ca.sealCodeSeven:null}")
    private String sealCodeSeven;

    @Value("${global.outimeUrl}")
    private String outimeUrl;

    @Autowired
    private DepartmentDefineService departmentDefineService;

    @Autowired
    private ClmsCaseIndicatorMapper clmsCaseIndicatorMapper;

    @Autowired
    private OpenGlobalService openGlobalService;
    @Autowired
    private NbsRequest nbsRequest;

    @Override
    public boolean sendClaimNoticeToCA(CreateClaimNoticeParamDTO contractParamDTO) {
        log.info("====sendContractToCA===contractParamDTO:{}===base64:{}",contractParamDTO.getCtCode(),contractParamDTO.getFileBase64Str().length());
        CreateClaimNoticeRequestDTO contractRequestDTO = new CreateClaimNoticeRequestDTO();
        contractRequestDTO.setVersion("1.0");//接口版本号: 1.0 (默认)
        contractRequestDTO.setServiceId("1");//业务系统编号:1:电子合同平台业务 (默认)，2:招采业务，3:电商业务，4:其他业务
        contractRequestDTO.setFormSource("3");//数据来源；3:其他系统同步(接口默认为3)
        contractRequestDTO.setLoginName(loginName);//合同发起人登陆账号
        contractRequestDTO.setCtCode(contractParamDTO.getCtCode());//业务系统合同编号，唯一  直接传保单号：policyNo
        contractRequestDTO.setCode(contractParamDTO.getCtCode());
        contractRequestDTO.setName("理赔通知书");//合同名称
        contractRequestDTO.setContractName("理赔通知书");//合同名称
        contractRequestDTO.setRemarks("理赔通知书");//合同说明
        contractRequestDTO.setTemplateId(templateId);//合同模板ID,文件上传方式 默认：6D48791D1D1F4AC9876FE0C211C71DE7

        List<SubscriberDTO> subscriberList = new ArrayList<>();
        SubscriberDTO subscriberDTO = new SubscriberDTO();
        StringBuilder keyWordBuild = new StringBuilder();
        keyWordBuild.append(keyWord).append("|").append(keyWordOrigin).append("|").append(keyWordXOffset).append("|").append(keyWordYOffset);
        subscriberDTO.setKeyword(keyWordBuild.toString());//签约关键字参数：a|b|c|d
        subscriberDTO.setOrgName(orgName);//签约人企业名称
        subscriberDTO.setSignsort(1);//签约顺序,大于0的正整数
        //印章类型编号001、手写签名 002、方印 003、注册师章 004、法人章 101、单位公章 102、合同专用章 103、财务专用章 104、
        // 业务专用章 105、勘察设计出图专用章 106、审查合格专用章 107、备案专用章 108、资质号章 109、发票专用章取多个印章使用逗号分隔
        //  理赔单独定制的章
//        subscriberDTO.setSealTypeCode("102");
        subscriberDTO.setSubType("1");//签约人类型，1-企业，2-个人
        subscriberDTO.setAutoSign("1");//企业是否自动签章，0-否，1-是，不传默认为0,仅用于内部企业
        //根据所属机构判断传入的印章编码
        subscriberDTO.setSealCode(getSealCode(contractParamDTO.getCtCode()));
        subscriberList.add(subscriberDTO);
        contractRequestDTO.setSubscriberList(subscriberList);
        contractRequestDTO.setWaitSite("0");//是否设置合同为待设置状态  0: 否   1: 是
        contractRequestDTO.setContractDataNotifyUrl(contractDataNotifyUrl);//合同签署成功合同文件数据异步回调地址，
        //因为文件流很大，为了不影响日志，故不打印文件流
        log.info("请求CA系统签章接口入参：{}", JSON.toJSONString(contractRequestDTO));
        contractRequestDTO.setFileBase64Str(contractParamDTO.getFileBase64Str());//base64编码的文件内容(支持doc，docx,pdf)
        // 改成Mesh
        String result;
        if (switchMesh){
            result = MeshSendUtils.post(createContractUrl + "?" + this.getSignature(), JSON.toJSONString(contractRequestDTO));
        }else {
            result = HttpClientUtil.doPost(createContractUrl + "?" + this.getSignature(), JSON.toJSONString(contractRequestDTO));
        }
        log.info("请求CA系统签章接口成功，出参：{}", result);
        ResponseCaDTO responseCaDto = JSON.parseObject(result, ResponseCaDTO.class);
        //用印发送失败
        return "0".equals(responseCaDto.getCode());//用印发送成功
    }

    @Override
    public boolean sendClaimEntrustToCA(CreateClaimNoticeParamDTO contractParamDTO) {
        log.info("====sendContractToCA===contractParamDTO:{}===base64:{}",contractParamDTO.getCtCode(),contractParamDTO.getFileBase64Str().length());
        CreateClaimNoticeRequestDTO contractRequestDTO = new CreateClaimNoticeRequestDTO();
        contractRequestDTO.setVersion("1.0");//接口版本号: 1.0 (默认)
        contractRequestDTO.setServiceId("1");//业务系统编号:1:电子合同平台业务 (默认)，2:招采业务，3:电商业务，4:其他业务
        contractRequestDTO.setFormSource("3");//数据来源；3:其他系统同步(接口默认为3)
        contractRequestDTO.setLoginName(loginName);//合同发起人登陆账号
        contractRequestDTO.setCtCode(contractParamDTO.getCtCode());//业务系统合同编号，唯一  直接传保单号：policyNo
        contractRequestDTO.setCode(contractParamDTO.getCtCode());
        contractRequestDTO.setName("公估委托书");//合同名称
        contractRequestDTO.setContractName("公估委托书");//合同名称
        contractRequestDTO.setRemarks("公估委托书");//合同说明
        contractRequestDTO.setTemplateId(templateId);//合同模板ID,文件上传方式 默认：6D48791D1D1F4AC9876FE0C211C71DE7

        List<SubscriberDTO> subscriberList = new ArrayList<>();
        SubscriberDTO subscriberDTO = new SubscriberDTO();
        StringBuilder keyWordBuild = new StringBuilder();
        PrintTemplateDTO printTemplate = new PrintTemplateDTO();
        printTemplate.setPrintTemplateCode("ValuationAuthorizationLetter");
        printTemplate.setPrintTemplateType("CLAIM");
        PrintTemplateDTO resultPrintTemplate = printTemplateMapper.getPrintTemplate(printTemplate);
        keyWordBuild = new StringBuilder(resultPrintTemplate.getKeyword());
        subscriberDTO.setKeyword(keyWordBuild.toString());//签约关键字参数：a|b|c|d
        subscriberDTO.setOrgName(orgName);//签约人企业名称
        subscriberDTO.setSignsort(1);//签约顺序,大于0的正整数
        //印章类型编号001、手写签名 002、方印 003、注册师章 004、法人章 101、单位公章 102、合同专用章 103、财务专用章 104、
        // 业务专用章 105、勘察设计出图专用章 106、审查合格专用章 107、备案专用章 108、资质号章 109、发票专用章取多个印章使用逗号分隔
        //  理赔单独定制的章
//        subscriberDTO.setSealTypeCode("102");
        subscriberDTO.setSubType("1");//签约人类型，1-企业，2-个人
        subscriberDTO.setAutoSign("1");//企业是否自动签章，0-否，1-是，不传默认为0,仅用于内部企业
        //根据所属机构判断传入的印章编码
        subscriberDTO.setSealCode(getSealCode(contractParamDTO.getCtCode()));
        subscriberList.add(subscriberDTO);
        contractRequestDTO.setSubscriberList(subscriberList);
        contractRequestDTO.setWaitSite("0");//是否设置合同为待设置状态  0: 否   1: 是
        contractRequestDTO.setContractDataNotifyUrl(contractDataNotifyUrl);//合同签署成功合同文件数据异步回调地址，
        //因为文件流很大，为了不影响日志，故不打印文件流
        log.info("请求CA系统签章接口入参：{}", JSON.toJSONString(contractRequestDTO));
        contractRequestDTO.setFileBase64Str(contractParamDTO.getFileBase64Str());//base64编码的文件内容(支持doc，docx,pdf)
        // 改成Mesh
        String result;
        if (switchMesh){
            result = MeshSendUtils.post(createContractUrl + "?" + this.getSignature(), JSON.toJSONString(contractRequestDTO));
        }else {
            result = HttpClientUtil.doPost(createContractUrl + "?" + this.getSignature(), JSON.toJSONString(contractRequestDTO));
        }
        log.info("请求CA系统签章接口成功，出参：{}", result);
        ResponseCaDTO responseCaDto = JSON.parseObject(result, ResponseCaDTO.class);
        //用印发送失败
        return "0".equals(responseCaDto.getCode());//用印发送成功
    }

    @Override
    public void saveClaimNoticeFileId(String reportNo, Integer caseTimes, String fileId) {
        printMapper.saveClaimNoticeFileId(reportNo,caseTimes,fileId);
    }

    @Override
    public void saveCommissionFileId(PrintEntrustDTO printEntrustDTO) {
        printMapper.saveCommissionFileId(printEntrustDTO);
    }

    @Override
    public String findFileId(String reportNo, Integer caseTimes) {
        return printMapper.findFileId(reportNo,caseTimes);
    }

    @Override
    public List<WholeCaseVO> findFileIdList(String reportNo) {
        return printMapper.findFileIdList(reportNo);
    }


    public String timeOutExport(TimeOutExportVo timeOutExportVo) throws Exception{
        File filepath = new File("./temp");
        if(!filepath.exists()){
            filepath.mkdir();
        }
        String fileName = null;
        try{
            XSSFWorkbook excelResultWorkbook = this.timeOutListExcel(timeOutExportVo);
            if(excelResultWorkbook != null){
                //文件名
                fileName = "temp.xlsx";
                FileOutputStream os = new FileOutputStream(filepath+"/"+fileName);
                //写入文件
                excelResultWorkbook.write(os);
                os.flush();
                os.close();
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return filepath+"/"+fileName;
    }

    public void dealResponse(HttpServletResponse response, String fileName) throws Exception {
        String s = fileName;
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try{
            File file = new File(s);
            if(file.exists()){
                inputStream = new FileInputStream(file);

                response.setContentType("application/vnd.sheet");
                response.setHeader("Content-Disposition",
                        "attachment; filename=\""+new String("超时案件导出".getBytes("gb2312"),"ISO8859-1")+"\"");
                byte[] buffer = new byte[1024 * 1024];
                outputStream = response.getOutputStream();
                int length = 0;
                while((length = inputStream.read(buffer)) > 1){
                    outputStream.write(buffer,0,length);
                }
                //使用完后，删除临时文件
                file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(inputStream != null){
                inputStream.close();
            }
            if(outputStream != null){
                outputStream.close();
            }
        }
    }

    @Override
    public String getEPolicy(PolicyNoNbsQueryDTO policyNoNbsQueryDTO) {

        String result = nbsRequest.queryEPolicy(policyNoNbsQueryDTO);
        Map resultMap = JSON.parseObject(result, Map.class);
        if("000000".equals(resultMap.get("responseCode")) && Objects.nonNull(resultMap.get("data"))) {
            EpolicyVO ePolicyVO = JSON.parseObject(resultMap.get("data").toString(), EpolicyVO.class);
            return ePolicyVO.getPdfUrl();
        }
        return null;
    }

    private XSSFWorkbook timeOutListExcel(TimeOutExportVo timeOutExportVo) {
        //创建Excel的工作书册 Workbook,对应到一个excel文档
        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet("超时案件结果");
        //创建Excel的sheet的一行
        XSSFRow row = sheet.createRow(0);
        XSSFCell cell = null;

        List<String> list = newArrayList();
        list.add("超时类型");
        list.add("机构");
        list.add("报案号");
        list.add("当前案件处理人");
        list.add("超时时长（天）");
        list.add("系统来源");

        String[] cellTitle = new String[list.size()];
        for(int i = 0;i < cellTitle.length; i++){
            cellTitle[i] = list.get(i);
        }
        for(int i = 0;i < cellTitle.length; i++){
            cell = row.createCell(i);
            cell.setCellValue(cellTitle[i]);
        }

        //获取需导出的数据
        List<TimeOutCaseExcelDataVo> voList = newArrayList();
        //区分系统，新理赔/golabl
        String systemCode = timeOutExportVo.getSystemCode();
        if("global".equals(systemCode)){
            GlobalTimeOutReqVo globalTimeOutReqVo = new GlobalTimeOutReqVo();
            BeanUtils.copyProperties(timeOutExportVo,globalTimeOutReqVo);
            GlobalOutTimeCaseRespVO global = sendPostRequestToGlobal(globalTimeOutReqVo);
            //将global接口返回数据处理为excel需要的数据
            voList = globalTranforExcelData(global);
        }else{
            //新非车超时案件数据查询
            log.info(WebServletContext.getDepartmentCode()+"-"+WebServletContext.getUserName()+WebServletContext.getUser().getUserCode());
            timeOutExportVo.setDepartmentCode(WebServletContext.getDepartmentCode());
            // 包含下级机构
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(timeOutExportVo.getDepartmentCode());
            timeOutExportVo.setDepartmentCodes(departmentCodes);
            voList = clmsCaseIndicatorMapper.getTimeOutCaseList(timeOutExportVo);
        }

//        String departmentName = "";
//        DepartmentDefineEntity defineEntity = departmentDefineService.getDepartmentInfo(WebServletContext.getDepartmentCode());
//        if (defineEntity != null) {
//            departmentName = defineEntity.getDepartmentChineseName();
//        }

        //excel内容处理
        String[][] cellConext = new String[voList.size()][cellTitle.length];
        try{
            for(int i = 0; i<voList.size(); i++){
                for(int j = 0;j < list.size(); j++){
                    if(list.get(j).equals("超时类型")){
                        cellConext[i][j] = Constants.OUTTYPE_MAP.get(voList.get(i).getTimeOutType());
                        continue;
                    }
                    if(list.get(j).equals("机构")){

                        cellConext[i][j] = voList.get(i).getDepartmentCode();
                        continue;
                    }
                    if(list.get(j).equals("报案号")){
                        cellConext[i][j] = voList.get(i).getReportNo();
                        continue;
                    }
                    if(list.get(j).equals("当前案件处理人")){
                        cellConext[i][j] = voList.get(i).getDealCode();
                        continue;
                    }
                    if(list.get(j).equals("超时时长（天）")){
                        try{
                            //四舍五入只保留一位小数
                            BigDecimal bd = new BigDecimal(voList.get(i).getTimeoutDay());
                            bd = bd.setScale(1, RoundingMode.HALF_UP);
                            String result = bd.toString();
                            cellConext[i][j] = result;
                            continue;
                        }catch (Exception e){
                            throw new RuntimeException("超时时长数据有误导出失败，请调查");
                        }

                    }
                    if(list.get(j).equals("系统来源")){
                        cellConext[i][j] = voList.get(i).getSystemCode();
                        continue;
                    }
                }
            }
            //写入excel
            for(int i = 0; i < cellConext.length; i++){
                XSSFRow rown = sheet.createRow(i+1);
                rown.setHeight((short) 500);
                for(int j = 0; j < cellConext[i].length; j++){
                    XSSFCell cel = rown.createCell(j);
                    String currentValue = cellConext[i][j];
                    cel.setCellValue(currentValue);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        } finally {
            cellConext = null;
        }
        return wb;
    }

    private List<TimeOutCaseExcelDataVo> globalTranforExcelData(GlobalOutTimeCaseRespVO global) {
        List<TimeOutCaseExcelDataVo> timeOutCaseExcelDataVos = new ArrayList();
        TimeOutResDataVo timeOutResDataVo = global.getResponseData();
        List<TimeOutClaimListVo> timeOutClaimListVos = timeOutResDataVo.getTimeOutClaimList();
        if(timeOutClaimListVos!=null && timeOutClaimListVos.size()>0){
            //获取当前人员的机构
//            String departmentCode =  WebServletContext.getDepartmentCode();
//            DepartmentDefineEntity departmentInfo = departmentDefineService.getDepartmentInfo(departmentCode);
            for(int i = 0; i < timeOutClaimListVos.size(); i++){
                TimeOutCaseExcelDataVo timeOutCaseExcelDataVo = new TimeOutCaseExcelDataVo();
                BeanUtils.copyProperties(timeOutClaimListVos.get(i),timeOutCaseExcelDataVo);
                //处理结构字段值
                timeOutCaseExcelDataVo.setDepartmentCode(timeOutClaimListVos.get(i).getComName());
                //处理当前处理人字段值
                timeOutCaseExcelDataVo.setDealCode(WebServletContext.getUserName());
                timeOutCaseExcelDataVo.setTimeOutType(timeOutClaimListVos.get(i).getTimeOutType());
                timeOutCaseExcelDataVos.add(timeOutCaseExcelDataVo);
            }
        }
        return timeOutCaseExcelDataVos;
    }

    private GlobalOutTimeCaseRespVO sendPostRequestToGlobal(GlobalTimeOutReqVo globalTimeOutReqVo) {
        final String url = outimeUrl;
        String json = JSONObject.toJSONString(globalTimeOutReqVo);
        String response = openGlobalService.openGloabl(url,json);
        //回参解析
        GlobalOutTimeCaseRespVO globalOutTimeCaseRespVO = new GlobalOutTimeCaseRespVO();
        if(response!=null){
             globalOutTimeCaseRespVO = JSONObject.parseObject(response,GlobalOutTimeCaseRespVO.class);
        }
        return globalOutTimeCaseRespVO;
    }

    @Override
    public String findEntrustFileId(PrintEntrustDTO printEntrustDTO) {
        if (InvestigateConstants.PRINT_INVESTIGATE.equals(printEntrustDTO.getPrintFlag())) {// 1-提调
            return printMapper.findCommissionFileId(printEntrustDTO);
        } else if (InvestigateConstants.PRINT_ENTRUSTMENT.equals(printEntrustDTO.getPrintFlag())) {// 2-委托
            return printMapper.findEntrustmentFileId(printEntrustDTO);
        } else {
            throw new GlobalBusinessException("printFlag参数错误，必须为1(提调)或2(委托)");
        }
    }
    @Override
    public AppraisalCommissionDTO bulidPrintInfo(String idAhcsInvestigate) {
        AppraisalCommissionDTO appraisalCommissionDTO =new AppraisalCommissionDTO();
        appraisalCommissionDTO.setIdAhcsInvestigate(idAhcsInvestigate);
        bulidAppraisalCommissionDTO(appraisalCommissionDTO);
        return appraisalCommissionDTO;
    }

    @Override
    public AppraisalCommissionDTO bulidPrintInfoForEntrustment(String idEntrustment) {
        AppraisalCommissionDTO appraisalCommissionDTO = new AppraisalCommissionDTO();
        appraisalCommissionDTO.setIdEntrustment(idEntrustment);
        bulidAppraisalCommissionDTOForEntrustment(appraisalCommissionDTO, idEntrustment);
        return appraisalCommissionDTO;
    }

    public void setEntrustedPartyInfo(AppraisalCommissionDTO appraisalCommissionDTO, InvestigateVO investigateVO) {
        log.info("组装公估委托书数据-调用结算平台查询公估公司信息开始");
        //增加公估公司
        TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
        List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
        Map<String, ServerInfoVO> mapB = new HashMap<>();
        for (ServerInfoVO serverInfoVO : serverInfoList) {
            mapB.put(serverInfoVO.getServerCode(), serverInfoVO);
        }
        ServerInfoVO vo2 = mapB.get(investigateVO.getServerCode());
        //公估公司信息
        if (vo2 != null) {
            appraisalCommissionDTO.setEntrustedPartyName(vo2.getServerName());
            List<ContactInfoVO> contactnfolist = vo2.getContactInfoList();
            if(contactnfolist != null && !contactnfolist.isEmpty()){
                appraisalCommissionDTO.setEntrustedPartyContainer(contactnfolist.get(0).getContacts());
                appraisalCommissionDTO.setEntrustedPartyContactTel(contactnfolist.get(0).getContactNumber());
                appraisalCommissionDTO.setEntrustedPartyContactEmail(contactnfolist.get(0).getEmail());
            }
        }
        log.info("组装公估委托书数据-调用结算平台查询公估公司信息结束");
    }
    public void setClientInfo(AppraisalCommissionDTO appraisalCommissionDTO, InvestigateVO investigateVO) {
        log.info("组装公估委托书数据-委托方信息开始");
        //委托方联系人
        String userCode = investigateVO.getPrimaryInvestigatorUm();
        try {
            UserInfoDTO userInfoDTO = Optional.ofNullable(cacheService.queryUserInfo(userCode)).orElse(new UserInfoDTO());
            appraisalCommissionDTO.setClientContainer(userInfoDTO.getUserName());
            appraisalCommissionDTO.setClientEmail(userInfoDTO.getEmail());
        } catch (NcbsException e){
            log.error("获取委托方信息失败！",e.getMessage());
        }
        log.info("组装公估委托书数据-委托方信息结束");
    }
    public void setOtherInfo(AppraisalCommissionDTO appraisalCommissionDTO, InvestigateVO investigateVO) {
        log.info("组装公估委托书数据-其他信息开始");
        String idAhcsInvestigate = appraisalCommissionDTO.getIdAhcsInvestigate();
        String reportNo = investigateVO.getReportNo();
        //获取委托日期
        InvestigateDTO investigateDTO = new InvestigateDTO();
        investigateDTO.setReportNo(reportNo);
        //查询案件下所有的外部调查
        List<InvestigateVO> list = investigateDao.getHistoryOutInvestigate(investigateDTO);
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setReportNo(investigateDTO.getReportNo());
        //查询任务表中提调审批的信息
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getOutInvestigateTaskInfo(taskInfoDTO);
        Map<String, TaskInfoVO> mapA = new HashMap<>();
        for (TaskInfoVO taskInfoVO : taskInfoVOList) {
            mapA.put(taskInfoVO.getOrderNo(), taskInfoVO);
        }
        for (InvestigateVO vo1 : list) {
            TaskInfoVO vo2 = mapA.get(vo1.getOrderNo());
            if (vo2 != null && idAhcsInvestigate.equals(vo1.getIdAhcsInvestigate())) {
                Date entrustedDate = vo2.getCompleteTime();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = dateFormat.format(entrustedDate);
                appraisalCommissionDTO.setEntrustedDate(formattedDate);
            }
        }
        List<OcasInsuredDTO> ocasInsuredDTOList =
                queryReportController.getCustomerInfoList(reportNo).getData();
        if(!ocasInsuredDTOList.isEmpty()){
            appraisalCommissionDTO.setInsuredName(ocasInsuredDTOList.get(0).getInsuredName());
        }
        ReportInfoEntity reportInfoEntity = reportInfoMapper.getReportInfo(reportNo);
        appraisalCommissionDTO.setInsuredContainer(reportInfoEntity.getReporterName());
        appraisalCommissionDTO.setInsuredTel(reportInfoEntity.getReporterCallNo());
        appraisalCommissionDTO.setRiskCode(ahcsPolicyInfoMapper.getProductNameByReportNo(reportNo));
        appraisalCommissionDTO.setReportNo(reportNo);
        ReportAccidentEntity reportAccidentEntity = reportAccidentMapper.getReportAccident(reportNo);
        appraisalCommissionDTO.setAccidentDetail(reportAccidentEntity.getAccidentDetail());
        appraisalCommissionDTO.setInvestigateItems(investigateVO.getInvestigateItems());
        String accidentClassName = "";
        if (!StringUtils.isEmptyStr(investigateVO.getAccidentScene())){
            List<String> itemNames= Arrays.asList(investigateVO.getAccidentScene().split(",") );
            Collections.sort(itemNames);
            String accidentSceneName = investigateDao.getSelectItemName(itemNames) ;
            investigateVO.setAccidentSceneName(accidentSceneName);
            itemNames.forEach(e->{
                if (ConstValues.INVESTIGATE_OTHER.contains(e)){
                    investigateVO.setAccidentSceneName(accidentSceneName+" "+investigateVO.getOther());
                }
            });
            String valueCode = itemNames.get(0);
            accidentClassName = investigateMapper.getUpperValueName(valueCode);
        }
        String accidentSceneName = investigateVO.getAccidentSceneName();
        String accidentSceneResult = accidentSceneName.replaceAll("\\d+\\.", "");
        appraisalCommissionDTO.setAccidentScene(accidentClassName + "：" + accidentSceneResult);
        log.info("组装公估委托书数据-其他信息结束");
    }
    public void bulidAppraisalCommissionDTO(AppraisalCommissionDTO appraisalCommissionDTO) {
        log.info("组装公估委托书数据开始");
        InvestigateVO investigateVO = investigateMapper.getInvestigateById(appraisalCommissionDTO.getIdAhcsInvestigate());
        //设置公估公司信息
        setEntrustedPartyInfo(appraisalCommissionDTO,investigateVO);
        //设置委托方信息
        setClientInfo(appraisalCommissionDTO,investigateVO);
        //设置其他信息
        setOtherInfo(appraisalCommissionDTO,investigateVO);
    }

    /**
     * 组装委托数据的公估委托书信息
     * @param appraisalCommissionDTO 公估委托书DTO
     * @param idEntrustment 委托ID
     */
    public void bulidAppraisalCommissionDTOForEntrustment(AppraisalCommissionDTO appraisalCommissionDTO, String idEntrustment) {
        log.info("组装委托公估委托书数据开始");
        EntrustmentDTO entrustmentDTO = entrustmentMapper.selectById(idEntrustment);

        if (entrustmentDTO == null) {
            throw new GlobalBusinessException("未找到委托信息，委托ID：" + idEntrustment);
        }
        //设置委托公估公司信息（从EntrustmentDTO中获取）
        setEntrustedPartyInfoForEntrustment(appraisalCommissionDTO, entrustmentDTO);
        //设置委托方信息
        setClientInfoForEntrustment(appraisalCommissionDTO, entrustmentDTO);
        //设置其他信息
        setOtherInfoForEntrustment(appraisalCommissionDTO, entrustmentDTO);
        log.info("组装委托公估委托书数据结束");
    }

    /**
     * 设置委托的公估公司信息
     * @param appraisalCommissionDTO 公估委托书DTO
     * @param entrustmentDTO 委托信息
     */
    public void setEntrustedPartyInfoForEntrustment(AppraisalCommissionDTO appraisalCommissionDTO, EntrustmentDTO entrustmentDTO) {
        log.info("组装委托公估委托书数据-公估公司信息开始");
        // 从委托信息中获取公估公司信息
        appraisalCommissionDTO.setEntrustedPartyName(entrustmentDTO.getEntrustmentDpmName());
        appraisalCommissionDTO.setEntrustedPartyContainer(entrustmentDTO.getContactName());
        appraisalCommissionDTO.setEntrustedPartyContactTel(entrustmentDTO.getContactPhone());
        // 委托表中没有邮箱和手机字段，可以根据需要扩展
        log.info("组装委托公估委托书数据-公估公司信息结束");
    }

    /**
     * 设置委托的委托方信息
     * @param appraisalCommissionDTO 公估委托书DTO
     * @param entrustmentDTO 委托信息
     */
    public void setClientInfoForEntrustment(AppraisalCommissionDTO appraisalCommissionDTO, EntrustmentDTO entrustmentDTO) {
        log.info("组装委托公估委托书数据-委托方信息开始");
        // 从委托信息中获取委托方信息
        String userCode = entrustmentDTO.getCreatedBy();
        try {
            UserInfoDTO userInfoDTO = Optional.ofNullable(cacheService.queryUserInfo(userCode)).orElse(new UserInfoDTO());
            appraisalCommissionDTO.setClientContainer(userInfoDTO.getUserName());
            appraisalCommissionDTO.setClientEmail(userInfoDTO.getEmail());
        } catch (NcbsException e){
            log.error("获取委托方信息失败！",e.getMessage());
        }
        log.info("组装委托公估委托书数据-委托方信息结束");
    }

    /**
     * 设置委托的其他信息
     * @param appraisalCommissionDTO 公估委托书DTO
     * @param entrustmentDTO 委托信息
     */
    public void setOtherInfoForEntrustment(AppraisalCommissionDTO appraisalCommissionDTO, EntrustmentDTO entrustmentDTO) {
        log.info("组装委托公估委托书数据-其他信息开始");
        String reportNo = entrustmentDTO.getReportNo();

        // 获取报案信息
        ReportInfoEntity reportInfoEntity = reportInfoMapper.getReportInfo(reportNo);
        appraisalCommissionDTO.setInsuredContainer(reportInfoEntity.getReporterName());
        appraisalCommissionDTO.setInsuredTel(reportInfoEntity.getReporterCallNo());
        appraisalCommissionDTO.setRiskCode(ahcsPolicyInfoMapper.getProductNameByReportNo(reportNo));
        appraisalCommissionDTO.setReportNo(reportNo);

        // 获取事故信息
        ReportAccidentEntity reportAccidentEntity = reportAccidentMapper.getReportAccident(reportNo);
        appraisalCommissionDTO.setAccidentDetail(reportAccidentEntity.getAccidentDetail());

        // 设置委托内容（从委托说明字段获取）
        appraisalCommissionDTO.setInvestigateItems(entrustmentDTO.getEntrustmentDescription());

        // 设置委托对象信息
        appraisalCommissionDTO.setAccidentScene("委托对象：" + entrustmentDTO.getEntrustmentObject());

        log.info("组装委托公估委托书数据-其他信息结束");
    }

    //条件查询历史已结案件
    @Override
    public WholeCasePageResult getHistoryCaseList(WholeCaseVO wholeCaseVO) throws GlobalBusinessException {
        long startTime = System.currentTimeMillis();
        if (StringUtils.isNotEmpty(wholeCaseVO.getReportNo())
                || StringUtils.isNotEmpty(wholeCaseVO.getPolicyNo())
                || StringUtils.isNotEmpty(wholeCaseVO.getCaseNo())
                || StringUtils.isNotEmpty(wholeCaseVO.getReportBatchNo())
        ) {
            LogUtil.audit("#获取结案信息#入参# reportNo={},CaseNo={},reportBatchNo={},policyNo={}", wholeCaseVO.getReportNo(), wholeCaseVO.getCaseNo(), wholeCaseVO.getReportBatchNo(), wholeCaseVO.getPolicyNo());
            //获取数据
            WholeCasePageResult pageResult = this.getHistoryCasePage(wholeCaseVO);
            LogUtil.audit("查询案件信息列表耗时A=" + (System.currentTimeMillis() - startTime) + "入参:" + JSONObject.toJSONString(wholeCaseVO));
            if (pageResult == null) {
                LogUtil.info("未查询到相关信息，请核实");
                throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "未查询到相关信息，请核实");
            }
            return pageResult;
        } else {
            LogUtil.info("查询条件为空");
            throw new GlobalBusinessException(ErrorCode.Print.PRINT_MISS_PARAM, "查询条件为空");
        }
    }

    //分页
    private WholeCasePageResult getHistoryCasePage(WholeCaseVO wholeCase) {
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
        Pager pager = new Pager();
        pager.setPageIndex(wholeCase.getCurrentPage());
        pager.setPageRows(wholeCase.getPerPageSize());
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        PageHelper.orderBy("report_date desc");
//        List<WholeCaseVO> list = printMapper.getHistoryCase(wholeCase);//获取报案号，结案标识（案件状态），赔付次数,报案时间排序
        List<WholeCaseVO> list = null;
        if(StringUtils.isNotEmpty(wholeCase.getReportNo())){
            list = printMapper.getHistoryCaseByReportNo(wholeCase.getReportNo(),departmentCodes);
        }else if(StringUtils.isNotEmpty(wholeCase.getPolicyNo()) || StringUtils.isNotEmpty(wholeCase.getCaseNo())){
            list = printMapper.getHistoryCaseByPolicyCaseNo(wholeCase.getPolicyNo(),wholeCase.getCaseNo(),wholeCase.getReportBatchNo(),departmentCodes);
        }else if(StringUtils.isNotEmpty(wholeCase.getReportBatchNo())){
            list = printMapper.getHistoryCaseByBatchNo(wholeCase.getReportBatchNo(),departmentCodes);
        }else{

        }

        if (RapeCheckUtil.isEmpty(list)) {
            return null;
        }
        for (WholeCaseVO w : list) {
            w.setAccidentDate(printMapper.getAccidentDateByReportNo(w.getReportNo()));//获取事故时间
            w.setInsuredName(printMapper.getInsuredNameByReportNo(w.getReportNo()));//获取被保险人
            w.setEndCaseAmount(this.getSumPay(w.getReportNo(), w.getCaseTimes()));//获取结案金额
        }
        PageInfo<WholeCaseVO> pageInfo = new PageInfo<>(list);
        List<WholeCaseVO> pageList = pageInfo.getList();

        WholeCasePage wholeCasePage = new WholeCasePage();
        wholeCasePage.setPageIndex(pager.getPageIndex());
        wholeCasePage.setPageRows(pager.getPageRows());
        wholeCasePage.setCurrPageRows(pager.getPageRows());
        wholeCasePage.setDefaultPageRows(20);
        wholeCasePage.setTotalPages(pageInfo.getPages());
        wholeCasePage.setHasNextPage(pageInfo.isHasNextPage());
        wholeCasePage.setHasPrevPage(pageInfo.isHasPreviousPage());
        wholeCasePage.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();

        WholeCasePageResult pageResult = new WholeCasePageResult();
        pageResult.setPager(wholeCasePage);
        pageResult.setList(pageList);

        return pageResult;
    }

    private BigDecimal getSumPay(String reportNo, Integer caseTimes) {
        BigDecimal b = printMapper.getSumPay(reportNo, caseTimes);
        b = b.setScale(2, BigDecimal.ROUND_DOWN);
        return b;
    }

    //获取正常赔付信息
    @Override
    public PrintVO getFormalPayInfo(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException {
        String reportNo = printDutyPayVO.getReportNo();
        Integer caseTimes = printDutyPayVO.getCaseTimes();
        LogUtil.trace("#开始查询正常赔付通知书#入参reportNo=" + reportNo + PrintConstValues.LOG_CASETIMES + caseTimes);
        PrintVO printVO = this.getCaseInfo(reportNo, caseTimes);
        return printVO;
    }

    //获取协议赔付信息
    @Override
    public PrintVO getProtocolPayPrintInfo(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException {
        String reportNo = printDutyPayVO.getReportNo();
        Integer caseTimes = printDutyPayVO.getCaseTimes();
        LogUtil.trace("#开始查询协议赔付通知书#入参reportNo=" + reportNo + PrintConstValues.LOG_CASETIMES + caseTimes);
        PrintVO printVO = this.getCaseInfo(reportNo, caseTimes);
        return printVO;
    }

    //获取零结信息
    @Override
    public PrintVO getZeroEndPrintInfo(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException {
        String reportNo = printDutyPayVO.getReportNo();
        Integer caseTimes = printDutyPayVO.getCaseTimes();
        LogUtil.trace("#开始查询零结通知书#入参reportNo=" + reportNo + PrintConstValues.LOG_CASETIMES + caseTimes);
        PrintVO printVO = this.getZeroEndCaseInfo(reportNo, caseTimes);
        return printVO;
    }

    //获取注销信息
    @Override
    public PrintVO getCancelPrintInfo(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException {
        String reportNo = printDutyPayVO.getReportNo();
        Integer caseTimes = printDutyPayVO.getCaseTimes();
        LogUtil.trace("#开始查询注销通知书#入参reportNo=" + reportNo + PrintConstValues.LOG_CASETIMES + caseTimes);
        PrintVO printVO = this.getCancelCaseInfo(reportNo, caseTimes);
        return printVO;
    }

    //获取拒赔信息
    @Override
    public PrintVO getRefusePay(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException {
        String reportNo = printDutyPayVO.getReportNo();
        Integer caseTimes = printDutyPayVO.getCaseTimes();
        LogUtil.trace("#开始查询拒赔通知书#入参reportNo=" + reportNo + PrintConstValues.LOG_CASETIMES + caseTimes);
        PrintVO printVO = this.getRefusePayCaseInfo(reportNo, caseTimes);
        return printVO;
    }

    //注销
    private PrintVO getCancelCaseInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        PrintVO printVO = new PrintVO();
        //获取结案信息
        PrintCaseInfoVO printCaseInfoVO = printMapper.getPrintCaseInfoVO(reportNo, caseTimes);
        if (printCaseInfoVO == null) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取案件失败");
        }
        printVO.setInsuredName(printCaseInfoVO.getInsuredName());
        printVO.setCertificateNo(printCaseInfoVO.getCertificateNo());
        printVO.setReportNo(printCaseInfoVO.getReportNo());
        printVO.setAccidentDate(printCaseInfoVO.getAccidentDate());
        printVO.setEndCaseDate(printCaseInfoVO.getEndCaseDate());
        //获取保单号
        List<String> policyNoList = printMapper.getPolicyNoListByReportNo(reportNo);
        if (ListUtils.isEmptyList(policyNoList)) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取保单号失败");
        }
        StringBuffer policyNos = new StringBuffer();
        for (int i = 0; i < policyNoList.size(); i++) {
            if (i > 0) {
                policyNos.append(",").append(policyNoList.get(i));
            } else {
                policyNos.append(policyNoList.get(i));
            }
        }
        printVO.setPolicyNos(policyNos.toString());
        //获取注销原因说明
        CaseZeroCancelDTO zeroCancelDTO = printMapper.getCaseZeroCancelInfo(reportNo, caseTimes, 2);
        if (null != zeroCancelDTO) {
            if (!"2".equals(zeroCancelDTO.getVerifyOptions())) {
                printVO.setVerifyRemark(zeroCancelDTO.getVerifyRemark());
            } else {
                throw new GlobalBusinessException(ErrorCode.Print.GET_ZERO_VERIFYREMARK_INFO_FAIL, "获取注销说明失败");
            }
        } else {
            throw new GlobalBusinessException(ErrorCode.Print.GET_ZERO_VERIFYREMARK_INFO_FAIL, "获取注销说明失败");
        }
        EndorsementDTO endorsement = printMapper.getEndorsementInfo(reportNo, caseTimes);
        if (endorsement != null && StringUtils.isNotEmpty(endorsement.getEndorsementRemark())) {
            printVO.setChsEndorsement(endorsement.getEndorsementRemark());
        }
//        printVO.setChsEndorsement("这里是说明/还没拼接字符串？");
        printVO.setChsEndorsement(endorsementAddAmt(printVO, PrintConstValues.DOCTYPE_BUSI));

        //累赔情况说明
        List<PrintDutyPayInfoVO> dutyPayInfo = setMaxPayAmount(reportNo, caseTimes);
        printVO.setDutyPayList(dutyPayInfo);
        //打印次数
        printVO.setPrintCount(printMapper.getPrintCount(reportNo, caseTimes) + 1);
        return printVO;
    }

    private List<PrintDutyPayInfoVO> setMaxPayAmount(String reportNo,Integer caseTimes){
        List<PrintDutyPayInfoVO> dutyPayInfo = printMapper.getDutyPayInfo(reportNo);
        if (dutyPayInfo == null) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取累赔情况说明失败");
        }
        List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(estimatePolicyDTOList)) {
            estimateService.clearEstimateDutyRecordList(estimatePolicyDTOList, BaseConstant.STRING_02);
            estimatePolicyDTOList.get(0).setRollback(true);
            maxPayService.initEstPoliciesPayMaxPay(estimatePolicyDTOList, BaseConstant.MAX_PAY_SCENE_PRINT);
            estimatePolicyDTOList.forEach(policy->{
                List<EstimatePlanDTO> planPayDTOS = policy.getEstimatePlanList();
                planPayDTOS.forEach(plan->{
                    List<EstimateDutyRecordDTO> dutyPayDTOS = plan.getEstimateDutyRecordList();
                    dutyPayDTOS.forEach(duty-> dutyPayInfo.forEach(duty2->{
                        if (duty.getPolicyNo().equals(duty2.getPolicyNo()) && duty.getDutyCode().equals(duty2.getDutyCode())){
                            duty2.setRemainMoney(duty.getDutyMaxPay());
                            duty2.setAlreadyPay(duty.getDutyMaxPay() != null ? duty2.getBaseAmountPay().subtract(duty.getDutyMaxPay()) : null);
                        }
                    }));
                });
            });
        }
        return dutyPayInfo;
    }

    //拒赔
    @SneakyThrows
    private PrintVO getRefusePayCaseInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        PrintVO printVO = new PrintVO();
        //获取结案信息
        PrintCaseInfoVO printCaseInfoVO = printMapper.getPrintCaseInfoVO(reportNo, caseTimes);
        if (printCaseInfoVO == null) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取案件失败");
        }
        printVO.setInsuredName(printCaseInfoVO.getInsuredName());
        printVO.setCertificateNo(printCaseInfoVO.getCertificateNo());
        printVO.setReportNo(printCaseInfoVO.getReportNo());
        printVO.setAccidentDate(printCaseInfoVO.getAccidentDate());
        printVO.setAccidentDateDetail(printCaseInfoVO.getAccidentDate());
        printVO.setEndCaseDate(printCaseInfoVO.getEndCaseDate());
        //获取本次理赔信息明细
        EndorsementDTO endorsementRemark = printMapper.getEndorsementInfo(reportNo, caseTimes);
        if (endorsementRemark != null) {
            endorsementRemark.setEndorsementRemark(endorsementRemark.getEndorsementRemark().replace("\"", ""));
            printVO.setChsEndorsement(endorsementRemark.getEndorsementRemark());
        }
        //保单号
        List<String> policyNoList = printMapper.getPolicyNoListByReportNo(reportNo);
        if (policyNoList == null) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取保单号失败");
        }
        StringBuffer policyNos = new StringBuffer();
        for (int i = 0; i < policyNoList.size(); i++) {
            if (i > 0) {
                policyNos.append(",").append(policyNoList.get(i));
            } else {
                policyNos.append(policyNoList.get(i));
            }
        }
        printVO.setPolicyNos(policyNos.toString());
        //拒赔说明
        VerifyConclusionDTO verifyConclusionDTO = printMapper.getRefuseInfo(reportNo, caseTimes);
        if (StringUtils.isNotEmpty(verifyConclusionDTO.getConclusionCauseCode())) {
            String substring = verifyConclusionDTO.getConclusionCauseCode().substring(0, 1);
            if ("4".equals(substring)) {
                printVO.setVerifyRemark(verifyConclusionDTO.getConclusionCauseDesc());
                printVO.setAuditingCommont(verifyConclusionDTO.getAuditingCommont());
            } else {
                throw new GlobalBusinessException(ErrorCode.Print.GET_ENDORSEMENT_INFO_FAIL, "获取拒赔说明失败");
            }
        } else {
            throw new GlobalBusinessException(ErrorCode.Print.GET_ENDORSEMENT_INFO_FAIL, "获取拒赔说明失败");
        }
        //累赔情况说明
        List<PrintDutyPayInfoVO> dutyPayInfo = setMaxPayAmount(reportNo, caseTimes);
//        printVO.setChsEndorsement(printVO.getVerifyRemark());
        printVO.setChsEndorsement(endorsementAddAmt(printVO,
                PrintConstValues.DOCTYPE_REFUSE_PAY_BUSI));
        printVO.setDutyPayList(dutyPayInfo);
        //打印次数
        printVO.setPrintCount(printMapper.getPrintCount(reportNo, caseTimes) + 1);
        return printVO;
    }

    //零结
    private PrintVO getZeroEndCaseInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        PrintVO printVO = new PrintVO();
        //获取结案信息
        PrintCaseInfoVO printCaseInfoVO = printMapper.getPrintCaseInfoVO(reportNo, caseTimes);
        if (printCaseInfoVO == null) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取案件失败");
        }
        printVO.setInsuredName(printCaseInfoVO.getInsuredName());
        printVO.setCertificateNo(printCaseInfoVO.getCertificateNo());
        printVO.setReportNo(printCaseInfoVO.getReportNo());
        printVO.setAccidentDate(printCaseInfoVO.getAccidentDate());
        printVO.setEndCaseDate(printCaseInfoVO.getEndCaseDate());

        //获取保单号
        List<String> policyNoList = printMapper.getPolicyNoListByReportNo(reportNo);
        if (ListUtils.isEmptyList(policyNoList)) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取保单号失败");
        }
        StringBuffer policyNos = new StringBuffer();
        for (int i = 0; i < policyNoList.size(); i++) {
            if (i > 0) {
                policyNos.append(",").append(policyNoList.get(i));
            } else {
                policyNos.append(policyNoList.get(i));
            }
        }
        printVO.setPolicyNos(policyNos.toString());
        //获取零结原因说明
        CaseZeroCancelDTO zeroCancelDTO = printMapper.getCaseZeroCancelInfo(reportNo, caseTimes, 1);
        if (null != zeroCancelDTO) {
            if (!"2".equals(zeroCancelDTO.getVerifyOptions())) {
                printVO.setVerifyRemark(zeroCancelDTO.getVerifyRemark());
            } else {
                throw new GlobalBusinessException(ErrorCode.Print.GET_ZERO_VERIFYREMARK_INFO_FAIL, "获取零结原因失败");
            }
        } else {
            throw new GlobalBusinessException(ErrorCode.Print.GET_ZERO_VERIFYREMARK_INFO_FAIL, "获取零结原因失败");
        }
        //原因字符拼接？
//        printVO.setChsEndorsement(printVO.getVerifyRemark());
        printVO.setChsEndorsement(endorsementAddAmt(printVO,PrintConstValues.DOCTYPE_ZERO_END_BUSI));
        //累赔情况说明
        List<PrintDutyPayInfoVO> dutyPayInfo = setMaxPayAmount(reportNo, caseTimes);
        printVO.setDutyPayList(dutyPayInfo);
        //打印次数
        printVO.setPrintCount(printMapper.getPrintCount(reportNo, caseTimes) + 1);
        LogUtil.audit("返回结果：" + printVO);
        return printVO;
    }

    //获取正常赔付协议赔付
    private PrintVO getCaseInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        PrintVO printVO = new PrintVO();
        //获取结案信息
        PrintCaseInfoVO printCaseInfoVO = printMapper.getPrintCaseInfoVO(reportNo, caseTimes);
        if (printCaseInfoVO == null) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取案件失败");
        }
        printVO.setInsuredName(printCaseInfoVO.getInsuredName());
        printVO.setCertificateNo(printCaseInfoVO.getCertificateNo());
        printVO.setReportNo(printCaseInfoVO.getReportNo());
        printVO.setAccidentDate(printCaseInfoVO.getAccidentDate());
        printVO.setEndCaseDate(printCaseInfoVO.getEndCaseDate());

        //获取本次理赔信息明细
        EndorsementDTO endorsementRemark = printMapper.getEndorsementInfo(reportNo, caseTimes);
        if (endorsementRemark != null && StringUtils.isNotEmpty(endorsementRemark.getEndorsementRemark())) {
            try {
                LogUtil.audit("#正常赔付通知书#批单内容=" + JSONObject.toJSONString(endorsementRemark));
                List<PrintDutyPayVO> printDutyPayVOList = this.getPrintEndorsementRemark(endorsementRemark.getEndorsementRemark(), reportNo);
                printVO.setEndorsementRemarkList(printDutyPayVOList);
            } catch (Exception e) {
                LogUtil.audit("#协议赔付通知书#批单内容=" + endorsementRemark);
                endorsementRemark.setEndorsementRemark(endorsementRemark.getEndorsementRemark().replace("\"", ""));
                printVO.setChsEndorsement(endorsementRemark.getEndorsementRemark());
            }
        }
        //获取金额
        SettleBatchInfoDTO settleBatchInfoDTO = printMapper.getSettleAmounts(reportNo, caseTimes);
        if (settleBatchInfoDTO == null) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_ENDORSEMENT_INFO_FAIL, "获取赔付金额失败");
        }
        BigDecimal policyPayAmount = nvl(settleBatchInfoDTO.getPolicyPayAmount(), 0);
        BigDecimal prePayAmount = nvl(settleBatchInfoDTO.getPrePayAmount(), 0);
        printVO.setShouldPayAmt(policyPayAmount);
        printVO.setPreparePayAmt(prePayAmount);
        BigDecimal finalPayAmount = policyPayAmount.subtract(prePayAmount);
        printVO.setRealityPayAmt(finalPayAmount);
        //本次费用扣减说明
        List<PrintBillInfoVO> printBillInfoVOList = printMapper.getPrintBillInfoVO(reportNo, caseTimes);
        if (printBillInfoVOList == null) {
            LogUtil.info("费用扣减说明为空");
        }
        //三方金额出现为空的情况发生，统一为0返回
        printBillInfoVOList.forEach(r->{
            if(null == r.getPrepaidAmount()){
                r.setPrepaidAmount(BigDecimal.valueOf(0));
            }
        });

        printVO.setFeeDiscountList(printBillInfoVOList);
        //领款人信息
        List<PrintPayInfoVO> printPayInfoList = this.getPrintPayInfoList(reportNo, caseTimes);
        LogUtil.audit("领款人信息列表：" + JSONObject.toJSONString(printPayInfoList));
        //ShouldPayAmt，PreparePayAmt，RealityPayAmt 三个金额都同时为0 时 认为赔付金额为0，赔付金额为0时不用校验领款人信息
        checkAmntAndPayInfo(printVO,printPayInfoList);

        printVO.setPayList(printPayInfoList);
        //累赔情况说明
        List<PrintDutyPayInfoVO> dutyPayInfo = setMaxPayAmount(reportNo, caseTimes);
        printVO.setDutyPayList(dutyPayInfo);
        //打印次数
        printVO.setPrintCount(printMapper.getPrintCount(reportNo, caseTimes) + 1);
        if (null != printVO.getDutyPayList() && !CollectionUtils.isEmpty(printVO.getDutyPayList())){
            List<PrintDutyPayInfoVO> dutyPayList = printVO.getDutyPayList();
            List<String> policys =dutyPayList.stream().map(PrintDutyPayInfoVO :: getPolicyNo).distinct().collect(Collectors.toList());
            printVO.setPolicyNos(String.join(",",policys));
        }
        return printVO;
    }


    //格式化json批单内容，获取投保人
    private List<PrintDutyPayVO> getPrintEndorsementRemark(String endorsementRemark, String reportNo) throws Exception {
        List<PrintDutyPayVO> printDutyPayVOList = JSONArray.parseArray(endorsementRemark, PrintDutyPayVO.class);
        if (ListUtils.isEmptyList(printDutyPayVOList)) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取正常赔付批单失败");
        }
        List<PrintDutyPayVO> newPrintDutyPayVOList = new ArrayList<PrintDutyPayVO>();
        for (PrintDutyPayVO printDutyPayVO : printDutyPayVOList) {
            String holderName = printMapper.getHolderNameByPolicyNo(null, reportNo, printDutyPayVO.getPolicyNo());
            if (StringUtils.isEmptyStr(holderName)) {
                holderName = printMapper.getHolderNameByPolicyNo(printDutyPayVO.getPolicyNo(), reportNo, null);
            }
            if (StringUtils.isEmptyStr(holderName)) {
                throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取投保人姓名失败");
            }
            printDutyPayVO.setHolderName(holderName);
            printDutyPayVO.getDetailArr().forEach(e-> {
                        if(!StringUtils.isEmptyStr(e.getAdjustmentTextArea())){
                            //换行符替换
                            String test = e.getAdjustmentTextArea().replaceAll("<br>","<br />") ;
                            e.setAdjustmentTextArea( test) ;
                        }
                    }
            );
            log.info("协议赔付协议br标签替换后的样式", JsonUtils.toJsonString(printDutyPayVO));
            newPrintDutyPayVOList.add(printDutyPayVO);
        }
        return newPrintDutyPayVOList;
    }

    //获取支付信息
    private List<PrintPayInfoVO> getPrintPayInfoList(String reportNo, int caseTimes) throws GlobalBusinessException {
        List<PaymentItemComData> paymentItems = null;
        paymentItems = printMapper.requestPaymentItemDTOList(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(paymentItems)) {
            return null;
        }
        List<PrintPayInfoVO> printPayInfos = null;
        for (int i = 0; i < paymentItems.size(); i++) {
            PaymentItemComData paymentItem = paymentItems.get(i);
            if ("90".equals(paymentItem.getPaymentItemStatus())) {
                continue;
            }
            String paymentType = paymentItem.getPaymentType();
            // 13-赔款 11-预赔 12-垫付 1H-追偿,1J直接理赔费用 1J和13一样都是赔款吗？
            if (PrintConstValues.PAYMENT_TYPE_PAY.equals(paymentType) ) {
                if (CollectionUtils.isEmpty(printPayInfos)) {
                    PrintPayInfoVO payInfoVO = new PrintPayInfoVO();
                    BeanUtils.copyProperties(paymentItem, payInfoVO);
                    printPayInfos = new ArrayList<>();
                    if (payInfoVO.getClientName() != null && payInfoVO.getClientBankAccount() != null && payInfoVO.getClientBankName() != null) {
                        printPayInfos.add(payInfoVO);
                    }
                } else {
                    boolean addFlag = false;
                    for (int j = 0; j < printPayInfos.size(); j++) {
                        PrintPayInfoVO payInfoVO = printPayInfos.get(j);
                        if (payInfoVO.getClientName().equals(paymentItem.getClientName())
                                && payInfoVO.getClientBankAccount().equals(paymentItem.getClientBankAccount())
                                && payInfoVO.getClientBankName().equals(paymentItem.getClientBankName())) {
                            payInfoVO.setPaymentAmount(payInfoVO.getPaymentAmount().add(paymentItem.getPaymentAmount()));
                            addFlag = true;
                        }
                    }
                    if (!addFlag) {
                        PrintPayInfoVO payInfoVO = new PrintPayInfoVO();
                        BeanUtils.copyProperties(paymentItem, payInfoVO);
                        if (payInfoVO.getClientName() != null && payInfoVO.getClientBankAccount() != null && payInfoVO.getClientBankName() != null) {
                            printPayInfos.add(payInfoVO);
                        }
                    }
                }
            }
        }
        if (ListUtils.isEmptyList(printPayInfos)) {
            LogUtil.info("未查询到领款人相关信息，请核实");
        }
        return printPayInfos;
    }

    //查询赔付结论
    @Override
    public WholeCaseBaseDTO getWholeCaseIndemnityStatus(String reportNo, Integer caseTimes) {
        WholeCaseBaseDTO wholeCaseIndemnityStatus = printMapper.getWholeCaseIndemnityStatus(reportNo, caseTimes);
        if (wholeCaseIndemnityStatus == null) {
            return null;
        }
        if (StringUtils.isNotEmpty(wholeCaseIndemnityStatus.getIndemnityConclusion()) && "0".equals(wholeCaseIndemnityStatus.getWholeCaseStatus())) {
            return wholeCaseIndemnityStatus;
        } else {
            return null;
        }
    }

    /**
     * 2023-10-23 改动点：零结 注销时 去掉 零结/注销的字样
     * @param printVO
     * @param docType
     * @return
     */
    @SneakyThrows
    private String endorsementAddAmt(PrintVO printVO, String docType) {
        StringBuffer str = new StringBuffer();
        if (PrintConstValues.DOCTYPE_REFUSE_PAY_BUSI.equals(docType)) {
            Date accidentDate = printVO.getAccidentDate();
            String accidentDateStr = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd HH:mm:ss");
            str.append("经本公司审核决定，对被保险人").append(printVO.getInsuredName()).append(accidentDateStr)
                    .append("因事故在保单号").append(printVO.getPolicyNos()).append("下不属保险责任，现做出如下理赔决定：<br/>1、不予给付保险金。<br/>2、本公司做出上述决定的理由是：<br/>")
                    .append(printVO.getAuditingCommont()).append("<br/>若您对本公司的理赔结论有异议，可于接到本通知之日起十日向本公司理赔部门寻求解释。若您觉得仍无法获得满意的答复，您还享有向仲裁机关申请仲裁/向人民法院提起诉讼的权利。请申请并审慎运用您的上述权利。");
        } else if (PrintConstValues.DOCTYPE_ZERO_END_BUSI.equals(docType)) {
            str.append("经本公司审核决定，对被保险人事故在保单")
                    .append(printVO.getPolicyNos()).append("下理赔申请，现做出如下理赔决定：<br/>")
                    .append(printVO.getVerifyRemark());
        } else {
            str.append("经本公司审核决定，对被保险人事故在保单")
                    .append(printVO.getPolicyNos()).append("下理赔申请，现做出如下理赔决定：<br/>")
                    .append(printVO.getVerifyRemark());;
        }
        return str.toString();
    }

    //编写中。。。
    @Override
    public PrintParameterDTO getInfoPrint(PrintVO printVO, IDGXmlPrintDTO<Object> info, String xdpName) throws Exception {
        Configuration configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        String classpath = this.getClass().getResource("/").getPath() + "templates";
        //设置模板路径
        try {
            configuration.setDirectoryForTemplateLoading(new File(classpath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        //设置字符集
        configuration.setDefaultEncoding("UTF-8");
        Template template = configuration.getTemplate(xdpName + ".ftl");
        //塞值
        //模拟查询数据，然后塞到ftl里
        if (info.getDataSet() instanceof PrintFormalPayInfoDTO) {
            PrintFormalPayInfoDTO data = (PrintFormalPayInfoDTO) info.getDataSet();
            data.setBillAmountAll(new BigDecimal(0));
            data.setDeductibleAmountAll(new BigDecimal(0));
            data.setImmoderateAmountAll(new BigDecimal(0));
            data.setPrepaidAmountAll(new BigDecimal(0));
            data.setReasonableAmountAll(new BigDecimal(0));
            data.setPartialDeductibleAll(new BigDecimal(0));
            for (PrintBillInfoDTO d : data.getFeeDiscountList()) {
                if (d.getBillAmount() != null) {
                    data.setBillAmountAll(data.getBillAmountAll().add(d.getBillAmount()));
                } else {
                    d.setBillAmount(new BigDecimal(0));
                }
                if (d.getDeductibleAmount() != null) {
                    data.setDeductibleAmountAll(data.getDeductibleAmountAll().add(d.getDeductibleAmount()));
                } else {
                    d.setDeductibleAmount(new BigDecimal(0));
                }
                if (d.getImmoderateAmount() != null) {
                    data.setImmoderateAmountAll(data.getImmoderateAmountAll().add(d.getImmoderateAmount()));
                } else {
                    d.setImmoderateAmount(new BigDecimal(0));
                }
                if (d.getPrepaidAmount() != null) {
                    data.setPrepaidAmountAll(data.getPrepaidAmountAll().add(d.getPrepaidAmount()));
                } else {
                    d.setPrepaidAmount(new BigDecimal(0));
                }
                if (d.getReasonableAmount() != null) {
                    data.setReasonableAmountAll(data.getReasonableAmountAll().add(d.getReasonableAmount()));
                } else {
                    d.setReasonableAmount(new BigDecimal(0));
                }
                if (d.getPartialDeductible() != null) {
                    data.setPartialDeductibleAll(data.getPartialDeductibleAll().add(d.getPartialDeductible()));
                } else {
                    d.setPartialDeductible(new BigDecimal(0));
                }
            }
        }

        Map<String, Object> map = new HashMap<String, Object>() {{
            put("IDGXmlPrintDTO", info);
        }};
        //xml报文
        String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        System.out.println(content);

        //调用打印接口
        PrintParameterDTO printParameterDTO = new PrintParameterDTO();
        printParameterDTO.setDownload("http://test-open.zking.com/api/gateway/public-base-file-persistence/file/download/a2236e65274b4515a9a396f41afe93a5");
        return printParameterDTO;
    }

    @Override
    public void addPrintRecord(PrintVO printVO) {
        if (printVO != null) {
            if (StringUtils.isNotEmpty(printVO.getReportNo()) && printVO.getCaseTimes() != null) {
                try {
                    PrintRecordDTO recordDTO = new PrintRecordDTO();
                    recordDTO.setReportNo(printVO.getReportNo());
                    recordDTO.setCaseTimes(printVO.getCaseTimes());
//                    recordDTO.setReportNo("111");
//                    recordDTO.setCaseTimes(1);
                    recordDTO.setIdAhcsPrintRecord(UuidUtil.getUUID());
                    recordDTO.setPrintDate(new Date());//打印时间
                    recordDTO.setUserId(WebServletContext.getUserName());//用户id
                    recordDTO.setCreatedBy(WebServletContext.getUserName());//创建人员
                    recordDTO.setCreatedDate(new Date());//创建时间
                    recordDTO.setUpdatedDate(new Date());//修改时间
                    recordDTO.setUpdatedBy(WebServletContext.getUserName());//修改人员
                    printMapper.addPrintRecord(recordDTO);
                } catch (Exception e) {
                    LogUtil.info("插入记录失败");
                }
            } else {
                LogUtil.info("报案号或赔案次数可能为空");
            }
        } else {
            LogUtil.info("前端传入参数可能为空");
        }
    }

    @SneakyThrows
    @Override
    public ClmCommonPayFtlDTO getFormal(PrintVO printVO) {
        ClmCommonPayFtlDTO clmCommonPayFtlDTO = new ClmCommonPayFtlDTO();
        // 领款人
        List<ReceiptInfoFtlDTO> receiptInfos = newArrayList();
        List<PrintPayInfoVO> payList = printVO.getPayList();
        if (!CollectionUtils.isEmpty(payList)){
            for (PrintPayInfoVO printPayInfoVO : payList) {
                ReceiptInfoFtlDTO receiptInfoFtlDTO = new ReceiptInfoFtlDTO();
                receiptInfoFtlDTO.setClientName(printPayInfoVO.getClientName());
                receiptInfoFtlDTO.setReceiptAmount(String.valueOf(printPayInfoVO.getPaymentAmount()));
                receiptInfoFtlDTO.setClientBankName(printPayInfoVO.getClientBankName());
                receiptInfoFtlDTO.setClientBankAccoount(printPayInfoVO.getClientBankAccount());
                receiptInfos.add(receiptInfoFtlDTO);
            }
        }
        clmCommonPayFtlDTO.setReceiptInfos(receiptInfos);
        // 理赔信息
        List<CliamInfoFtlDTO> cliamInfos = newArrayList();
        List<PrintDutyPayVO> remarkList = printVO.getEndorsementRemarkList();
        if (!CollectionUtils.isEmpty(remarkList)){
            for (PrintDutyPayVO printDutyPayVO:remarkList){
                List<PrintDutyVO> detailArr = printDutyPayVO.getDetailArr();
                for (PrintDutyVO printDutyVO : detailArr) {
                    //赔付金额为0时，本次赔付信息只展示赔付依据不为空的保单，责任，金额，依据
                    if(nvl(printDutyVO.getDutyAmount(),0).compareTo(BigDecimal.ZERO)==0){
                        //赔付依据
                        if(ObjectUtil.isNotEmpty(printDutyVO.getAdjustmentTextArea())){
                            setCliamInfoValue(printDutyVO,printDutyPayVO,cliamInfos);
                        }
                    }else{
                        //赔付金额不为0的情况
                        setCliamInfoValue(printDutyVO,printDutyPayVO,cliamInfos);
                    }

                }
            }
        }
        clmCommonPayFtlDTO.setCliamInfos(cliamInfos);
        // 费用
        List<FeeDeductionInfotlDTO> feeDeductionInfos = newArrayList();
        List<PrintBillInfoVO> feeDiscountList = printVO.getFeeDiscountList();
        if (!CollectionUtils.isEmpty(feeDiscountList)){
            for (int i = 0; i < feeDiscountList.size(); i++) {
                PrintBillInfoVO billInfoVO = feeDiscountList.get(i);
                FeeDeductionInfotlDTO feeDeductionInfotlDTO = new FeeDeductionInfotlDTO();
                feeDeductionInfotlDTO.setSerialNo(String.valueOf(i+1));
                feeDeductionInfotlDTO.setReceiptNo(billInfoVO.getBillNo());
                feeDeductionInfotlDTO.setVisitDate(RapeDateUtil.parseToFormatString(billInfoVO.getStartDate(),"yyyy-MM-dd"));
                feeDeductionInfotlDTO.setVisitType(billInfoVO.getTherapyTypeName());
                feeDeductionInfotlDTO.setVisitHospital(billInfoVO.getHospitalName());
                feeDeductionInfotlDTO.setBillAmount(String.valueOf(billInfoVO.getBillAmount()));
                feeDeductionInfotlDTO.setOwnAmount(String.valueOf(billInfoVO.getDeductibleAmount()));
                feeDeductionInfotlDTO.setPartOwnAmount(String.valueOf(billInfoVO.getPartialDeductible()));
                feeDeductionInfotlDTO.setImmoderateAmount(String.valueOf(billInfoVO.getImmoderateAmount()));
                feeDeductionInfotlDTO.setThirdPartyAmount(String.valueOf(billInfoVO.getPrepaidAmount()));
                feeDeductionInfotlDTO.setReasonableAmount(String.valueOf(billInfoVO.getReasonableAmount()));
                feeDeductionInfos.add(feeDeductionInfotlDTO);
            }
        }
        clmCommonPayFtlDTO.setFeeDeductionInfos(feeDeductionInfos);
        // 保单
        List<PolicyInfoFtlDTO> policyInfos = newArrayList();
        List<PrintDutyPayInfoVO> dutyPayList = printVO.getDutyPayList();
        if (!CollectionUtils.isEmpty(dutyPayList)){
            for (PrintDutyPayInfoVO printDutyPayInfoVO : dutyPayList) {
                PolicyInfoFtlDTO policyInfoFtlDTO = new PolicyInfoFtlDTO();
                policyInfoFtlDTO.setPolicyNo(printDutyPayInfoVO.getPolicyNo());
                policyInfoFtlDTO.setDutyName(printDutyPayInfoVO.getDutyName());
                policyInfoFtlDTO.setPolicyTotalAmount(String.valueOf(printDutyPayInfoVO.getBaseAmountPay()));
                policyInfoFtlDTO.setTotalPayAmount(String.valueOf(printDutyPayInfoVO.getAlreadyPay()));
                policyInfoFtlDTO.setRemainAmount(String.valueOf(printDutyPayInfoVO.getRemainMoney()));
                policyInfos.add(policyInfoFtlDTO);
            }
        }
        clmCommonPayFtlDTO.setPolicyInfos(policyInfos);
        clmCommonPayFtlDTO.setNeedPayAmount(String.valueOf(printVO.getShouldPayAmt()));
        clmCommonPayFtlDTO.setPayedAmount(String.valueOf(printVO.getPreparePayAmt()));
        clmCommonPayFtlDTO.setActualPayAmount(String.valueOf(printVO.getRealityPayAmt()));
        clmCommonPayFtlDTO.setInsuredName(printVO.getInsuredName());
        clmCommonPayFtlDTO.setReportNo(printVO.getReportNo());
        clmCommonPayFtlDTO.setCertificateNo(printVO.getCertificateNo());
        Date accidentDate = printVO.getAccidentDate();
        String accidentDateStr = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd");
        String accidentDateStr1 = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd HH:mm:ss");
        clmCommonPayFtlDTO.setAccidentDate(accidentDateStr);
        clmCommonPayFtlDTO.setAccidentTime(accidentDateStr1);
        Date endCaseDate = printVO.getEndCaseDate();
        String endCaseDateStr = DateUtils.parseToFormatString(endCaseDate, "yyyy-MM-dd");
        clmCommonPayFtlDTO.setEndCaseDate(endCaseDateStr);
        clmCommonPayFtlDTO.setPolicyNo(printVO.getPolicyNos());
        clmCommonPayFtlDTO.setEndYear(getYear());
        clmCommonPayFtlDTO.setEndMonth(getMonth());
        clmCommonPayFtlDTO.setEndDay(getDay());
        return clmCommonPayFtlDTO;
    }



    private String getYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int year = calendar.get(Calendar.YEAR);
        return String.valueOf(year);
    }

    @SneakyThrows
    @Override
    public ClmCollegiatePayFtlDTO copyProtocol(PrintVO printVO) {
        ClmCollegiatePayFtlDTO clmCollegiatePayFtlDTO = new ClmCollegiatePayFtlDTO();
        clmCollegiatePayFtlDTO.setNeedPayAmount(String.valueOf(printVO.getShouldPayAmt()));
        clmCollegiatePayFtlDTO.setPayedAmount(String.valueOf(printVO.getPreparePayAmt()));
        clmCollegiatePayFtlDTO.setActualPayAmount(String.valueOf(printVO.getRealityPayAmt()));
        clmCollegiatePayFtlDTO.setInsuredName(printVO.getInsuredName());
        clmCollegiatePayFtlDTO.setReportNo(printVO.getReportNo());
        clmCollegiatePayFtlDTO.setCertificateNo(printVO.getCertificateNo());
        Date accidentDate = printVO.getAccidentDate();
        String accidentDateStr = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd");
        String accidentDateStr1 = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd HH:mm:ss");
        clmCollegiatePayFtlDTO.setAccidentDate(accidentDateStr);
        clmCollegiatePayFtlDTO.setAccidentTime(accidentDateStr1);
        Date endCaseDate = printVO.getEndCaseDate();
        String endCaseDateStr = DateUtils.parseToFormatString(endCaseDate, "yyyy-MM-dd");
        clmCollegiatePayFtlDTO.setEndCaseDate(endCaseDateStr);
        clmCollegiatePayFtlDTO.setPolicyNo(printVO.getPolicyNos());
        clmCollegiatePayFtlDTO.setEndYear(getYear());
        clmCollegiatePayFtlDTO.setEndMonth(getMonth());
        clmCollegiatePayFtlDTO.setEndDay(getDay());
        List<PrintPayInfoVO> payList = printVO.getPayList();
        List<ReceiptInfoFtlDTO> receiptInfos = newArrayList();
        if (!CollectionUtils.isEmpty(payList)){
            for (PrintPayInfoVO printPayInfoVO : payList) {
                ReceiptInfoFtlDTO receiptInfoFtlDTO = new ReceiptInfoFtlDTO();
                receiptInfoFtlDTO.setClientName(printPayInfoVO.getClientName());
                receiptInfoFtlDTO.setReceiptAmount(String.valueOf(printPayInfoVO.getPaymentAmount()));
                receiptInfoFtlDTO.setClientBankName(printPayInfoVO.getClientBankName());
                receiptInfoFtlDTO.setClientBankAccoount(printPayInfoVO.getClientBankAccount());
                receiptInfos.add(receiptInfoFtlDTO);
            }
        }
        clmCollegiatePayFtlDTO.setReceiptInfos(receiptInfos);
        // 保单
        List<PolicyInfoFtlDTO> policyInfos = newArrayList();
        List<PrintDutyPayInfoVO> dutyPayList = printVO.getDutyPayList();
        if (!CollectionUtils.isEmpty(dutyPayList)){
            for (PrintDutyPayInfoVO printDutyPayInfoVO : dutyPayList) {
                PolicyInfoFtlDTO policyInfoFtlDTO = new PolicyInfoFtlDTO();
                policyInfoFtlDTO.setPolicyNo(printDutyPayInfoVO.getPolicyNo());
                policyInfoFtlDTO.setDutyName(printDutyPayInfoVO.getDutyName());
                policyInfoFtlDTO.setPolicyTotalAmount(String.valueOf(printDutyPayInfoVO.getBaseAmountPay()));
                policyInfoFtlDTO.setTotalPayAmount(String.valueOf(printDutyPayInfoVO.getAlreadyPay()));
                policyInfoFtlDTO.setRemainAmount(String.valueOf(printDutyPayInfoVO.getRemainMoney()));
                policyInfos.add(policyInfoFtlDTO);
            }
        }
        clmCollegiatePayFtlDTO.setPolicyInfos(policyInfos);
        return clmCollegiatePayFtlDTO;
    }

    @SneakyThrows
    @Override
    public ClmRefuseFtlDTO copyRefuse(PrintVO printVO) {
        ClmRefuseFtlDTO clmRefuseFtlDTO = new ClmRefuseFtlDTO();
        clmRefuseFtlDTO.setInsuredName(printVO.getInsuredName());
        clmRefuseFtlDTO.setReportNo(printVO.getReportNo());
        clmRefuseFtlDTO.setCertificateNo(printVO.getCertificateNo());
        Date accidentDate = printVO.getAccidentDate();
        String accidentDateStr = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd");
        String accidentDateStr1 = DateUtils.parseToFormatString(printVO.getAccidentDateDetail(), "yyyy-MM-dd HH:mm:ss");
        clmRefuseFtlDTO.setAccidentDate(accidentDateStr);
        clmRefuseFtlDTO.setAccidentTime(accidentDateStr1);
        Date endCaseDate = printVO.getEndCaseDate();
        String endCaseDateStr = DateUtils.parseToFormatString(endCaseDate, "yyyy-MM-dd");
        clmRefuseFtlDTO.setEndCaseDate(endCaseDateStr);
        clmRefuseFtlDTO.setPolicyNo(printVO.getPolicyNos());
        clmRefuseFtlDTO.setEndYear(getYear());
        clmRefuseFtlDTO.setEndMonth(getMonth());
        clmRefuseFtlDTO.setEndDay(getDay());
        clmRefuseFtlDTO.setRefuseReson(printVO.getVerifyRemark());
        // 保单
        List<PolicyInfoFtlDTO> policyInfos = newArrayList();
        List<PrintDutyPayInfoVO> dutyPayList = printVO.getDutyPayList();
        if (!CollectionUtils.isEmpty(dutyPayList)){
            for (PrintDutyPayInfoVO printDutyPayInfoVO : dutyPayList) {
                PolicyInfoFtlDTO policyInfoFtlDTO = new PolicyInfoFtlDTO();
                policyInfoFtlDTO.setPolicyNo(printDutyPayInfoVO.getPolicyNo());
                policyInfoFtlDTO.setDutyName(printDutyPayInfoVO.getDutyName());
                policyInfoFtlDTO.setPolicyTotalAmount(String.valueOf(printDutyPayInfoVO.getBaseAmountPay()));
                policyInfoFtlDTO.setTotalPayAmount(String.valueOf(printDutyPayInfoVO.getAlreadyPay()));
                policyInfoFtlDTO.setRemainAmount(String.valueOf(printDutyPayInfoVO.getRemainMoney()));
                policyInfos.add(policyInfoFtlDTO);
            }
        }
        clmRefuseFtlDTO.setPolicyInfos(policyInfos);
        return clmRefuseFtlDTO;
    }

    @SneakyThrows
    @Override
    public ClmZeroCancelFtlDTO copyZeroCancel(PrintVO printVO) {
        ClmZeroCancelFtlDTO clmZeroCancelFtlDTO = new ClmZeroCancelFtlDTO();
        clmZeroCancelFtlDTO.setInsuredName(printVO.getInsuredName());
        clmZeroCancelFtlDTO.setReportNo(printVO.getReportNo());
        clmZeroCancelFtlDTO.setCertificateNo(printVO.getCertificateNo());
        Date accidentDate = printVO.getAccidentDate();
        String accidentDateStr = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd");
        String accidentDateStr1 = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd HH:mm:ss");
        clmZeroCancelFtlDTO.setAccidentDate(accidentDateStr);
        clmZeroCancelFtlDTO.setAccidentTime(accidentDateStr1);
        Date endCaseDate = printVO.getEndCaseDate();
        String endCaseDateStr = DateUtils.parseToFormatString(endCaseDate, "yyyy-MM-dd");
        clmZeroCancelFtlDTO.setEndCaseDate(endCaseDateStr);
        clmZeroCancelFtlDTO.setPolicyNo(printVO.getPolicyNos());
        clmZeroCancelFtlDTO.setEndYear(getYear());
        clmZeroCancelFtlDTO.setEndMonth(getMonth());
        clmZeroCancelFtlDTO.setEndDay(getDay());
        clmZeroCancelFtlDTO.setRefuseReson(printVO.getVerifyRemark());
        // 保单
        List<PolicyInfoFtlDTO> policyInfos = newArrayList();
        List<PrintDutyPayInfoVO> dutyPayList = printVO.getDutyPayList();
        if (!CollectionUtils.isEmpty(dutyPayList)){
            for (PrintDutyPayInfoVO printDutyPayInfoVO : dutyPayList) {
                PolicyInfoFtlDTO policyInfoFtlDTO = new PolicyInfoFtlDTO();
                policyInfoFtlDTO.setPolicyNo(printDutyPayInfoVO.getPolicyNo());
                policyInfoFtlDTO.setDutyName(printDutyPayInfoVO.getDutyName());
                policyInfoFtlDTO.setPolicyTotalAmount(String.valueOf(printDutyPayInfoVO.getBaseAmountPay()));
                policyInfoFtlDTO.setTotalPayAmount(String.valueOf(printDutyPayInfoVO.getAlreadyPay()));
                policyInfoFtlDTO.setRemainAmount(String.valueOf(printDutyPayInfoVO.getRemainMoney()));
                policyInfos.add(policyInfoFtlDTO);
            }
        }
        clmZeroCancelFtlDTO.setPolicyInfos(policyInfos);
        return clmZeroCancelFtlDTO;
    }

    @SneakyThrows
    @Override
    public ClmCancelFtlDTO copyCancel(PrintVO printVO) {
        ClmCancelFtlDTO clmCancelFtlDTO = new ClmCancelFtlDTO();
        clmCancelFtlDTO.setInsuredName(printVO.getInsuredName());
        clmCancelFtlDTO.setReportNo(printVO.getReportNo());
        clmCancelFtlDTO.setCertificateNo(printVO.getCertificateNo());
        Date accidentDate = printVO.getAccidentDate();
        String accidentDateStr = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd");
        String accidentDateStr1 = DateUtils.parseToFormatString(accidentDate, "yyyy-MM-dd HH:mm:ss");
        clmCancelFtlDTO.setAccidentDate(accidentDateStr);
        clmCancelFtlDTO.setAccidentTime(accidentDateStr1);
        Date endCaseDate = printVO.getEndCaseDate();
        String endCaseDateStr = DateUtils.parseToFormatString(endCaseDate, "yyyy-MM-dd");
        clmCancelFtlDTO.setEndCaseDate(endCaseDateStr);
        clmCancelFtlDTO.setPolicyNo(printVO.getPolicyNos());
        clmCancelFtlDTO.setEndYear(getYear());
        clmCancelFtlDTO.setEndMonth(getMonth());
        clmCancelFtlDTO.setEndDay(getDay());
        clmCancelFtlDTO.setRefuseReson(printVO.getVerifyRemark());
        // 保单
        List<PolicyInfoFtlDTO> policyInfos = newArrayList();
        List<PrintDutyPayInfoVO> dutyPayList = printVO.getDutyPayList();
        if (!CollectionUtils.isEmpty(dutyPayList)){
            for (PrintDutyPayInfoVO printDutyPayInfoVO : dutyPayList) {
                PolicyInfoFtlDTO policyInfoFtlDTO = new PolicyInfoFtlDTO();
                policyInfoFtlDTO.setPolicyNo(printDutyPayInfoVO.getPolicyNo());
                policyInfoFtlDTO.setDutyName(printDutyPayInfoVO.getDutyName());
                policyInfoFtlDTO.setPolicyTotalAmount(String.valueOf(printDutyPayInfoVO.getBaseAmountPay()));
                policyInfoFtlDTO.setTotalPayAmount(String.valueOf(printDutyPayInfoVO.getAlreadyPay()));
                policyInfoFtlDTO.setRemainAmount(String.valueOf(printDutyPayInfoVO.getRemainMoney()));
                policyInfos.add(policyInfoFtlDTO);
            }
        }
        clmCancelFtlDTO.setPolicyInfos(policyInfos);
        return clmCancelFtlDTO;
    }

    @Override
    public void sendPrintCore(String reportNo, Integer caseTimes) {
        PrintDutyPayVO printDutyPayVO = new PrintDutyPayVO();
        printDutyPayVO.setReportNo(reportNo);
        printDutyPayVO.setCaseTimes(caseTimes);
        //查找打印数据
        PrintVO printVO = findVo(printDutyPayVO);
        if(printVO == null){
            LogUtil.info("调用打印平台前未查询到打印数据");
        }
        String docNo = System.currentTimeMillis()+"";
        try {
            LogUtil.info("sendPrintCore发送打印数据入参={}",JSONObject.toJSONString(printVO)); //临时添加，到时删除
            String result = sendData(printVO, docNo);
            LogUtil.info("-发送数据调用打印平台-sendPrintCore result = {}", result);
            String flag =result.replaceAll("returnValue=","");
            //0: 发送数据成功  1:已存在xml数据，重复发送
            if (BaseConstant.STRING_1.equals(flag) || BaseConstant.STRING_0.equals(flag)) {
                LogUtil.info("-发送数据调用打印平台-sendPrintCore success");
                String now = System.currentTimeMillis()+"";
                //异步调用存库
                printCoreService.saveFileAsync(docNo,printVO.getXslFileName(),now,printDutyPayVO);
            } else {
                String message = "未知错误";
                if(BaseConstant.STRING_2.equals(flag)){
                    message = "数据格式不合法";
                }else if ("-1".equals(flag)){
                    message = "密码错误";
                }
                LogUtil.error("调用接口异常:", message);
            }
        } catch (Exception e) {
            LogUtil.error("发送数据调用打印平台失败", e);
        }
    }

    private PrintVO findVo(PrintDutyPayVO printDutyPayVO){
        PrintVO printVO;
        WholeCaseBaseDTO wholeCaseBase = getWholeCaseIndemnityStatus(printDutyPayVO.getReportNo(), printDutyPayVO.getCaseTimes());
        if (wholeCaseBase == null) {
            LogUtil.info("PrintServiceImpl findVo wholeCaseBase is empty");
            return null;
        }
        String conclusion = wholeCaseBase.getIndemnityConclusion();
        String model = wholeCaseBase.getIndemnityModel();
        String docType = null;
        try {
            if (ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED.equals(conclusion)) {
                docType = PrintConstValues.DOCTYPE_ZERO_END_BUSI;
                printVO = getZeroEndPrintInfo(printDutyPayVO);
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_REFUSE.equals(conclusion)) {
                docType = PrintConstValues.DOCTYPE_REFUSE_PAY_BUSI;
                printVO = getRefusePay(printDutyPayVO);
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_PAY.equals(conclusion)) {
                if (StringUtils.isEmptyStr(model)) {
                    docType = PrintConstValues.DOCTYPE_FORMAL_PAY_BUSI;
                    printVO = getFormalPayInfo(printDutyPayVO);
                } else if(PROTOCOL_MODEL.equals(model)){
                    docType = PrintConstValues.DOCTYPE_PROTOCOL_PAY_BUSI;
                    printVO = getProtocolPayPrintInfo(printDutyPayVO);
                } else {
                    docType = PrintConstValues.DOCTYPE_ACCOMMODATION_PAY_BUSI;
                    printVO = getProtocolPayPrintInfo(printDutyPayVO);
                }
            } else {
                docType = PrintConstValues.DOCTYPE_BUSI;
                printVO = getCancelPrintInfo(printDutyPayVO);
            }
        } catch (GlobalBusinessException e) {
            LogUtil.info("PrintServiceImpl findVo error");
            return null;
        }
        printVO.setDocType(docType);
        printVO.setCompanyName("第三方系统财产保险股份有限公司");//写死？
        printVO.setCaseTimes(printDutyPayVO.getCaseTimes());
        // MODIFY BY SHENWEN 针对理赔通知书打印，去除特殊符号html转义逻辑 start
//        if (StringUtils.isNotEmpty(printVO.getVerifyRemark())) {
//            printVO.setVerifyRemark(HtmlUtils.htmlEscape(printVO.getVerifyRemark()));
//        }
//        if (StringUtils.isNotEmpty(printVO.getChsEndorsement())) {
//            printVO.setChsEndorsement(HtmlUtils.htmlEscape(printVO.getChsEndorsement()));
//        }
        // MODIFY BY SHENWEN 针对理赔通知书打印，去除特殊符号html转义逻辑 end
        return printVO;
    }

    private String sendData(PrintVO printVO, String docNo) throws ParseException {
        LogUtil.audit("sendData发送打印数据入参={}",JSONObject.toJSONString(printVO));
        Map<String, String> param = new HashMap<>();
        Map<String, Object> printMap = Maps.newHashMap();
        // 请求配置信息
        IDGConfigDTO configDTO = IDGConfigDTO.builder()
                .branchId(WebServletContext.getDepartmentCode())
                .fileId(printVO.getReportNo() + "_" + DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS))
                .jobType("")
//                .flag("N")
                .build();
        String ftlFileName;
        String templateName = "";
        LogUtil.audit("sendData请求配置信息={}",JSONObject.toJSONString(configDTO));
        if (PrintConstValues.DOCTYPE_FORMAL_PAY_BUSI.equals(printVO.getDocType())) {
            //正常通知书业务
            ClmCommonPayFtlDTO clmCommonPayFtlDTO = getFormal(printVO);
            List<FeeDeductionInfotlDTO> feeDeductionInfotlDTOS = clmCommonPayFtlDTO.getFeeDeductionInfos();
            BigDecimal totalBillAmount = new BigDecimal(0);
            BigDecimal totalReasonableAmount = new BigDecimal(0);
            BigDecimal totalOwnAmount = new BigDecimal(0);
            BigDecimal totalPartOwnAmount = new BigDecimal(0);
            BigDecimal totalImmoderateAmount = new BigDecimal(0);
            BigDecimal totalThirdPartyAmount = new BigDecimal(0);
            //合计数据拼接
            for(int i = 0; i < feeDeductionInfotlDTOS.size(); i++){
                String billAmount = feeDeductionInfotlDTOS.get(i).getBillAmount();
                String reasonableAmount = feeDeductionInfotlDTOS.get(i).getReasonableAmount();
                String ownAmount = feeDeductionInfotlDTOS.get(i).getOwnAmount();
                String partOwnAmount = feeDeductionInfotlDTOS.get(i).getPartOwnAmount();
                String immoderateAmount = feeDeductionInfotlDTOS.get(i).getImmoderateAmount();
                String thirdPartyAmount = feeDeductionInfotlDTOS.get(i).getThirdPartyAmount();

                totalBillAmount = BigDecimalUtils.sum(totalBillAmount,BigDecimalUtils.getBigDecimal(billAmount));
                totalReasonableAmount = BigDecimalUtils.sum(totalReasonableAmount,BigDecimalUtils.getBigDecimal(reasonableAmount));
                totalOwnAmount = BigDecimalUtils.sum(totalOwnAmount,BigDecimalUtils.getBigDecimal(ownAmount));
                totalPartOwnAmount = BigDecimalUtils.sum(totalPartOwnAmount,BigDecimalUtils.getBigDecimal(partOwnAmount));
                totalImmoderateAmount =  BigDecimalUtils.sum(totalImmoderateAmount,BigDecimalUtils.getBigDecimal(immoderateAmount));
                totalThirdPartyAmount = BigDecimalUtils.sum(totalThirdPartyAmount,BigDecimalUtils.getBigDecimal(thirdPartyAmount));
            }
            clmCommonPayFtlDTO.setTotalBillAmount(totalBillAmount+"");
            clmCommonPayFtlDTO.setTotalOwnAmount(totalOwnAmount+"");
            clmCommonPayFtlDTO.setTotalPartOwnAmount(totalPartOwnAmount+"");
            clmCommonPayFtlDTO.setTotalImmoderateAmount(totalImmoderateAmount+"");
            clmCommonPayFtlDTO.setTotalThirdPartyAmount(totalThirdPartyAmount+"");
            clmCommonPayFtlDTO.setTotalReasonableAmount(totalReasonableAmount+"");
            LogUtil.audit("clmCommonPayFtlDTO入参信息={}",JSONObject.toJSONString(clmCommonPayFtlDTO));
            ftlFileName = "ClmCommonPayFtlDTO.ftl";
            configDTO.setClassesCode("ClmCommonPayFtlDTO");
            IDGXmlPrintDTO<ClmCommonPayFtlDTO> xmlDto = IDGXmlPrintDTO.<ClmCommonPayFtlDTO>builder()
                    .dataSet(clmCommonPayFtlDTO)
                    .configDTO(configDTO).build();
            printMap.put(IDGXMLPRINTDTO, xmlDto);
            templateName = "ncbsClaimNormal";
        } else if (PrintConstValues.DOCTYPE_PROTOCOL_PAY_BUSI.equals(printVO.getDocType()) || PrintConstValues.DOCTYPE_ACCOMMODATION_PAY_BUSI.equals(printVO.getDocType())) {
            //合赔业务
            ClmCollegiatePayFtlDTO clmCollegiatePayFtlDTO = copyProtocol(printVO);
            String strDescription = printVO.getChsEndorsement().replaceAll("&lt;br/&gt;|&lt;br /&gt;|&lt;br&gt;", "\n");
            clmCollegiatePayFtlDTO.setChsEndorsement(strDescription);
            //字段
            ftlFileName = "ClmCollegiatePayFtlDTO.ftl";
            configDTO.setClassesCode("ClmCollegiatePayFtlDTO");
            IDGXmlPrintDTO<ClmCollegiatePayFtlDTO> xmlDto = IDGXmlPrintDTO.<ClmCollegiatePayFtlDTO>builder()
                    .dataSet(clmCollegiatePayFtlDTO)
                    .configDTO(configDTO).build();
            printMap.put(IDGXMLPRINTDTO, xmlDto);
            templateName = "ncbsClaimNegotiation";
        } else if (PrintConstValues.DOCTYPE_REFUSE_PAY_BUSI.equals(printVO.getDocType())) {
            //拒赔
            ClmRefuseFtlDTO clmRefuseFtlDTO = copyRefuse(printVO);
            String strDescription = printVO.getChsEndorsement().replaceAll("&lt;br/&gt;|&lt;br /&gt;|&lt;br&gt;", "\n");
            clmRefuseFtlDTO.setRefuseReson(strDescription);
            ftlFileName = "ClmRefuseFtlDTO.ftl";
            configDTO.setClassesCode("ClmRefuseFtlDTO");
            IDGXmlPrintDTO<ClmRefuseFtlDTO> xmlDto = IDGXmlPrintDTO.<ClmRefuseFtlDTO>builder()
                    .dataSet(clmRefuseFtlDTO)
                    .configDTO(configDTO).build();
            printMap.put(IDGXMLPRINTDTO, xmlDto);
            templateName = "ncbsClaimAbnormal";
        } else if (PrintConstValues.DOCTYPE_ZERO_END_BUSI.equals(printVO.getDocType())) {
            //零结
            ClmZeroCancelFtlDTO clmZeroCancelFtlDTO = copyZeroCancel(printVO);
            String strDescription = printVO.getChsEndorsement().replaceAll("&lt;br/&gt;|&lt;br /&gt;|&lt;br&gt;", "\n");
            clmZeroCancelFtlDTO.setRefuseReson(strDescription);
            ftlFileName = "ClmZeroCancelFtlDTO.ftl";
            configDTO.setClassesCode("ClmZeroCancelFtlDTO");
            IDGXmlPrintDTO<ClmZeroCancelFtlDTO> xmlDto = IDGXmlPrintDTO.<ClmZeroCancelFtlDTO>builder()
                    .dataSet(clmZeroCancelFtlDTO)
                    .configDTO(configDTO).build();
            printMap.put(IDGXMLPRINTDTO, xmlDto);
            templateName = "ncbsClaimAbnormal";
        } else if (PrintConstValues.DOCTYPE_BUSI.equals(printVO.getDocType())) {
            //注销
            ClmCancelFtlDTO clmCancelFtlDTO = copyCancel(printVO);
            String strDescription = printVO.getChsEndorsement().replaceAll("&lt;br/&gt;|&lt;br /&gt;|&lt;br&gt;", "\n");
            clmCancelFtlDTO.setRefuseReson(strDescription);
            ftlFileName = "ClmCancelFtlDTO.ftl";
            configDTO.setClassesCode("ClmCancelFtlDTO");
            IDGXmlPrintDTO<ClmCancelFtlDTO> xmlDto = IDGXmlPrintDTO.<ClmCancelFtlDTO>builder()
                    .dataSet(clmCancelFtlDTO)
                    .configDTO(configDTO).build();
            printMap.put(IDGXMLPRINTDTO, xmlDto);
            templateName = "ncbsClaimAbnormal";
        } else {
            //没有文件类型
            throw new GlobalBusinessException(ErrorCode.Print.PRINT_MISS_PARAM, "文件类型可能为空");
        }
        //塞进vo中
        printVO.setXslFileName(templateName);
        String xml = xmlByFtl(ftlFileName, printMap);
        LogUtil.info("PrintController getPrintParameterNew send data to print core,xml= {}", xml);
        String requestUrl = printNewUrl + "cgi-bin/jaspersendXml.py";//具体请求打印平台的地址
        param.put("sysid", printSysid);
        param.put("xmlString", xml);//组装的xmlstring
        param.put("userName", printUserName);
        param.put("password", printPassword);
        param.put("xslFileName", templateName);
        param.put("isSigned", "false");//不用传
        param.put("docno", docNo);//System.currentTimeMillis()
        param.put("doctype", "EDR00001");//单证类型
        if ("Y".equals(isSign)){
            requestUrl = requestUrl + "?" + getSignature();
        }
        // 发送打印请求
        LogUtil.info("PrintController getPrintParameterNew send data to print core,xml= {}", JSON.toJSONString(param));
        // 打印暂时不对接mesh
        return HttpUtils.post(requestUrl, null, param, null, "UTF-8");
    }

    @Override
    public String getSignature() {
        String nonce = RandomStringUtils.randomAlphanumeric(32);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signature = Sha1SignUtil.generateSign(token, timestamp, nonce).toLowerCase();
        StringBuilder sb = new StringBuilder();
        sb.append("nonce=").append(nonce);
        sb.append("&timestamp=").append(timestamp);
        sb.append("&clientCode=").append(clientCode);
        sb.append("&signature=").append(signature);
        log.info("syncDataToCP加签信息: {} " , sb.toString());
        return sb.toString();
    }

    private String getMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int month = calendar.get(Calendar.MONDAY) + 1;
        return String.valueOf(month);
    }

    private String getDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        return String.valueOf(day);
    }

    private String getSealCode(String ctCode){
        String sealCode;
        //因为ctCode是reportNo+caseTimes拼接而成,现在需要根据reportNo获取机构信息,需要做拆分
        String[] ctCodeSplit = ctCode.split("_");
        String reportNo = ctCodeSplit[0];
        //调用接口查询机构信息
        String parentDepartmentCode = printMapper.selectParentDepartmentCodeByReportNo(reportNo);
        //当查询不到三级分公司的上级时,默认给到二级分公司775
        if(null == parentDepartmentCode){
            sealCode = sealCodeSeven;
        }else {
            //根据获取到的机构代码匹配对应的印章编码
            switch (parentDepartmentCode) {
                //北京分公司
                case "776":
                    sealCode = sealCodeOne;
                    break;
                //江苏分公司
                case "785":
                    sealCode = sealCodeTwo;
                    break;
                //青岛分公司
                case "793":
                    sealCode = sealCodeThree;
                    break;
                //陕西分公司
                case "802":
                    sealCode = sealCodeFour;
                    break;
                //深圳分公司
                case "808":
                    sealCode = sealCodeFive;
                    break;
                //天津分公司
                case "814":
                    sealCode = sealCodeSix;
                    break;
                //当查询不到三级分公司的上级时,默认给到二级分公司775
                default:
                    sealCode = sealCodeSeven;
            }
        }
        return sealCode;
    }

    /**
     * 给赔付信息赋值
     * @param printDutyVO
     * @param printDutyPayVO
     */
    private void setCliamInfoValue(PrintDutyVO printDutyVO, PrintDutyPayVO printDutyPayVO,List<CliamInfoFtlDTO> cliamInfos) {
        CliamInfoFtlDTO cliamInfoFtlDTO = new CliamInfoFtlDTO();
        cliamInfoFtlDTO.setApplicationName(printDutyPayVO.getHolderName());
        cliamInfoFtlDTO.setPolicyNo(printDutyPayVO.getPolicyNo());
        cliamInfoFtlDTO.setDutyName(printDutyVO.getDutyName());
        cliamInfoFtlDTO.setPayAmount(String.valueOf(printDutyVO.getDutyAmount()));
        //添加忽略xml转义标签
        cliamInfoFtlDTO.setPaymentBasis("<![CDATA["+printDutyVO.getAdjustmentTextArea()+"]]>");
        cliamInfos.add(cliamInfoFtlDTO);
    }

    /**
     * 校验赔付金额和领款人信息
     * 赔付金额为0时不校验领款人信息
     * printVO.getShouldPayAmt();
     * printVO.getPreparePayAmt();
     * printVO.getRealityPayAmt(); 这三个金额都为0 视为赔付金额为0
     * @param printVO
     * @param printPayInfoList
     */
    private void checkAmntAndPayInfo(PrintVO printVO, List<PrintPayInfoVO> printPayInfoList) {
        //如果ShouldPayAmt，PreparePayAmt，RealityPayAmt 都为0
        if(BigDecimalUtils.isNullOrZero(printVO.getShouldPayAmt())
                &&BigDecimalUtils.isNullOrZero(printVO.getPreparePayAmt())
                &&BigDecimalUtils.isNullOrZero(printVO.getRealityPayAmt())){
            return;
        }
        if (CollectionUtil.isEmpty(printPayInfoList)) {
            throw new GlobalBusinessException(ErrorCode.Print.GET_CASE_INFO_FAIL, "获取领款人信息失败");
        }
    }
}