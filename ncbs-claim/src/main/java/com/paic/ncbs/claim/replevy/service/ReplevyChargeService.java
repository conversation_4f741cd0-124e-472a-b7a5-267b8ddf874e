package com.paic.ncbs.claim.replevy.service;

import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyTextVo;
import com.paic.ncbs.claim.replevy.vo.PayThawBody;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 追偿费用信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public interface ReplevyChargeService {
    /**
     * 追偿费用审批
     * @param clmsReplevyTextVo
     * @param msgList
     * @throws Exception
     */
    public void sendRelevyFeeAudit(ClmsReplevyTextVo clmsReplevyTextVo, List<String> msgList) throws Exception;
    /**
     * 追偿审批
     * @param clmsReplevyTextVo
     * @param msgList
     * @throws Exception
     */
    public void sendRelevyAudit(ClmsReplevyTextVo clmsReplevyTextVo, List<String> msgList) throws Exception;
   /**
     * 发送支付确认（核销接口）
     * @param batchNo
     * @param receiptType
     * @return
     */
    PayThawBody sendPaymentConfirm(String batchNo, String receiptType);
    PaymentItemDTO buildFeePaymemtItem(ClmsReplevyCharge clmsReplevyCharge, ClmsReplevyMain clmsReplevyMainVo);
    /**
     * 构建赔款支付项
     * @param clmsRelatedActualReceipt
     * @param clmsReplevyMainVo
     * @return
     */
    PaymentItemDTO buildPaymemtItem(ClmsRelatedActualReceipt clmsRelatedActualReceipt, ClmsReplevyMain clmsReplevyMainVo);

    /**
     * 生成批次号
     * @return
     */
    String generateBatchNumber();
}
