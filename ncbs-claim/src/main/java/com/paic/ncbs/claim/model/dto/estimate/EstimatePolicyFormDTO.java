package com.paic.ncbs.claim.model.dto.estimate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.dto.report.DutyEstimateLossDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@ApiModel("预估保单表单dto")
public class EstimatePolicyFormDTO extends EntityDTO {

    private static final long serialVersionUID = -3054436254355902624L;

    @ApiModelProperty("意键险保单预估列表")
    private List<EstimatePolicyDTO> estimatePolicyList;

    @ApiModelProperty("数据来源")
    private String dataResource;

    @ApiModelProperty("")
    private String adjustingRemark;

    @ApiModelProperty("跟踪记录表主键")
    private String idAhcsTrackInto;

    @ApiModelProperty("")
    private String approvalOpinions;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("未决类型(01、预估未决、02.立案未决、03.未决跟踪、04.核赔未决、05 重开未决)")
    private String estimateType;

    @ApiModelProperty("")
    private String operateType;

    @ApiModelProperty("是否修正")
    private String isAmend;

    @ApiModelProperty("旧预估金额总和")
    private BigDecimal oldEstimateAmountSum;

    @ApiModelProperty("新预估金额总和")
    private BigDecimal newEstimateAmountSum;

    @ApiModelProperty("")
    private BigDecimal newSubtractOldValue;

    @ApiModelProperty("")
    private String fromPage;

    @ApiModelProperty("")
    private boolean isNeedEstimateReview;

    @ApiModelProperty("")
    private String unOfferAmountTips;

    @ApiModelProperty("预估金额列表")
    private List<BigDecimal> estimateAmount = new ArrayList<>();

    @ApiModelProperty("仲裁费列表")
    private List<BigDecimal> arbitrageFee = new ArrayList<>();

    @ApiModelProperty("诉讼费")
    private List<BigDecimal> lawsuitFee = new ArrayList<>();

    @ApiModelProperty("公估费列表")
    private List<BigDecimal> commonEstimateFee = new ArrayList<>();

    @ApiModelProperty("律师费列表")
    private List<BigDecimal> lawyerFee = new ArrayList<>();

    @ApiModelProperty("执行费列表")
    private List<BigDecimal> executeFee = new ArrayList<>();

    @ApiModelProperty("检验费列表")
    private List<BigDecimal> verifyFee = new ArrayList<>();

    @ApiModelProperty("奖励费列表")
    private List<BigDecimal> awardFee = new ArrayList<>();

    @ApiModelProperty("立案人")
    private String registerUm;

    @ApiModelProperty("立案人姓名")
    private String registerName;

    @ApiModelProperty("立案时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerDate;

    @ApiModelProperty("报案预估金额")
    private BigDecimal estimateLossAmount;

    @ApiModelProperty("案件状态")
    private String processStatus;
    @ApiModelProperty("保单所属机构")
    private String caseDepartment;
    @ApiModelProperty("理赔金额")
    private BigDecimal sumPayAmount;

    /**
     * 查询场景（暂时只用于区分查询历史案件，查询历史案件时 计算剩余理赔金额的已赔付金额要包含当前报案号）
     */
    @ApiModelProperty("查询场景")
    private String scene;

    @ApiModelProperty("赔付结论")
    private String indemnityConclusion;

    /**
     *  案件类别，一级类别：1 人伤，2 非人伤
     */
    private String caseType;
    /**
     *  案件类别，二级类别：意外医疗、疾病住院医疗、疾病门急诊医疗、重大疾病、津贴、意外残疾、疾病残疾、意外身故、疾病身故、其他
     */
    private List<String> caseKind;

    /**
     * 责任估损
     */
    private List<DutyEstimateLossDTO> dutyEstimateLossList;

    /**
     * 发票总金额
     */
    private BigDecimal allBillAmount;

    @ApiModelProperty("未决修正申请保单预估列表")
    private List<EstimateChangePolicyDTO> applyEstimatePolicyList;

    /**
     * 页面调用来源，01-未决修正审批页面
     */
    private String pageSource;

}
