package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.math.BigDecimal;

public interface ClmsRelatedActualReceiptMapper extends BaseDao<ClmsRelatedActualReceipt> {
    List<ClmsRelatedActualReceipt> getRelatedActualReceiptByEntity(ClmsRelatedActualReceipt clmsRelatedActualReceipt);
    List<ClmsRelatedActualReceipt> getListGroupByBankTransFlowNo(ClmsRelatedActualReceipt clmsRelatedActualReceipt);
    void updateSelectiveByPrimaryKey(ClmsRelatedActualReceipt clmsRelatedActualReceipt);

    List<ClmsRelatedActualReceipt> getListByBusinessId(String businessId);
    int updateBatchNo(String batchNo, String reportNo, Integer caseTimes, Integer subTimes);

    /**
     * 根据业务ID查询核销金额总和
     * @param businessId 业务ID（追偿明细ID）
     * @return 核销金额总和
     */
    BigDecimal getTotalWriteOffAmountByDetailId(@Param("businessId") String businessId);
    /**
     * 根据明细Id删除数据
     * @param replevyDetailId
     * @return
     */
    int deleteByBusinessId(String replevyDetailId);
    ClmsRelatedActualReceipt getFreezeData(String reportNo, Integer subTimes, String freezeFlag);
}
