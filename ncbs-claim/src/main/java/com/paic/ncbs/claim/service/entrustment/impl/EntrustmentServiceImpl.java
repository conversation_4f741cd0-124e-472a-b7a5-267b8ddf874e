package com.paic.ncbs.claim.service.entrustment.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

@Service("entrustmentService")
public class EntrustmentServiceImpl implements EntrustmentService {

    @Autowired
    private EntrustmentMapper entrustmentMapper;

    @Autowired
    private EntrustmentAuditMapper entrustmentAuditMapper;

    @Autowired
    private BpmService bpmService;
    
    @Autowired
    private IOperationRecordService operationRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String initEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException {
        UserInfoDTO u = WebServletContext.getUser();
        EntrustmentDTO entrustment = entrustmentApiVo.getEntrustmentDTO();
        // 检查是否可以发起委托
        checkIsCanSendEntrustment(entrustment);

        // 设置基础信息
        if (StringUtils.isEmptyStr(entrustment.getIdEntrustment())) {
            entrustment.setIdEntrustment(UuidUtil.getUUID());
        }

        entrustment.setCreatedBy(u.getUserCode());
        entrustment.setUpdatedBy(u.getUserCode());
        entrustment.setEntrustmentStatus("2"); // 待审批
        
        // 如果是暂存操作
        if ("0".equals(entrustment.getOperate())) {
            entrustment.setEntrustmentStatus("1"); // 草稿
            EntrustmentDTO noCommitData =  entrustmentMapper.getNoCommitData(entrustment.getReportNo(), entrustment.getCaseTimes());
            if (noCommitData == null) {
                entrustmentMapper.insertEntrustment(entrustment);
            } else {
                entrustment.setIdEntrustment(noCommitData.getIdEntrustment());
                entrustmentMapper.updateEntrustment(entrustment);
            }
            return entrustment.getIdEntrustment();
        }
        
        // 提交时删除草稿任务
        entrustmentMapper.deleteEntrustmentNoOperate(entrustment.getReportNo(), entrustment.getCaseTimes());
        
        // 保存委托信息
        entrustmentMapper.insertEntrustment(entrustment);

        // 启动审批流程 - 使用委托专用的工作流启动方法
        bpmService.startProcessEntrustment(entrustment.getReportNo(), entrustment.getCaseTimes(), BpmConstants.OC_ENTRUSTMENT_APPROVAL,
                entrustment.getIdEntrustment());
        
        // 记录操作日志
        operationRecordService.insertOperationRecord(entrustment.getReportNo(), BpmConstants.OC_ENTRUSTMENT_APPROVAL, "发起", null, u.getUserCode());
        
        return entrustment.getIdEntrustment();
    }

    @Override
    public void checkIsCanSendEntrustment(EntrustmentDTO entrustment) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(entrustment.getReportNo())) {
            throw new GlobalBusinessException("报案号不能为空");
        }
        if (entrustment.getCaseTimes() == null) {
            throw new GlobalBusinessException("赔付次数不能为空");
        }
        if (StringUtils.isEmptyStr(entrustment.getThirdPartyType())) {
            throw new GlobalBusinessException("第三方类型不能为空");
        }

        // 新增校验：本案存在未审批完成的第三方委托任务，不允许发起新的委托
        List<EntrustmentDTO> unapprovedEntrustments = entrustmentMapper.selectUnapprovedEntrustments(entrustment.getReportNo(), entrustment.getCaseTimes());
        if (!unapprovedEntrustments.isEmpty()) {
            throw new GlobalBusinessException("本案存在未审批完成的第三方委托任务，不允许发起新的委托");
        }
    }

    @Override
    public EntrustmentVO getEntrustmentById(String idAhcsEntrustment) {
        // 实现根据ID查询委托信息的逻辑
        EntrustmentDTO entrustmentDTO = entrustmentMapper.selectById(idAhcsEntrustment);
        if (entrustmentDTO == null) {
            return null;
        }
        
        // 转换为VO并设置额外信息
        EntrustmentVO entrustmentVO = new EntrustmentVO();
        // 复制属性
        // BeanUtils.copyProperties(entrustmentDTO, entrustmentVO);
        
        return entrustmentVO;
    }

    @Override
    public List<EntrustmentVO> getHistoryEntrustment(String reportNo, Integer caseTimes) {
        List<EntrustmentDTO> entrustmentDTOList = entrustmentMapper.selectHistoryByReportNo(reportNo);
        List<EntrustmentVO> entrustmentVOList = new ArrayList<>();
        
        if (CollectionUtils.isNotEmpty(entrustmentDTOList)) {
            for (EntrustmentDTO dto : entrustmentDTOList) {
                EntrustmentVO vo = new EntrustmentVO();
                // 复制属性
                // BeanUtils.copyProperties(dto, vo);
                entrustmentVOList.add(vo);
            }
        }
        
        return entrustmentVOList;
    }

    @Override
    public List<EntrustmentVO> getHistoryOutEntrustment(EntrustmentDTO entrustmentDTO) {
        // 实现查询历史外部委托的逻辑
        return new ArrayList<>();
    }

    @Override
    public List<AccidentSceneDto> getAccidentSceneData(String collectionCode) {
        // 实现查询事故场景数据的逻辑
        return new ArrayList<>();
    }

    @Override
    public EntrustmentDTO getNoFinishEntrustmentRecord(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        // 实现获取未完成委托记录的逻辑
        return entrustmentMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
    }

    @Override
    public Integer getEntrustmentCount(String reportNo, Integer caseTimes) {
        // 实现获取委托次数的逻辑
        List<EntrustmentDTO> list = entrustmentMapper.selectHistoryByReportNo(reportNo);
        return list != null ? list.size() : 0;
    }

    @Override
    public EntrustmentDTO getCurrentEntrustment(String reportNo, Integer caseTimes) {
        return entrustmentMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
    }

    @Override
    public List<EntrustmentDTO> getPendingApprovalList(String approverUm) {
        return entrustmentMapper.selectPendingApprovalList(approverUm);
    }

    @Override
    public List<EntrustmentDTO> getEntrustmentForPrint(String reportNo) {
        return entrustmentMapper.selectForPrint(reportNo);
    }

    @Override
    public Map<String, String> generateEntrustmentPdf(String idAhcsEntrustment) {
        // 实现生成PDF的逻辑
        return null;
    }

    @Override
    public List<EntrustmentDTO> getHistoryEntrustments(String reportNo, Integer caseTimes, String idEntrustment) {
        // 获取所有历史委托数据
        List<EntrustmentDTO> allHistory = entrustmentMapper.selectHistoryByReportNo(reportNo);

        // 过滤掉当前案件（通过主键进行排除）
        return allHistory.stream()
                .filter(dto -> !dto.getIdEntrustment().equals(idEntrustment))
                .collect(Collectors.toList());
    }
}