package com.paic.ncbs.claim.service.entrustment.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("entrustmentService")
public class EntrustmentServiceImpl implements EntrustmentService {

    @Autowired
    private EntrustmentMapper entrustmentMapper;
    @Autowired
    private EntrustmentAuditMapper entrustmentAuditMapper;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    InvestigateMapper investigateMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String initEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException {
        UserInfoDTO u = WebServletContext.getUser();
        EntrustmentDTO entrustment = entrustmentApiVo.getEntrustmentDTO();
        // 检查是否可以发起委托
        checkIsCanSendEntrustment(entrustment);

        // 设置基础信息
        if (StringUtils.isEmptyStr(entrustment.getIdEntrustment())) {
            entrustment.setIdEntrustment(UuidUtil.getUUID());
        }

        entrustment.setCreatedBy(u.getUserCode());
        entrustment.setUpdatedBy(u.getUserCode());
        entrustment.setEntrustmentStatus("2"); // 待审批

        // 如果是暂存操作
        if ("0".equals(entrustment.getOperate())) {
            entrustment.setEntrustmentStatus("1"); // 草稿
            EntrustmentDTO noCommitData =  entrustmentMapper.getNoCommitData(entrustment.getReportNo(), entrustment.getCaseTimes());
            if (noCommitData == null) {
                entrustmentMapper.insertEntrustment(entrustment);
            } else {
                entrustment.setIdEntrustment(noCommitData.getIdEntrustment());
                entrustmentMapper.updateEntrustment(entrustment);
            }
            return entrustment.getIdEntrustment();
        }

        // 提交时删除草稿任务
        entrustmentMapper.deleteEntrustmentNoOperate(entrustment.getReportNo(), entrustment.getCaseTimes());

        // 保存委托信息
        entrustmentMapper.insertEntrustment(entrustment);

        // 当为提交操作时，生成一笔审批任务
        if ("1".equals(entrustment.getOperate())) {
            generateEntrustmentAuditTask(entrustment, u);
        }

        // 启动审批流程 - 使用委托专用的工作流启动方法
        bpmService.startProcessEntrustment(entrustment.getReportNo(), entrustment.getCaseTimes(), BpmConstants.OC_ENTRUSTMENT_APPROVAL,entrustment.getIdEntrustment());

        // 记录操作日志
        operationRecordService.insertOperationRecord(entrustment.getReportNo(), BpmConstants.OC_ENTRUSTMENT_APPROVAL, "发起", null, u.getUserCode());

        return entrustment.getIdEntrustment();
    }

    @Override
    public void checkIsCanSendEntrustment(EntrustmentDTO entrustment) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(entrustment.getReportNo())) {
            throw new GlobalBusinessException("报案号不能为空");
        }
        if (entrustment.getCaseTimes() == null) {
            throw new GlobalBusinessException("赔付次数不能为空");
        }
        if (StringUtils.isEmptyStr(entrustment.getThirdPartyType())) {
            throw new GlobalBusinessException("第三方类型不能为空");
        }

        // 新增校验：本案存在未审批完成的第三方委托任务，不允许发起新的委托
        List<EntrustmentDTO> unapprovedEntrustments = entrustmentMapper.selectUnapprovedEntrustments(entrustment.getReportNo(), entrustment.getCaseTimes());
        if (!unapprovedEntrustments.isEmpty()) {
            throw new GlobalBusinessException("本案存在未审批完成的第三方委托任务，不允许发起新的委托");
        }
    }

    @Override
    public List<AccidentSceneDto> getAccidentSceneData(String collectionCode) {
        List<AccidentSceneDto> list = new ArrayList<>();
        String[] codes = collectionCode.split(",");
        for (String code : codes) {
            List<AccidentSceneDto> temp = investigateMapper.getAccidentSceneData(code);
            list.addAll(temp);
        }
        //剔除 意外事故、疾病医疗、重大疾病、身故
        list.removeIf(item -> "ASM_1001".equals(item.getValueCode())
                || "ASM_1002".equals(item.getValueCode())
                || "ASM_1003".equals(item.getValueCode())
                || "ASM_1004".equals(item.getValueCode()));
        return list;
    }

    @Override
    public Integer getEntrustmentCount(String reportNo, Integer caseTimes) {
        // 实现获取委托次数的逻辑
        List<EntrustmentDTO> list = entrustmentMapper.selectHistoryByReportNo(reportNo);
        return list != null ? list.size() : 0;
    }

    @Override
    public EntrustmentDTO getCurrentEntrustment(String reportNo, Integer caseTimes) {
        return entrustmentMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
    }

    @Override
    public List<EntrustmentDTO> getPendingApprovalList(String approverUm) {
        return entrustmentMapper.selectPendingApprovalList(approverUm);
    }

    @Override
    public List<EntrustmentDTO> getEntrustmentForPrint(String reportNo) {
        List<EntrustmentDTO> entrustmentList = entrustmentMapper.selectForPrint(reportNo);
        // 过滤出第三方类型为“公估”且审批状态为“同意”的委托记录
        return entrustmentList.stream()
                .filter(e -> "01".equals(e.getThirdPartyType()) && "3".equals(e.getEntrustmentStatus()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, String> generateEntrustmentPdf(String idAhcsEntrustment) {
        // 实现生成PDF的逻辑
        return null;
    }

    @Override
    public List<EntrustmentDTO> getHistoryEntrustments(String reportNo, Integer caseTimes, String idEntrustment) {
        // 获取所有历史委托数据
        List<EntrustmentDTO> allHistory = entrustmentMapper.selectHistoryByReportNo(reportNo);

        // 过滤掉当前案件（通过主键进行排除）
        return allHistory.stream()
                .filter(dto -> !dto.getIdEntrustment().equals(idEntrustment))
                .collect(Collectors.toList());
    }

    /**
     * 生成委托审批任务
     * @param entrustment 委托信息
     * @param user 当前用户
     */
    private void generateEntrustmentAuditTask(EntrustmentDTO entrustment, UserInfoDTO user) {
        // 查询保单号和被保险人姓名
        Map<String, String> policyInfo = entrustmentMapper.getPolicyInfoByReportNo(entrustment.getReportNo());

        // 创建审批记录
        EntrustmentAuditDTO auditDTO = new EntrustmentAuditDTO();
        auditDTO.setIdEntrustmentAudit(UuidUtil.getUUID());
        auditDTO.setIdEntrustment(entrustment.getIdEntrustment());
        auditDTO.setReportNo(entrustment.getReportNo());
        auditDTO.setCaseTimes(entrustment.getCaseTimes());

        // 从查询结果中获取保单号和被保险人姓名
        if (policyInfo != null) {
            auditDTO.setPolicyNo(policyInfo.get("policyNo"));
            auditDTO.setInsuredName(policyInfo.get("insuredName"));
        }

        // 复制委托信息中的数据
        auditDTO.setThirdPartyType(entrustment.getThirdPartyType());
        auditDTO.setEntrustmentDpmCode(entrustment.getEntrustmentDpmCode());
        auditDTO.setEntrustmentDpmName(entrustment.getEntrustmentDpmName());

        // 设置发起人信息（从UserInfoDTO中获取）
        auditDTO.setInitiatorUm(user.getUserCode());
        auditDTO.setInitiatorUmName(user.getUserName());

        // 设置审批人信息（从委托信息中获取）
        auditDTO.setAuditorUm(entrustment.getAuditorUmCode());
        auditDTO.setAuditorUmName(entrustment.getAuditorUmName());

        // 设置基础信息
        auditDTO.setValidFlag("Y");
        auditDTO.setCreatedBy(user.getUserCode());
        auditDTO.setUpdatedBy(user.getUserCode());

        // 插入审批记录
        entrustmentAuditMapper.insertEntrustmentAudit(auditDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitEntrustmentAudit(EntrustmentAuditDTO entrustmentAudit) throws GlobalBusinessException {
        // 更新审批记录
        entrustmentAudit.setExamineTime(new Date());
        entrustmentAuditMapper.updateEntrustmentAudit(entrustmentAudit);

        // 更新委托表状态
        EntrustmentDTO entrustment = entrustmentMapper.selectById(entrustmentAudit.getIdEntrustment());
        if (entrustment != null) {
            if ("1".equals(entrustmentAudit.getAuditOpinion())) { // 不同意
                entrustment.setEntrustmentStatus("2"); // 不同意

                // 生成提醒任务
                generateRejectionNotice(entrustment, entrustmentAudit);
            } else if ("2".equals(entrustmentAudit.getAuditOpinion())) { // 同意
                entrustment.setEntrustmentStatus("3"); // 同意
            }

            UserInfoDTO user = WebServletContext.getUser();
            entrustment.setUpdatedBy(user.getUserCode());
            entrustmentMapper.updateEntrustment(entrustment);
        }
    }

    /**
     * 生成审批退回提醒任务
     * @param entrustment 委托信息
     * @param audit 审批信息
     */
    private void generateRejectionNotice(EntrustmentDTO entrustment, EntrustmentAuditDTO audit) {
        try {
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setReportNo(entrustment.getReportNo());
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK); // 审批退回
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_APPRAISAL_COMMISSION); // 公估委托审批
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(entrustment.getCaseTimes());

            // 提醒内容："{报案号}第三方委托申请被{审批人}审批退回"
            String noticeContent = entrustment.getReportNo() + "第三方委托申请被" + audit.getAuditorUmName() + "审批退回";
            noticesDTO.setNoticeContent(noticeContent);

            // 发送给发起人
            noticeService.saveNotices(noticesDTO, audit.getInitiatorUm());
        } catch (Exception e) {
            LogUtil.error("生成委托审批退回提醒失败", e);
        }
    }
}