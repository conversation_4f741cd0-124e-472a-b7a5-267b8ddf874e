package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.openapi.OrderPaymentRequestDTO;
import com.paic.ncbs.claim.sao.OrderPaymentNoticeService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 指令支付透传接口
 * 渠道调用 告诉收付系统可以支付了
 */
@Api(tags = "指令支付透传接口")
@Slf4j
@RestController
@RequestMapping("/public/instantPayment")
public class OrderPaymentNoticeController {

    @Autowired
    public OrderPaymentNoticeService orderPaymentNoticeService;

    /**
     * 通知收付
     * @return
     */
    @PostMapping("/paymentNotice")
    public ResponseResult<Object> orderPaymentNotice(@RequestBody OrderPaymentRequestDTO requestDTO){
        log.info("指令支付通知接口接收到通知通知收付打款付钱={}", JsonUtils.toJsonString(requestDTO));
        String message = orderPaymentNoticeService.orderPaymentNotice(requestDTO);
        return  ResponseResult.success(message);
    }

}
