package com.paic.ncbs.claim.dao.mapper.entrustment;

import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * 委托审批 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface EntrustmentAuditMapper {

    /**
     * 插入委托审批记录
     * 
     * @param entrustmentAuditDTO 委托审批记录DTO
     * @return 插入结果
     */
    int insertEntrustmentAudit(EntrustmentAuditDTO entrustmentAuditDTO);

    /**
     * 更新委托审批记录
     * 
     * @param entrustmentAuditDTO 委托审批记录DTO
     * @return 更新结果
     */
    int updateEntrustmentAudit(EntrustmentAuditDTO entrustmentAuditDTO);

    /**
     * 根据主键查询委托审批记录
     * 
     * @param idEntrustmentAudit 委托审批记录主键
     * @return 委托审批记录DTO
     */
    EntrustmentAuditDTO selectById(@Param("idEntrustmentAudit") String idEntrustmentAudit);

    /**
     * 根据委托主表ID查询审批记录
     * 
     * @param idEntrustment 委托主表ID
     * @return 委托审批记录列表
     */
    List<EntrustmentAuditDTO> selectByEntrustmentId(@Param("idEntrustment") String idEntrustment);

    /**
     * 根据报案号查询审批记录
     * 
     * @param reportNo 报案号
     * @return 委托审批记录列表
     */
    List<EntrustmentAuditDTO> selectByReportNo(@Param("reportNo") String reportNo);

    /**
     * 根据审批人查询待审批列表
     * 
     * @param auditorUm 审批人UM号
     * @return 待审批的委托审批记录列表
     */
    List<EntrustmentAuditDTO> selectPendingAuditList(@Param("auditorUm") String auditorUm);
}