package com.paic.ncbs.claim.service.wzemployer.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.paic.ncbs.claim.dao.entity.wzemployer.WZListUploadRecordEntity;
import com.paic.ncbs.claim.dao.mapper.wzemployer.WZListUploadRecordMapper;
import com.paic.ncbs.claim.service.wzemployer.WZListUploadRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RefreshScope
@Service("WZListUploadRecordService")
public class WZListUploadRecordServiceImpl implements WZListUploadRecordService {

    @Autowired
    private WZListUploadRecordMapper wzListUploadRecordMapper;

    public String getUploadRecordUrl(String uploadDate) {
        String uploadFileUrl = "";
        List<WZListUploadRecordEntity> wzListUploadRecordEntities = wzListUploadRecordMapper.selectList(new LambdaQueryWrapper<WZListUploadRecordEntity>().eq(WZListUploadRecordEntity::getUploadDate,
                uploadDate).orderByDesc(WZListUploadRecordEntity::getUploadDate));
        if(null != wzListUploadRecordEntities && wzListUploadRecordEntities.size() > 0){
            uploadFileUrl = wzListUploadRecordEntities.get(0).getUploadFileUrl();
        }
        return uploadFileUrl;
    }
}
