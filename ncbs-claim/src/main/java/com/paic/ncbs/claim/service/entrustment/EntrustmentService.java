package com.paic.ncbs.claim.service.entrustment;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;

import java.util.List;
import java.util.Map;

public interface EntrustmentService {

    /**
     * 初始化委托
     * @param entrustmentApiVo 委托信息
     * @throws GlobalBusinessException 业务异常
     */
    String initEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException;

    /**
     * 检查是否可以发送委托
     * @param entrustment 委托信息
     * @throws GlobalBusinessException 业务异常
     */
    void checkIsCanSendEntrustment(EntrustmentDTO entrustment) throws GlobalBusinessException;

    /**
     * 获取事故场景数据
     * @param collectionCode 集合代码
     * @return 事故场景DTO列表
     */
    List<AccidentSceneDto> getAccidentSceneData(String collectionCode);

    /**
     * 获取委托次数
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托次数
     */
    Integer getEntrustmentCount(String reportNo, Integer caseTimes);

    /**
     * 获取当前委托
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托DTO
     */
    EntrustmentDTO getCurrentEntrustment(String reportNo, Integer caseTimes);

    /**
     * 获取待审批列表
     * @param approverUm 审批人UM
     * @return 委托DTO列表
     */
    List<EntrustmentDTO> getPendingApprovalList(String approverUm);

    /**
     * 获取用于打印的委托信息
     * @param reportNo 报案号
     * @return 委托DTO列表
     */
    List<EntrustmentDTO> getEntrustmentForPrint(String reportNo);
    
    /**
     * 生成委托PDF
     * @param idAhcsEntrustment 委托ID
     * @return PDF信息
     */
    Map<String, String> generateEntrustmentPdf(String idAhcsEntrustment);

    /**
     * 获取历史委托列表（排除当前案件）
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 历史委托列表
     */
    List<EntrustmentDTO> getHistoryEntrustments(String reportNo, Integer caseTimes, String idEntrustment);

    /**
     * 提交委托审批
     * @param entrustmentAudit 委托审批信息
     * @throws GlobalBusinessException 业务异常
     */
    void submitEntrustmentAudit(EntrustmentAuditDTO entrustmentAudit) throws GlobalBusinessException;
}