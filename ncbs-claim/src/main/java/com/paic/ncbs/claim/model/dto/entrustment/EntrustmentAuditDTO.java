package com.paic.ncbs.claim.model.dto.entrustment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Setter
@Getter
@ApiModel("委托审批")
public class EntrustmentAuditDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("委托审核表主键")
    private String idEntrustmentAudit;

    @ApiModelProperty("委托表主键")
    private String idEntrustment;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("被保险人名称")
    private String insuredName;

    @ApiModelProperty("第三方类型：01-公估，02-律师，03-其他")
    private String thirdPartyType;

    @ApiModelProperty("第三方公估公司代码")
    private String entrustmentDpmCode;

    @ApiModelProperty("第三方公估公司名称")
    private String entrustmentDpmName;

    @ApiModelProperty("发起人")
    private String initiatorUm;

    @ApiModelProperty("发起人姓名")
    private String initiatorUmName;

    @ApiModelProperty("审批人")
    private String auditorUm;

    @ApiModelProperty("审批人姓名")
    private String auditorUmName;

    @ApiModelProperty("审批人机构代码")
    private String auditorDpmCode;

    @ApiModelProperty("审批意见 1-不同意、2-同意")
    private String auditOpinion;

    @ApiModelProperty("审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date examineTime;

    @ApiModelProperty("有效标志 Y-有效 N-无效")
    private String validFlag;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sysCtime;

    @ApiModelProperty("修改人员")
    private String updatedBy;

    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sysUtime;
    
    // 调整了字段顺序以匹配clms_entrustment_audit表结构，并添加了缺失的auditorDpmCode字段
}