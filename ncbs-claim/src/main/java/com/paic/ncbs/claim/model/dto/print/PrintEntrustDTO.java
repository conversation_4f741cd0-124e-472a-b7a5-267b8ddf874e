package com.paic.ncbs.claim.model.dto.print;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 公估委托书送打印数据
 */
@Data
public class PrintEntrustDTO {

    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date updatedDate;
    /**
     * 主键-调查信息表
     */
    private String idAhcsInvestigate;
    /**
     * 主键-委托信息表
     */
    private String idEntrustment;
    /**
     * 受委托方-名称
     */
    private String entrustedPartyName;
    /**
     * 受委托方-联系人
     */
    private String entrustedPartyContainer;
    /**
     * 受委托方-Tel
     */
    private String entrustedPartyContactTel;
    /**
     * 受委托方-Mobile
     */
    private String entrustedPartyContactMobile;
    /**
     * 受委托方-Email
     */
    private String entrustedPartyContactEmail;

    /**
     * 被保险人-名称
     */
    private String insuredName;
    /**
     * 被保险人-联系人
     */
    private String insuredContainer;
    /**
     * 被保险人-Tel
     */
    private String insuredTel;
    /**
     * 被保险人-Mobile
     */
    private String insuredMobile;
    /**
     * 被保险人-Email
     */
    private String insuredEmail;
    /**
     * 出险险种
     */
    private String riskCode;
    /**
     * 我司案号
     */
    private String reportNo;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 事故简要
     */
    private String accidentDetail;
    /**
     * 委托内容
     */
    private String entrustedContent;
    /**
     * 委托方-联系人
     */
    private String clientContainer;
    /**
     * 委托方-Tel
     */
    private String clientTel;
    /**
     * 委托方-Mobile
     */
    private String clientMobile;
    /**
     * 委托方-Email
     */
    private String clientEmail;
    /**
     * fileId
     */
    private String fileId;
    /**
     * printFlag 1-提调 2-委托
     */
    private String printFlag;

}
