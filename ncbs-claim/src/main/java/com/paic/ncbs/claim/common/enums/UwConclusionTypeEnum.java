package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 核保结论枚举
 */
public enum UwConclusionTypeEnum {

    TYPE_ONE("1","标体"),
    TYPE_TWO("2","拒保"),
    TYPE_THREE("3","除外");
    private final String name;
    private final String code;

    UwConclusionTypeEnum(String code,String name) {
        this.code = code;
        this.name = name;

    }
    public static String getName(String type) {
        if(StrUtil.isEmpty(type)){
            return null;
        }
        for (UwConclusionTypeEnum typeEnum : UwConclusionTypeEnum.values()) {
            if (type.equals(typeEnum.getCode())) {
                return typeEnum.getName();
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
