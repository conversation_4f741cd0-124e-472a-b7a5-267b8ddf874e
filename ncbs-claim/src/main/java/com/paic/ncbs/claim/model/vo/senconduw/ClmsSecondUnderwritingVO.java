package com.paic.ncbs.claim.model.vo.senconduw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 理赔二核申请记录表(ClmsSecondUnderwritingVO)实体类
 *
 * <AUTHOR>
 * @since 2023-09-08 15:15:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ClmsSecondUnderwritingVO")
public class ClmsSecondUnderwritingVO implements Serializable {
    private static final long serialVersionUID = 964523950609098843L;
    /**
     * 主键
     */
    @ApiModelProperty(" 主键")
    private String id;

    @ApiModelProperty("序号")
    private String serialNo;

    @ApiModelProperty("送核状态：01-送核审批中，02-核保完成,03-核保退回")
    private String underwritingStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("二核发起时间")
    private Date uwStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("二核完成时间")
    private Date uwCompleteDate;

    @ApiModelProperty("核保意见")
    private String uwAdvice;

    @ApiModelProperty("核保人员")
    private String uwOperator;

    @ApiModelProperty("核保任务号")
    private String manualInfoId;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("事故日期")
    private Date accidentDate;

    @ApiModelProperty("诊断信息")
    List<DiagnosisVO>  diagnosisVOList;

    @ApiModelProperty("证据材料类型")
    private String evidenceMaterialType;

    @ApiModelProperty("文件id")
    private String materialFileId;
    @ApiModelProperty("送核说明")
    private String underwritingExplain;

    /***
     * 函件信息
     */
    List<UwLetterVO> uwLetterVOList;

    @ApiModelProperty("函件回销状态")
    private String lettersCancelStatus;


    @ApiModelProperty("保单核保结论信息")
    List<ClmsSeconduwPolicyConclusionVO> policyConclusionVOList;

    @ApiModelProperty("送核材料")
    private List<FileInfoDTO> fileInfoDTOs;

    @ApiModelProperty("等待期天数")
    private Integer waitingPeriodDays;

}

