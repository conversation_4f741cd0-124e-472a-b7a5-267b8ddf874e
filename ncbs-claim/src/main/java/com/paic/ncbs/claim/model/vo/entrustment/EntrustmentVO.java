package com.paic.ncbs.claim.model.vo.entrustment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@SuppressWarnings("serial")
public class EntrustmentVO extends EntrustmentDTO {

    private String initiatorUmName;

    private String initiatorName;

    private String primaryEntrustmentUmName;

    private String entrustmentDepartmentName;

    private String sendBackMan;

    private String sendBackReason;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendBackTime;

    private String entrustmentQualitativeName;

    private List<EntrustmentAdditionalVO> additionals;

    private EntrustmentEvaluateVO entrustmentEvaluateVO;

    public String getEntrustmentQualitativeName() {
        return entrustmentQualitativeName;
    }

    public void setEntrustmentQualitativeName(String entrustmentQualitativeName) {
        this.entrustmentQualitativeName = entrustmentQualitativeName;
    }

    public String getInitiatorUmName() {
        return initiatorUmName;
    }

    public void setInitiatorUmName(String initiatorUmName) {
        this.initiatorUmName = initiatorUmName;
    }

    public String getInitiatorName() {
        return initiatorName;
    }

    public void setInitiatorName(String initiatorName) {
        this.initiatorName = initiatorName;
    }

    public String getPrimaryEntrustmentUmName() {
        return primaryEntrustmentUmName;
    }

    public void setPrimaryEntrustmentUmName(String primaryEntrustmentUmName) {
        this.primaryEntrustmentUmName = primaryEntrustmentUmName;
    }

    public String getEntrustmentDepartmentName() {
        return entrustmentDepartmentName;
    }

    public void setEntrustmentDepartmentName(String entrustmentDepartmentName) {
        this.entrustmentDepartmentName = entrustmentDepartmentName;
    }

    public String getSendBackMan() {
        return sendBackMan;
    }

    public void setSendBackMan(String sendBackMan) {
        this.sendBackMan = sendBackMan;
    }

    public String getSendBackReason() {
        return sendBackReason;
    }

    public void setSendBackReason(String sendBackReason) {
        this.sendBackReason = sendBackReason;
    }

    public Date getSendBackTime() {
        return sendBackTime;
    }

    public void setSendBackTime(Date sendBackTime) {
        this.sendBackTime = sendBackTime;
    }

    public List<EntrustmentAdditionalVO> getAdditionals() {
        return additionals;
    }

    public void setAdditionals(List<EntrustmentAdditionalVO> additionals) {
        this.additionals = additionals;
    }

    public EntrustmentEvaluateVO getEntrustmentEvaluateVO() {
        return entrustmentEvaluateVO;
    }

    public void setEntrustmentEvaluateVO(EntrustmentEvaluateVO entrustmentEvaluateVO) {
        this.entrustmentEvaluateVO = entrustmentEvaluateVO;
    }
}
