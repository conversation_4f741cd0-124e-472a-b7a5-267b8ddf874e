package com.paic.ncbs.claim.controller.entrustment;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.service.entrustment.EntrustmentAuditService;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Api(tags = "第三方委托")
@RestController
@RequestMapping("/who/app/entrustmentAction")
public class EntrustmentController extends BaseController {

    @Autowired
    private EntrustmentService entrustmentService;

    @Autowired
    private EntrustmentAuditService entrustmentAuditService;

    @ApiOperation("提起委托")
    @PostMapping(value = "/addEntrustment")
    public ResponseResult<Object> addEntrustment(@RequestBody EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException {
        entrustmentService.initEntrustment(entrustmentApiVo);
        return ResponseResult.success();
    }

    @ApiOperation("查询委托详情")
    @PostMapping(value = "/getEntrustmentData")
    public ResponseResult<Object> getEntrustmentData(@RequestBody EntrustmentApiVo entrustmentApiVo) {
        String reportNo = entrustmentApiVo.getReportNo();
        Integer caseTimes = entrustmentApiVo.getCaseTimes();
        String initFlag = entrustmentApiVo.getInitFlag();

        // 本次的委托数据
        EntrustmentDTO currentEntrustment = entrustmentService.getCurrentEntrustment(reportNo, caseTimes);

        // 获取除本次以外的reportNo下所有历史委托数据
        List<EntrustmentDTO> historyEntrustments = entrustmentService.getHistoryEntrustments(reportNo, caseTimes, currentEntrustment.getIdEntrustment());

        // 查询历史审批信息
        List<EntrustmentAuditDTO> auditHistory = new ArrayList<>();
        if (currentEntrustment != null) {
            auditHistory = entrustmentAuditService.getAuditHistoryByEntrustmentId(currentEntrustment.getIdEntrustment());
        }

        if ("1".equals(initFlag)) {// 录入查询
            entrustmentApiVo.setEntrustmentDTO(currentEntrustment);
            entrustmentApiVo.setEntrustmentList(historyEntrustments);
            entrustmentApiVo.setAuditHistoryList(auditHistory);
        } else if ("2".equals(initFlag)) {// 审核查询
            entrustmentApiVo.setEntrustmentDTO(currentEntrustment);
            entrustmentApiVo.setEntrustmentList(historyEntrustments);
            entrustmentApiVo.setAuditHistoryList(auditHistory);
        }

        return ResponseResult.success(entrustmentApiVo);
    }

    @ApiOperation("委托次数")
    @GetMapping(value = "/getEntrustmentCount/{reportNo}/{caseTimes}")
    public ResponseResult<Integer> getEntrustmentCount(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(entrustmentService.getEntrustmentCount(reportNo, null));
    }

    @ApiOperation("查询事故场景")
    @GetMapping(value = "/getAccidentScenarios/{collectionCode}")
    public ResponseResult<List<AccidentSceneDto>> getAccidentSceneData(@ApiParam("参数类型码") @PathVariable("collectionCode") String collectionCode) {
        LogUtil.audit("#委托· 查询事故场景#入参#collectionCode=" + collectionCode);
        return ResponseResult.success(entrustmentService.getAccidentSceneData(collectionCode));
    }

    @ApiOperation("获取待审批列表")
    @GetMapping("/getPendingApprovalList")
    public ResponseResult<List<EntrustmentDTO>> getPendingApprovalList() {
        String approverUm = WebServletContext.getUserId();
        return ResponseResult.success(entrustmentService.getPendingApprovalList(approverUm));
    }

    @ApiOperation("获取打印委托列表")
    @GetMapping("/getEntrustmentForPrint")
    public ResponseResult<List<EntrustmentDTO>> getEntrustmentForPrint(
            @RequestParam(required = false) String reportNo) {
        return ResponseResult.success(entrustmentService.getEntrustmentForPrint(reportNo));
    }

    @ApiOperation("生成委托书PDF")
    @GetMapping("/generateEntrustmentPdf/{idAhcsEntrustment}")
    public ResponseResult<Map<String, String>> generateEntrustmentPdf(
            @ApiParam("委托ID") @PathVariable String idAhcsEntrustment) {
        return ResponseResult.success(entrustmentService.generateEntrustmentPdf(idAhcsEntrustment));
    }

    @ApiOperation("提交委托审批")
    @PostMapping(value = "/submitEntrustmentAudit")
    public ResponseResult<Object> submitEntrustmentAudit(@RequestBody EntrustmentAuditDTO entrustmentAudit) throws GlobalBusinessException {
        LogUtil.audit("#委托· 提交委托审批#入参#entrustmentAudit=" + entrustmentAudit);

        UserInfoDTO u = WebServletContext.getUser();
        entrustmentAudit.setUpdatedBy(u.getUserCode());
        entrustmentAudit.setAuditorUm(u.getUserCode());
        entrustmentAudit.setAuditorUmName(u.getUserName());

        entrustmentService.submitEntrustmentAudit(entrustmentAudit);

        return ResponseResult.success();
    }
}