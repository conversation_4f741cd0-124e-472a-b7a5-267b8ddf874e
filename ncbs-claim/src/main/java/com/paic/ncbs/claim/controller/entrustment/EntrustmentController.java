package com.paic.ncbs.claim.controller.entrustment;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentVO;
import com.paic.ncbs.claim.service.entrustment.EntrustmentAuditService;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "第三方委托")
@RestController
@RequestMapping("/who/app/entrustmentAction")
public class EntrustmentController extends BaseController {

    @Autowired
    private EntrustmentService entrustmentService;

    @Autowired
    private EntrustmentAuditService entrustmentAuditService;

    @ApiOperation("提起委托")
    @PostMapping(value = "/addEntrustment")
    public ResponseResult<Object> addEntrustment(@RequestBody EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException {
        entrustmentService.initEntrustment(entrustmentApiVo);
        return ResponseResult.success();
    }

    @ApiOperation("查询委托详情")
    @PostMapping(value = "/getEntrustmentData")
    public ResponseResult<Object> getEntrustmentData(@RequestBody EntrustmentApiVo entrustmentApiVo) {
        String reportNo = entrustmentApiVo.getReportNo();
        Integer caseTimes = entrustmentApiVo.getCaseTimes();
        String initFlag = entrustmentApiVo.getInitFlag();

        // 本次的委托数据
        EntrustmentDTO currentEntrustment = entrustmentService.getCurrentEntrustment(reportNo, caseTimes);

        // 获取除本次以外的reportNo下所有历史委托数据
        List<EntrustmentDTO> historyEntrustments = entrustmentService.getHistoryEntrustments(reportNo, caseTimes, currentEntrustment.getIdEntrustment());

        if ("1".equals(initFlag)) {// 录入查询
            entrustmentApiVo.setEntrustmentDTO(currentEntrustment);
            entrustmentApiVo.setEntrustmentList(historyEntrustments);
        } else if ("2".equals(initFlag)) {// 审核查询
            entrustmentApiVo.setEntrustmentDTO(currentEntrustment);
            entrustmentApiVo.setEntrustmentList(historyEntrustments);
        }

        return ResponseResult.success(entrustmentApiVo);
    }

    @ApiOperation("委托次数")
    @GetMapping(value = "/getEntrustmentCount/{reportNo}/{caseTimes}")
    public ResponseResult<Integer> getEntrustmentCount(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(entrustmentService.getEntrustmentCount(reportNo, null));
    }

    @ApiOperation("查询案件的历史委托信息")
    @GetMapping(value = "/getHistoryEntrustmentByReportNo/{reportNo}")
    public ResponseResult<List<EntrustmentVO>> getHistoryEntrustmentByReportNo(@PathVariable("reportNo") String reportNo) {
        LogUtil.audit("#委托·查询案件的历史委托信息#入参#reportNo=%s", reportNo);
        return ResponseResult.success(entrustmentService.getHistoryEntrustment(reportNo, null));
    }

    @ApiOperation("根据委托ID查询委托信息")
    @GetMapping(value = "/getEntrustmentById/{idAhcsEntrustment}")
    public ResponseResult<EntrustmentVO> getEntrustmentById(@PathVariable("idAhcsEntrustment") String idAhcsEntrustment) {
        LogUtil.audit("#委托·根据委托ID查询委托信息#入参#idAhcsEntrustment=%s", idAhcsEntrustment);
        return ResponseResult.success(entrustmentService.getEntrustmentById(idAhcsEntrustment));
    }

    @ApiOperation("查询事故场景")
    @GetMapping(value = "/getAccidentSceneData/{collectionCode}")
    public ResponseResult<List<AccidentSceneDto>> getAccidentSceneData(@ApiParam("参数类型码") @PathVariable("collectionCode") String collectionCode) {
        LogUtil.audit("#委托· 查询事故场景#入参#collectionCode=" + collectionCode);
        return ResponseResult.success(entrustmentService.getAccidentSceneData(collectionCode));
    }

    @ApiOperation("新增委托审批")
    @PostMapping(value = "/addEntrustmentAudit")
    public ResponseResult<Object> addEntrustmentAudit(@RequestBody EntrustmentAuditDTO entrustmentAudit) throws GlobalBusinessException {
        LogUtil.audit("#委托· 完成委托审批#入参#entrustmentAudit=" + entrustmentAudit);

        UserInfoDTO u = WebServletContext.getUser();
        entrustmentAudit.setCreatedBy(u.getUserCode());
        entrustmentAudit.setUpdatedBy(u.getUserCode());
        entrustmentAudit.setAuditorUm(u.getUserCode());

//        if (entrustmentAudit.getEntrustmentDepartment() == null || entrustmentAudit.getEntrustmentDepartment().isEmpty()) {
//            LogUtil.audit("#委托· 完成委托审批#入参#Department=" + WebServletContext.getDepartmentCode());
//            entrustmentAudit.setEntrustmentDepartment(WebServletContext.getDepartmentCode());
//        }

        entrustmentAuditService.addEntrustmentAudit(entrustmentAudit);
        return ResponseResult.success();
    }

    @ApiOperation("获取未完成的委托记录")
    @GetMapping(value = "/getNoFinishEntrustmentRecord/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int", dataTypeClass = Integer.class)
    })
    public ResponseResult<EntrustmentDTO> getNoFinishEntrustmentRecord(@PathVariable("reportNo") String reportNo,
                                                                       @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        return ResponseResult.success(entrustmentService.getNoFinishEntrustmentRecord(reportNo, caseTimes));
    }

    @ApiOperation("查询历史外部委托信息")
    @GetMapping(value = "/getHistoryOutEntrustmentByReportNo/{reportNo}")
    public ResponseResult<List<EntrustmentVO>> getHistoryOutEntrustmentByReportNo(@PathVariable("reportNo") String reportNo) {
        LogUtil.audit("#委托·查询历史外部委托信息#入参#reportNo=%s", reportNo);
        EntrustmentDTO entrustmentDTO = new EntrustmentDTO();
        entrustmentDTO.setReportNo(reportNo);
        return ResponseResult.success(entrustmentService.getHistoryOutEntrustment(entrustmentDTO));
    }

    @ApiOperation("获取待审批列表")
    @GetMapping("/getPendingApprovalList")
    public ResponseResult<List<EntrustmentDTO>> getPendingApprovalList() {
        String approverUm = WebServletContext.getUserId();
        return ResponseResult.success(entrustmentService.getPendingApprovalList(approverUm));
    }

    @ApiOperation("获取打印委托列表")
    @GetMapping("/getEntrustmentForPrint")
    public ResponseResult<List<EntrustmentDTO>> getEntrustmentForPrint(
            @RequestParam(required = false) String reportNo) {
        return ResponseResult.success(entrustmentService.getEntrustmentForPrint(reportNo));
    }

    @ApiOperation("生成委托书PDF")
    @GetMapping("/generateEntrustmentPdf/{idAhcsEntrustment}")
    public ResponseResult<Map<String, String>> generateEntrustmentPdf(
            @ApiParam("委托ID") @PathVariable String idAhcsEntrustment) {
        return ResponseResult.success(entrustmentService.generateEntrustmentPdf(idAhcsEntrustment));
    }
}