package com.paic.ncbs.claim.controller.user;

import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.user.GradeVO;
import com.paic.ncbs.claim.model.vo.user.PermissionTypeVO;
import com.paic.ncbs.claim.model.vo.user.PermissionUserVO;
import com.paic.ncbs.claim.model.vo.user.PermissionVO;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/app/permissionAction")
public class PermissionController {
    private static PermissionUserDTO userDTO;
    @Autowired
    private PermissionService permissionService;

    @GetMapping("/getTypeList")
    public ResponseResult<List<PermissionTypeVO>> getTypeList(){
        return ResponseResult.success(Constants.PERMISSION_TYPE_LIST);
    }

    @GetMapping("/getRoleTypeList")
    public ResponseResult<List<PermissionTypeVO>> getRoleTypeList(){
        return ResponseResult.success(Constants.PERMISSION_ROLE_LIST);
    }

    @GetMapping("/getGradeList")
    public ResponseResult<List<GradeVO>> getGradeList(@RequestParam("typeCode") String typeCode){

        return ResponseResult.success(permissionService.getUserGradeList(typeCode));
    }

    /**
     * 获取审批权限
     * @param typeCode
     * @param deptCode
     * @return
     */
    @GetMapping("/getPermissionList")
    public ResponseResult<PermissionVO> getPermissionList(@RequestParam("typeCode") String typeCode,
                                                          @RequestParam("deptCode") String deptCode){

        return ResponseResult.success(permissionService.getPermissionList(typeCode,deptCode));
    }

    /**
     * 保存审批权限
     * @param permissionVO
     * @return
     */
    @PostMapping("/addPermissionList")
    public ResponseResult addPermissionList(@RequestBody PermissionVO permissionVO){
        permissionService.addPermissionList(permissionVO);
        return ResponseResult.success();
    }

    @GetMapping("/getPermissionUserInfo")
    public ResponseResult<PermissionUserVO> getPermissionUserInfo(@RequestParam("userId") String userId){
        return ResponseResult.success(permissionService.getPermissionUserInfo(userId));
    }

    @GetMapping("/getRoleTypes")
    public ResponseResult<List<PermissionTypeVO>> getRoleTypes(@RequestParam("userId")String userId,@RequestParam("comCode")String comCode){
        return ResponseResult.success(permissionService.getRoleTypeList(userId, comCode));
    }
    @GetMapping("/getManageTypes")
    public ResponseResult<List<PermissionTypeVO>> getManageTypes(){
        String currentDeptCode = WebServletContext.getDepartmentCode();
        String userId = WebServletContext.getUserId();
        return ResponseResult.success(permissionService.getManageTypeList(userId, currentDeptCode));
    }

    /**
     * 保存用户权限
     * @param permissionUserVO
     * @return
     */
    @PostMapping("/addPermissionUser")
    public ResponseResult addPermissionUser(@RequestBody PermissionUserVO permissionUserVO){
        permissionService.addPermissionUser(permissionUserVO);
        return ResponseResult.success();
    }

    @GetMapping("/getPermissionSystemCom")
    public ResponseResult<List<SystemComInfoDTO>> getPermissionSystemCom(){
        return ResponseResult.success(permissionService.getPermissionSystemCom(WebServletContext.getUserId()));
    }

    @PostMapping("/getPermissionUserList")
    public ResponseResult getPermissionUserList(@RequestBody PermissionUserVO permissionUserVO){

        if(permissionUserVO == null || StringUtils.isEmptyStr(permissionUserVO.getComCode())){
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }

        List<PermissionUserDTO> list = permissionService.getPermissionUserList(permissionUserVO);
        PageInfo<PermissionUserDTO> pageInfo = new PageInfo<>(list);
        permissionUserVO.getPager().setTotalRows((int)pageInfo.getTotal());
        return ResponseResult.success(list,permissionUserVO.getPager());
    }

    @PostMapping("/updatePermissionUserList")
    public ResponseResult updatePermissionUserList(@RequestBody List<PermissionUserDTO> permissionUserList){

        if(ListUtils.isEmptyList(permissionUserList)){
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        permissionService.updatePermissionUserList(permissionUserList);
        return ResponseResult.success();
    }

    @GetMapping("/removePermissionUser")
    public ResponseResult removePermissionUser(@RequestParam("idClmsPermissionUser")String idClmsPermissionUser){

        if(StringUtils.isEmptyStr(idClmsPermissionUser)){
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        permissionService.removePermissionUser(idClmsPermissionUser);
        return ResponseResult.success();
    }

}
