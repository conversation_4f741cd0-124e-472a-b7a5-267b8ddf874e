package com.paic.ncbs.claim.model.dto.report;

public class CopyPolicyQueryVO {

    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 出险时间
     */
    private String time;
    /**
     * ply_risk_person表id
     */
    private String riskPersonId;
    /**
     * 证件号
     */
    private String certNo;
    /**
     * 姓名，通常是被保人姓名
     */
    private String clientName;

    /**
     * 客户号
     */
    private String clientNo;
    private String productKind = "0";
    private String isElecSubPolicyNo = "0";
    private String umsDisplayFlag = "claim";

    // 支付方式- 区分支付给谁 ,废弃，用paymentCompanyMode替换
    private String paymentMode ;

    public String getPaymentCompanyMode() {
        return paymentCompanyMode;
    }

    public void setPaymentCompanyMode(String paymentCompanyMode) {
        this.paymentCompanyMode = paymentCompanyMode;
    }

    //支付方式- 区分支付给谁
    private String paymentCompanyMode;
    /**
     * 被保人名字 抄单入参用
     */
    private String name;
    /**
     * 证件类型 抄单入参用
     */
    private String certificateType;
    /**
     * 证件号 抄单入参用
     */
    private String certificateNo;

    public CopyPolicyQueryVO() {
    }

    public CopyPolicyQueryVO(String policyNo, String time) {
        this.policyNo = policyNo;
        this.time = time;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getRiskPersonId() {
        return riskPersonId;
    }

    public void setRiskPersonId(String riskPersonId) {
        this.riskPersonId = riskPersonId;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    public String getProductKind() {
        return productKind;
    }

    public void setProductKind(String productKind) {
        this.productKind = productKind;
    }

    public String getIsElecSubPolicyNo() {
        return isElecSubPolicyNo;
    }

    public void setIsElecSubPolicyNo(String isElecSubPolicyNo) {
        this.isElecSubPolicyNo = isElecSubPolicyNo;
    }

    public String getUmsDisplayFlag() {
        return umsDisplayFlag;
    }

    public void setUmsDisplayFlag(String umsDisplayFlag) {
        this.umsDisplayFlag = umsDisplayFlag;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }
}
