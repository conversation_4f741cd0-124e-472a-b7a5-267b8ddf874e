package com.paic.ncbs.claim.common.constant;

import com.paic.ncbs.claim.common.enums.UwConclusionTypeEnum;

import java.util.HashMap;
import java.util.Map;

public class SecondUwConstant {

    //核保结论映射TPA中台的核保结论
    public final static Map<String,String> SECOND_UW_CONCLUSION_MAP=new HashMap<>();
    static{
        SECOND_UW_CONCLUSION_MAP.put(UwConclusionTypeEnum.TYPE_ONE.getCode(),"7");
        SECOND_UW_CONCLUSION_MAP.put(UwConclusionTypeEnum.TYPE_TWO.getCode(),"8");
        SECOND_UW_CONCLUSION_MAP.put(UwConclusionTypeEnum.TYPE_THREE.getCode(),"9");
    }
}
