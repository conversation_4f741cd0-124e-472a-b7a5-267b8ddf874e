package com.paic.ncbs.claim.dao.mapper.blacklist;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListAudit;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListAuditVO;

import java.util.List;

/**
 * <p>
 * 黑名单审批记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ClmsBlackListAuditMapper extends BaseMapper<ClmsBlackListAudit> {

    List<ClmsBlackListAuditVO> getPendingAudits();

    ClmsBlackListAudit getBlackListAuditById(String  id);

    void saveClmsBlackListAudit(ClmsBlackListAudit clmsBlackListAudit);

    void updateAuditStatus(ClmsBlackListAudit clmsBlackListAudit);

    ClmsBlackListAuditVO getPendingAuditByBlackListId(String blackListId);



}
