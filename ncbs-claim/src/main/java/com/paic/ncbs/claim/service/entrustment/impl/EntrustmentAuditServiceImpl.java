package com.paic.ncbs.claim.service.entrustment.impl;

import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import com.paic.ncbs.claim.service.entrustment.EntrustmentAuditService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 委托审批服务实现类
 */
@Slf4j
@Service
public class EntrustmentAuditServiceImpl implements EntrustmentAuditService {

    @Autowired
    private EntrustmentAuditMapper entrustmentAuditMapper;


    @Override
    public List<EntrustmentAuditDTO> getAuditHistoryByEntrustmentId(String idEntrustment) {
        return entrustmentAuditMapper.selectByEntrustmentId(idEntrustment);
    }


}