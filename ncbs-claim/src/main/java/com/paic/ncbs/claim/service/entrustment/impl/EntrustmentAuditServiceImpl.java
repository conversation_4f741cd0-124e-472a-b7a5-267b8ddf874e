package com.paic.ncbs.claim.service.entrustment.impl;

import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import com.paic.ncbs.claim.service.entrustment.EntrustmentAuditService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.UUID;

/**
 * 委托审批服务实现类
 */
@Slf4j
@Service
public class EntrustmentAuditServiceImpl implements EntrustmentAuditService {

    @Autowired
    private EntrustmentMapper entrustmentMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEntrustmentAudit(EntrustmentAuditDTO entrustmentAudit) throws GlobalBusinessException {
        // 实现添加委托审批的逻辑
        log.info("开始添加委托审批，委托ID: {}", entrustmentAudit.getIdAhcsEntrustment());
        
        // 1. 验证输入参数
        if (entrustmentAudit == null) {
            throw new GlobalBusinessException("委托审批信息不能为空");
        }
        
        if (entrustmentAudit.getIdAhcsEntrustment() == null || entrustmentAudit.getIdAhcsEntrustment().isEmpty()) {
            throw new GlobalBusinessException("委托ID不能为空");
        }
        
        // 2. 设置主键和基础字段
        if (entrustmentAudit.getIdAhcsEntrustmentAudit() == null || entrustmentAudit.getIdAhcsEntrustmentAudit().isEmpty()) {
            entrustmentAudit.setIdAhcsEntrustmentAudit(UUID.randomUUID().toString());
        }
        
        Date now = new Date();
        if (entrustmentAudit.getCreatedDate() == null) {
            entrustmentAudit.setCreatedDate(now);
        }
        entrustmentAudit.setUpdatedDate(now);
        
        // 3. 保存委托审批信息到数据库
        log.info("保存委托审批信息到数据库，审批ID: {}", entrustmentAudit.getIdAhcsEntrustmentAudit());
//        entrustmentAuditMapper.insertEntrustmentAudit(entrustmentAudit); // 使用EntrustmentAuditMapper保存数据
        
        // 4. 根据审批类型执行不同的业务逻辑
        if ("01".equals(entrustmentAudit.getAuditType())) {
            // 异地委托审批
            log.info("处理异地委托审批");
            // 更新委托状态等操作
//            handleOffsiteEntrustmentAudit(entrustmentAudit);
        } else if ("02".equals(entrustmentAudit.getAuditType())) {
            // 发起委托审批
            log.info("处理发起委托审批");
            // 更新委托状态等操作
//            handleInitiateEntrustmentAudit(entrustmentAudit);
        } else {
            log.warn("未知的审批类型: {}", entrustmentAudit.getAuditType());
        }
        
        // 5. 更新相关状态或发送通知等
//        updateEntrustmentStatus(entrustmentAudit);
        
        log.info("委托审批添加完成，委托ID: {}", entrustmentAudit.getIdAhcsEntrustment());
    }

    @Override
    public void addOffSiteEntrustment(OffSiteEntrustmentVO offSiteEntrustmentVO, UserInfoDTO u) throws GlobalBusinessException {
        // 实现添加异地委托的逻辑
        log.info("开始添加异地委托，报案号: {}", offSiteEntrustmentVO.getReportNo());
        
        // TODO: 添加具体的业务逻辑实现
        // 1. 验证输入参数
        if (offSiteEntrustmentVO == null || u == null) {
            throw new GlobalBusinessException("异地委托信息或用户信息不能为空");
        }
        
        // 2. 设置创建人和更新人信息
        offSiteEntrustmentVO.setCreatedBy(u.getUserCode());
        offSiteEntrustmentVO.setUpdatedBy(u.getUserCode());
        
        // 3. 保存异地委托信息到数据库
        // 这里需要调用DAO或Mapper来保存数据
        
        // 4. 执行其他相关业务逻辑
        
        log.info("异地委托添加完成，报案号: {}", offSiteEntrustmentVO.getReportNo());
    }
}