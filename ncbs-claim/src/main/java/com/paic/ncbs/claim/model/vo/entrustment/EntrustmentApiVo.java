package com.paic.ncbs.claim.model.vo.entrustment;

import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustmentAuditDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 委托API请求VO
 */
@Data
@ApiModel("委托API请求VO")
public class EntrustmentApiVo {

    /**
     * 页面初始化标志 1代表录入页面初始化，2代表审核页面初始化
     */
    @ApiModelProperty("页面初始化标志 1代表录入页面初始化，2代表审核页面初始化")
    private String initFlag;

    /**
     * 报案号
     */
    @ApiModelProperty("报案号")
    private String reportNo;

    /**
     * 赔付次数
     */
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    /**
     * 保存或者提交标志
     */
    @ApiModelProperty("保存或者提交标志")
    private String submitFlag;

    /**
     * 操作标志C-新增，U-修改，D-删除，V-查看
     */
    @ApiModelProperty("操作标志C-新增，U-修改，D-删除，V-查看")
    private EntrustmentDTO entrustmentDTO;

    /**
     * 委托列表
     */
    @ApiModelProperty("委托列表")
    private List<EntrustmentDTO> entrustmentList;

    /**
     * 委托审核DTO
     */
    @ApiModelProperty("委托审核DTO")
    private EntrustmentAuditDTO entrustmentAuditDTO;

    /**
     * 委托审核列表
     */
    @ApiModelProperty("委托审核列表")
    private List<EntrustmentAuditDTO> entrustmentAuditList;

    /**
     * 审批历史列表
     */
    @ApiModelProperty("审批历史列表")
    private List<EntrustmentAuditDTO> auditHistoryList;
}