package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.dto.ReplevyMainQueryDTO;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyDetailVo;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo;
import com.paic.ncbs.claim.replevy.vo.ReplevyApiVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 *
 * 表clms_replevy_main对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface ClmsReplevyMainMapper extends BaseDao<ClmsReplevyMain> {

    /**
     * 根据报案号查询主表的序号
     */
    public ClmsReplevyMain selectSerialNo(String reportNo);

    /**
     * 查询主表数据--根据报案号，赔付次数，有效标志,追偿案件号
     * @param clmsReplevyMainVo
     * @return
     */
    public ClmsReplevyMainVo  selectClmsReplevyMain(ClmsReplevyMainVo clmsReplevyMainVo);
    /**
     * @param reportNo 报案号
     * @param replevyNo 追偿案件号
     * @return
     */
    public ClmsReplevyMainVo  selectReplevyMain(@Param("reportNo") String reportNo,@Param("replevyNo") String replevyNo,@Param("replevyId") String replevyId);
    /**
     * 查询追偿信息
     * @return
     */
    List<ClmsReplevyMainVo> selectClmsReplevyMainList(@Param("reportNo") String reportNo, @Param("replevyNo") String replevyNo);

    /**
     * 更新总追偿收入金额
     * @param replevyId 追偿主表ID
     * @return 更新结果
     */
    int updateSumRealReplevy(@Param("replevyId") String replevyId, @Param("sumRepleviedMoney") BigDecimal sumRepleviedMoney, @Param("sumChargeMoney") BigDecimal sumChargeMoney);

    /**
     * 减少总追偿费用金额
     * @param replevyId 追偿主表ID
     * @param chargeMoney 要减少的费用金额
     * @return 更新结果
     */
    int decreaseSumReplevyFee(@Param("replevyId") String replevyId, @Param("chargeMoney") BigDecimal chargeMoney);

    /**
     * 减少总追偿收入金额
     * @param replevyId 追偿主表ID
     * @param repleviedMoney 要减少的收入金额
     * @return 更新结果
     */
    int decreaseSumRealReplevy(@Param("replevyId") String replevyId, @Param("repleviedMoney") BigDecimal repleviedMoney);

    /**
     * 根据报案号查询追偿主表信息
     * @param reportNo 报案号
     * @return 追偿主表查询结果集合
     */
    List<ReplevyMainQueryDTO> queryReplevyMainByReportNo(@Param("reportNo") String reportNo);
    ClmsReplevyMainVo selectNotFinished(@Param("reportNo") String reportNo);

}