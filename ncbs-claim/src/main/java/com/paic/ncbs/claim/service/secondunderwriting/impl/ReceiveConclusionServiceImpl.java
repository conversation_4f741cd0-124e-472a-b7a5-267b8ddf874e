package com.paic.ncbs.claim.service.secondunderwriting.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.LettersCancelStatusEnum;
import com.paic.ncbs.claim.common.enums.SendUwStatusEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUwLetterDTO;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPlanConclusionEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPolicyConclusionEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.TPAFeign;
import com.paic.ncbs.claim.model.dto.policy.PolicyConclusionDTO;
import com.paic.ncbs.claim.model.dto.policy.RiskConclusionDTO;
import com.paic.ncbs.claim.model.dto.policy.UwLetterDTO;
import com.paic.ncbs.claim.model.dto.policy.UwRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseResponseDTO;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.model.vo.senconduw.LetterSellBackSubmitDTO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.secondunderwriting.*;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.paic.ncbs.claim.common.constant.SecondUwConstant.SECOND_UW_CONCLUSION_MAP;


/**
 * 接收核保结论业务实现
 */
@Service
public class ReceiveConclusionServiceImpl implements ReceiveConclusionService {

    @Autowired
    private ClmsSecondUnderwritingService clmsSecondUnderwritingService;
    @Autowired
    private ClmsSeconduwPolicyConclusionService clmsSeconduwPolicyConclusionService;

    @Autowired
    private ClmsSeconduwPlanConclusionService clmsSeconduwPlanConclusionService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private ClmsSecondUwLetterService clmsSecondUwLetterService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private TPAFeign tpaFegin;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private IOperationRecordService operationRecordService;


    public ReceiveConclusionServiceImpl() {
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveConclusion(UwRequestDTO uwRequestDTO) {
        LogUtil.audit("接收核保结论入参={}", JsonUtils.toJsonString(uwRequestDTO));
        //0：重要必填入参校验
        checkInputData(uwRequestDTO);
        //1报案号和赔付次数是拼接在一起的在这里处理一下
        getReportNo(uwRequestDTO);
        //2:组装实体数据对象，用于更新clms_second_underwriting表记录数据
        ClmsSecondUnderwritingEntity entity = setClmsSecondUwVaule(uwRequestDTO);
        //组装函件信息
        List<ClmsSecondUwLetterDTO> dtos = setLetterValue(uwRequestDTO,entity.getId());
        //3:将请求参数转换封装为对应的entity对象，用于插入保单，险种 的核保结论表
        setuwPolicyConclusionValue(uwRequestDTO,entity.getId());
        //4更新更新理赔二核申请记录表clms_second_underwriting
        clmsSecondUnderwritingService.update(entity);
        //5保存函件信息集合
        if(CollectionUtil.isNotEmpty(dtos)){
            clmsSecondUwLetterService.saveBatch(dtos);
        }
        //6：保存保单层级的核保结论信息 插入表 clms_seconduw_policy_conclusion
        clmsSeconduwPolicyConclusionService.saveBatch(uwRequestDTO.getPolicEntityList());
        //7:保存险种层级的核保结论信息 插入表 clms_seconduw_plan_conclusion
        clmsSeconduwPlanConclusionService.saveBatch(uwRequestDTO.getRiskEntityList());
        //8将理赔二核工作流完成
        bpmService.completeTask_oc_css(uwRequestDTO.getReportNo(), uwRequestDTO.getCaseTimes(), BpmConstants.OC_CLAIM_SECOND_UNDERWRITING);
        //9将挂起的工作流重新打开
        bpmService.suspendOrActiveTask_oc_css(uwRequestDTO.getReportNo(), uwRequestDTO.getCaseTimes(), entity.getTaskCode(), false);
        //10更新案件流程表状态
        caseProcessService.updateCaseProcess(uwRequestDTO.getReportNo(), uwRequestDTO.getCaseTimes(), Transform.getCaseProcessStatus(entity.getTaskCode()));
        //操作记录
        operationRecordService.insertOperationRecord(uwRequestDTO.getReportNo(), BpmConstants.OC_CLAIM_SECOND_UNDERWRITING, "提交", null, WebServletContext.getUserIdForLog());
        //TPA全流程案件，调用TPA答复接口回复
        List<ReportInfoExEntity>  reportInfos = reportInfoExMapper.getReportInfoEx(uwRequestDTO.getReportNo());
        ReportInfoExEntity reportInfo = reportInfos.get(0);
        if("1".equals(reportInfo.getClaimDealWay())
                && !"channel".equals(reportInfo.getCompanyId())
                && 1 == uwRequestDTO.getCaseTimes()) {
            //默认回销函件
            if(CollectionUtil.isNotEmpty(dtos)){
                for(ClmsSecondUwLetterDTO uwLetterDTO : dtos){
                    if(uwLetterDTO.getFileType().equals(BaseConstant.STRING_2)){
                        LetterSellBackSubmitDTO letterSellBackSubmitDTO = new LetterSellBackSubmitDTO();
                        letterSellBackSubmitDTO.setCaseTimes(uwRequestDTO.getCaseTimes());
                        letterSellBackSubmitDTO.setReportNo(uwRequestDTO.getReportNo());
                        letterSellBackSubmitDTO.setManualInfoId(uwRequestDTO.getManualInfoId());
                        letterSellBackSubmitDTO.setIdSecondUnderwriting(entity.getId());
                        List<LetterSellBackSubmitDTO.LetterInfoDTO> letterInfoList = new ArrayList<>();
                        LetterSellBackSubmitDTO.LetterInfoDTO letterInfoDTO = new LetterSellBackSubmitDTO.LetterInfoDTO();
                        letterInfoDTO.setIdSecondUWLetter(uwLetterDTO.getId());
                        letterInfoDTO.setFileType(uwLetterDTO.getFileType());
                        letterInfoDTO.setLetterConclusion(BaseConstant.STRING_01);
                        letterInfoDTO.setLetterExplain("同意");
                        letterInfoDTO.setIdLetterInfo(uwLetterDTO.getIdLetterInfo());
                        letterInfoList.add(letterInfoDTO);
                        letterSellBackSubmitDTO.setLetterInfoList(letterInfoList);
                        LogUtil.audit("TPA案件：{}核保函自动回销，回销参数：{}", uwRequestDTO.getReportNo(), JSON.toJSONString(letterSellBackSubmitDTO));
                        clmsSecondUwLetterService.letterSellBack(letterSellBackSubmitDTO);
                    }
                }
            }

            String underWritingTaskId = taskInfoMapper.getLatestTaskId(uwRequestDTO.getReportNo(), uwRequestDTO.getCaseTimes(),BpmConstants.OC_CLAIM_SECOND_UNDERWRITING);
            ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
            problemCaseRequestDTO.setCompanyId(reportInfo.getCompanyId());
            RequestData requestData = new RequestData();
            requestData.setRegistNo(uwRequestDTO.getReportNo());
            requestData.setProblemNo(underWritingTaskId);
            requestData.setProblemType("06");
            requestData.setCaseConclusion(SECOND_UW_CONCLUSION_MAP.get(uwRequestDTO.getPolicEntityList().get(0).getUwConclusion()));
            try {
                requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
            } catch (ParseException e) {
                LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
            }
            requestData.setCaseConclusionDetail(null);
            requestData.setRemark(uwRequestDTO.getReason());
            problemCaseRequestDTO.setRequestData(requestData);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",uwRequestDTO.getReportNo(), JSON.toJSONString(problemCaseRequestDTO));
            ProblemCaseResponseDTO response = tpaFegin.response(problemCaseRequestDTO);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",uwRequestDTO.getReportNo(), JSON.toJSONString(response));
        }

    }

    /**
     * 组装函件信息
     * @param uwRequestDTO
     */
    private List<ClmsSecondUwLetterDTO> setLetterValue(UwRequestDTO uwRequestDTO,String idClmsSecondUnderwriting) {
        if(CollectionUtil.isEmpty(uwRequestDTO.getLetterInfoList())){
           return new ArrayList<ClmsSecondUwLetterDTO>() ;
        }
        Date date = new Date();
        List<ClmsSecondUwLetterDTO> dtos = new ArrayList<>();
        for (UwLetterDTO uwdto :uwRequestDTO.getLetterInfoList()) {
            ClmsSecondUwLetterDTO dto = new ClmsSecondUwLetterDTO();
            dto.setId(UuidUtil.getUUID());
            dto.setUserId(WebServletContext.getUserId());
            dto.setFileId(uwdto.getFiledId());
            //2-核保函件，99-其他函件
            dto.setFileType(uwdto.getFileType());
            dto.setIdLetterInfo(uwdto.getIdLetterInfo());
            dto.setReportNo(uwRequestDTO.getReportNo());
            dto.setCaseTimes(uwRequestDTO.getCaseTimes());
            dto.setIdClmsSecondUnderwriting(idClmsSecondUnderwriting);
            dto.setLetterSendDate(date);
            dto.setCreatedBy(WebServletContext.getUserId());
            dto.setCreatedDate(date);
            dto.setUpdatedBy(WebServletContext.getUserId());
            dto.setUpdatedDate(date);
            dtos.add(dto);
        }
        return dtos;
    }

    /**
     * 模拟数据给测试用
     * @return
     */
    private List<ClmsSecondUwLetterDTO> setTestData(String reportNo,String idClmsSecondUnderwriting) {
        String filedId="https://dsuprivate-1313541860.cos.ap-shanghai-fsi.myqcloud.com/ncbs-claim/169389052657664479398081000000001000026_b0a39.png";
        String type="png";
        Date date = new Date();
        List<ClmsSecondUwLetterDTO> dtos = new ArrayList<>();
        ClmsSecondUwLetterDTO dto = new ClmsSecondUwLetterDTO();
        dto.setId(UuidUtil.getUUID());
        dto.setUserId(WebServletContext.getUserId());
        dto.setFileId(filedId);
        dto.setFileType(type);
        dto.setIdLetterInfo("15c012cab90c4f8780729a368069e058");
        dto.setReportNo(reportNo);
        dto.setCaseTimes(1);
        dto.setIdClmsSecondUnderwriting(idClmsSecondUnderwriting);
        dto.setLetterSendDate(date);
        dto.setCreatedBy(WebServletContext.getUserId());
        dto.setCreatedDate(date);
        dto.setUpdatedBy(WebServletContext.getUserId());
        dto.setUpdatedDate(date);
        dtos.add(dto);
        return dtos;
    }

    /**
     * 设置组装 保单层核保结论信息
     * 和险种层级的核保结论信息
     * @param uwRequestDTO
     * @return
     */
    private void  setuwPolicyConclusionValue(UwRequestDTO uwRequestDTO,String idClmsSecondUnderwriting) {
        List<PolicyConclusionDTO> dtoList =  uwRequestDTO.getPolicyList();
        //保单层级核保结论组装后的实体集合
        List<ClmsSeconduwPolicyConclusionEntity> list = new ArrayList<>();
        //险种层级核保结论组装后的实体集合
        List<ClmsSeconduwPlanConclusionEntity> entityList = new ArrayList<>();
        Date date =new Date();
        for (PolicyConclusionDTO dto : dtoList) {
            ClmsSeconduwPolicyConclusionEntity entity = new ClmsSeconduwPolicyConclusionEntity();
            entity.setId(UuidUtil.getUUID());
            entity.setIdClmsSecondUnderwriting(idClmsSecondUnderwriting);
            entity.setReportNo(uwRequestDTO.getReportNo());
            entity.setCaseTimes(uwRequestDTO.getCaseTimes());
            entity.setPolicyNo(dto.getPolicyNo());
            entity.setApplyName(dto.getApplicantName());
            entity.setInsuredName(dto.getInsuredName());
            entity.setProductName(dto.getProductName());
            entity.setSchemeName(dto.getRiskGroupName());
            entity.setPolicyStatus(dto.getPolicyStatus());
            entity.setUwExceptions(dto.getManualConclusion());
            entity.setUwConclusion(dto.getManualResult());

            try {
                entity.setInsuranceBeginDate(DateUtils.parseToFormatDate(dto.getInsuranceBeginDate(),DateUtils.FULL_DATE_STR));
                entity.setInsuranceEndDate(DateUtils.parseToFormatDate(dto.getInsuranceEndDate(),DateUtils.FULL_DATE_STR));
            } catch (ParseException e) {
                throw new GlobalBusinessException("保单起始日期转换异常");
            }
            entity.setCreatedDate(date);
            entity.setUpdatedDate(date);
            entity.setCreatedBy(Constants.SYSTEM_USER);
            entity.setUpdatedBy(Constants.SYSTEM_USER);
            list.add(entity);
            //组装险种层级的
            setRiskConclusionValue(entity,dto,entityList);

        }
        uwRequestDTO.setRiskEntityList(entityList);
        uwRequestDTO.setPolicEntityList(list);
    }

    /**
     * 封装险种层级核保结论数据
     * @param inEntity
     * @param dto
     * @param
     */
    private void setRiskConclusionValue(ClmsSeconduwPolicyConclusionEntity inEntity, PolicyConclusionDTO dto,List<ClmsSeconduwPlanConclusionEntity> entityList) {

        for (RiskConclusionDTO riskDto: dto.getRiskConclusionList()) {
            ClmsSeconduwPlanConclusionEntity entity = new ClmsSeconduwPlanConclusionEntity();
            BeanUtils.copyProperties(riskDto,entity);
            entity.setId(UuidUtil.getUUID());
            entity.setIdClmsSeconduwPolicyConclusion(inEntity.getId());
            entity.setPolicyNo(dto.getPolicyNo());
            entity.setUwExceptions(riskDto.getUnderwritingInstructions());
            entity.setUwDecisions(riskDto.getUwsResult());
            entity.setCreatedDate(new Date());
            entity.setUpdatedDate(new Date());
            entity.setCreatedBy(Constants.SYSTEM_USER);
            entity.setUpdatedBy(Constants.SYSTEM_USER);
            entity.setReportNo(inEntity.getReportNo());
            entity.setCaseTimes(inEntity.getCaseTimes());
            try {
                entity.setInsuranceBeginDate(DateUtils.parseToFormatDate(riskDto.getInsuranceBeginDate(),DateUtils.FULL_DATE_STR));
                entity.setInsuranceEndDate(DateUtils.parseToFormatDate(riskDto.getInsuranceEndDate(),DateUtils.FULL_DATE_STR));
            } catch (Exception e){
                throw new GlobalBusinessException("保单险种起始日期转换异常");
            }

            entityList.add(entity);
        }
    }


    /**
     * 组装申请记录信息 用于更新
     * @param uwRequestDTO
     * @return
     */
    private ClmsSecondUnderwritingEntity setClmsSecondUwVaule(UwRequestDTO uwRequestDTO) {
        ClmsSecondUnderwritingEntity entity = new ClmsSecondUnderwritingEntity();
        //1:先根据报案号和赔付次数查询申请记录表中送核状态为01-送核审批中 的数据
        ClmsSecondUnderwritingEntity oldEntity = clmsSecondUnderwritingService.getUWRecord(uwRequestDTO.getReportNo(),uwRequestDTO.getCaseTimes());
        if(ObjectUtil.isEmpty(oldEntity)){
            throw new GlobalBusinessException("报案号："+uwRequestDTO.getReportNo()+"已完成核保结论接收，请勿重复推送");
        }
        BeanUtils.copyProperties(oldEntity,entity);
        //核保意见
        entity.setUwAdvice(uwRequestDTO.getReason());
        //核保人员
        entity.setUwOperator(uwRequestDTO.getUmCode());
        //核保任务号
        entity.setManualInfoId(uwRequestDTO.getManualInfoId());
        //送核状态
        entity.setUnderwritingStatus(SendUwStatusEnum.STATUS_02.getCode());
        //核保完成时间
        entity.setUwCompleteDate(new Date());
        entity.setUpdatedBy(WebServletContext.getUserId());
        entity.setUpdatedDate(new Date());
        entity.setConclusion(uwRequestDTO.getConclusion());
        //函件回销状态
        if(CollectionUtil.isEmpty(uwRequestDTO.getLetterInfoList())){
            entity.setLettersCancelStatus(LettersCancelStatusEnum.STATUS_ENUM_01.getCode());
        }else{
            entity.setLettersCancelStatus(LettersCancelStatusEnum.STATUS_ENUM_02.getCode());
        }



        return entity;
    }

    /**
     * 分割报案号
     * @param uwRequestDTO
     */
    private void getReportNo(UwRequestDTO uwRequestDTO) {
       try {
           List<String> stringList = StringUtils.getListWithSeparator(uwRequestDTO.getReportNo(),Constants.SEPARATOR_LINE);
           uwRequestDTO.setReportNo(stringList.get(0));
           String casetimes = stringList.get(1);
           uwRequestDTO.setCaseTimes(Integer.valueOf(casetimes));
       }catch (Exception e){
           throw new GlobalBusinessException("reportNo格式不正确:"+uwRequestDTO.getReportNo());
       }
    }
    /**
     * 重要必填入参校验
     * @param uwRequestDTO
     */
    private void checkInputData(UwRequestDTO uwRequestDTO) {
        if(StrUtil.isEmpty(uwRequestDTO.getReportNo())){
            throw new GlobalBusinessException("报案号reportNo不能为空");
        }
        if(CollectionUtil.isEmpty(uwRequestDTO.getPolicyList())){
            throw new GlobalBusinessException("保单层级核保信息policyList不能为空");
        }
        if(StrUtil.isEmpty(uwRequestDTO.getUmCode())){
            throw new GlobalBusinessException("核保人员不能为空");
        }
        if(CollectionUtil.isNotEmpty(uwRequestDTO.getLetterInfoList())){
            for (UwLetterDTO letterDTO : uwRequestDTO.getLetterInfoList()) {
                if(StringUtils.isEmptyStr(letterDTO.getFiledId())){
                    throw new GlobalBusinessException("fileid不能为空");
                }
                if(StringUtils.isEmptyStr(letterDTO.getFileType())){
                    throw new GlobalBusinessException("函件类型fileType不能为空");
                }
                if(StringUtils.isEmptyStr(letterDTO.getIdLetterInfo())){
                    throw new GlobalBusinessException("函件id不能为空");
                }
            }
        }
        for (PolicyConclusionDTO dto :uwRequestDTO.getPolicyList()) {
            if(CollectionUtil.isEmpty(dto.getRiskConclusionList())){
                throw new GlobalBusinessException("险种层级核保信息riskConclusionList不能为空");
            }
            if(StrUtil.isEmpty(dto.getPolicyNo())){
                throw new GlobalBusinessException("保单号不能为空");
            }
            if(StrUtil.isEmpty(dto.getApplicantName())){
                throw new GlobalBusinessException("投保人不能为空");
            }
            if(StrUtil.isEmpty(dto.getInsuredName())){
                throw new GlobalBusinessException("被保险人不能为空");
            }
            if(StrUtil.isEmpty(dto.getRiskGroupName())){
                throw new GlobalBusinessException("方案名称不能为空");
            }
            if(StrUtil.isEmpty(dto.getProductName())){
                throw new GlobalBusinessException("产品名称不能为空");
            }
            if(StrUtil.isEmpty(dto.getProductCode())){
                throw new GlobalBusinessException("产品编码不能为空");
            }
            if(StrUtil.isEmpty(dto.getManualResult())){
                throw new GlobalBusinessException("保单层核保结论不能为空");
            }
            if(StrUtil.isEmpty(dto.getInsuranceBeginDate())){
                throw new GlobalBusinessException("保单起始日期不能为空");
            }
            if(StrUtil.isEmpty(dto.getInsuranceEndDate())){
                throw new GlobalBusinessException("保单止期不能为空");
            }
//            if(StrUtil.isEmpty(dto.getPolicyStatus())){
//                throw new GlobalBusinessException("保单状态不能为空");
//            }
            for (RiskConclusionDTO riskDto : dto.getRiskConclusionList()) {
                if(StrUtil.isEmpty(riskDto.getPlanName())){
                    throw new GlobalBusinessException("险种名称不能为空");
                }
                if(StrUtil.isEmpty(riskDto.getPlanCode())){
                    throw new GlobalBusinessException("险种编码不能为空");
                }
                if(StrUtil.isEmpty(riskDto.getUwsResult())){
                    throw new GlobalBusinessException("险种核保结论不能为空");
                }
//                if(StrUtil.isEmpty(riskDto.getPlanStatus())){
//                    throw new GlobalBusinessException("保单险种状态不能为空");
//                }
                if(StrUtil.isEmpty(riskDto.getInsuranceBeginDate())){
                    throw new GlobalBusinessException("险种起期不能为空");
                }
                if(StrUtil.isEmpty(riskDto.getInsuranceEndDate())){
                    throw new GlobalBusinessException("险种止期不能为空");
                }
            }

        }

    }
}
