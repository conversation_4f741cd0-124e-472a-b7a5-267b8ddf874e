package com.paic.ncbs.claim.common.enums;

import lombok.Getter;

@Getter
public enum SyncCaseStatusEnum {

    REGISTER("REGISTER", "立案"),
    CANCEL("CANCEL", "立案注销"),
    ENDCASE("ENDCASE", "已结案"),
    PAYFAILD("PAYFAILD", "支付失败"),
    PAY("PAY", "已支付"),
    ADDMEDIA("ADDMEDIA", "补充材料"),
    COMMUNICATE("COMMUNICATE", "发起沟通"),
    ENDCOMMUNICATE("ENDCOMMUNICATE", "沟通结束"),
    INVESTIGATE("INVESTIGATE", "发起调查"),
    ENDINVESTIGATE("E<PERSON>INVESTIGATE", "调查结束");

    private final String type;
    private final String name;

    SyncCaseStatusEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }
}
