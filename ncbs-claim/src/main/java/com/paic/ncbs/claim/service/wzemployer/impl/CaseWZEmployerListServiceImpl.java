package com.paic.ncbs.claim.service.wzemployer.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.wzemployer.CaseWZEmployerListEntity;
import com.paic.ncbs.claim.dao.entity.wzemployer.WZListUploadRecordEntity;
import com.paic.ncbs.claim.dao.mapper.wzemployer.CaseWZEmployerListMapper;
import com.paic.ncbs.claim.dao.mapper.wzemployer.WZListUploadRecordMapper;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.wzemployer.CaseWZEmployerListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@RefreshScope
@Service("caseWZEmployerListService")
public class CaseWZEmployerListServiceImpl implements CaseWZEmployerListService {

    @Autowired
    private CaseWZEmployerListMapper caseWZEmployerListMapper;
    @Autowired
    private WZListUploadRecordMapper wzListUploadRecordMapper;
    @Autowired
    private IOBSFileUploadService ioBSFileUploadService;
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

    /**
     * 生成微众雇责理赔清单
     *
     * @param uploadDate 上传日期，格式为"yyyyMMdd"
     */
    public void createWZEmployerList(String uploadDate) {
        if (StringUtils.isBlank(uploadDate)) {
            uploadDate = dateFormat.format(new Date());
        }
        log.info("开始生成微众雇责理赔清单");
        if(null != uploadDate && !"".equals(uploadDate) && (uploadDate.length() != 8 || !uploadDate.matches("\\d+"))){
            throw new RuntimeException("输入的日期格式不对,请输入正确的日期!例:20250721！");
        }
        List<CaseWZEmployerListEntity> caseWZEmployerListEntities = caseWZEmployerListMapper.selectList(new LambdaQueryWrapper<CaseWZEmployerListEntity>().orderByAsc(CaseWZEmployerListEntity::getReportTime));
        if (CollectionUtils.isEmpty(caseWZEmployerListEntities)) {
            LogUtil.error(uploadDate+"未查询到微众雇责理赔清单数据，请核实！");
        } else {
            String fileName = "ClaimRecord_"+uploadDate;
            String filePath = "/STATISTICS/ClaimRecord/"+uploadDate;
            try {
                Date runDate =  dateFormat.parse(uploadDate);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(runDate);
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                String runLastDateString = dateFormat.format(calendar.getTime());
                log.info("==========runLastDateString========================="+runLastDateString);
                log.info("==========待处理理赔信息========================="+JSON.toJSONString(caseWZEmployerListEntities));
                log.info("==========待处理理赔信息数量========================="+caseWZEmployerListEntities.size());
                String detail = handleClaimDetail(caseWZEmployerListEntities);
                log.info("==========微众理赔信息========================="+detail);
                File file = new File(filePath);
                if(!file.exists()){
                    boolean isCreated = file.mkdirs();
                    log.info("create diectory"+(isCreated == true ? " SUCESS" : " FAIL"));
                }
                log.info("==========开始生成TXT=========================");
                FileWriter writer = new FileWriter(filePath+ File.separator+fileName+".txt");
                writer.write(detail);
                writer.close();
                log.info("==========TXT生成结束=========================");

                log.info("==========开始生成MD5=========================");
                String detailMD = getMD5(filePath+ File.separator+fileName+".txt");
                FileWriter writerMD = new FileWriter(filePath+ File.separator+fileName+".md5");
                writerMD.write(detailMD);
                writerMD.close();
                log.info("==========MD5生成结束=========================");

                log.info("==========开始生成压缩包=========================");
                //生成ZIP压缩包
                String zipFileName = filePath+ File.separator+fileName+".zip";
                zipDirectory(filePath,zipFileName);
                log.info("==========压缩包生成结束=========================");

                log.info("==========开始上传文件到cos=========================");
                File zipFile = new File(zipFileName);
                if (zipFile.exists() && zipFile.isFile()) {
                    long fileSize = FileUtil.size(zipFile);
                    String zipName = zipFile.getName();
                    String fileId = ioBSFileUploadService.uploadFileToFilePlatform(zipFile.getName(), FileUtil.readBytes(zipFile));
                    if (StringUtils.isNotBlank(fileId)) {
                        WZListUploadRecordEntity wzListUploadRecordEntity = new WZListUploadRecordEntity();
                        List<WZListUploadRecordEntity> wzListUploadRecordEntities = wzListUploadRecordMapper.selectList(new LambdaQueryWrapper<WZListUploadRecordEntity>().eq(WZListUploadRecordEntity::getUploadDate,
                                uploadDate).orderByDesc(WZListUploadRecordEntity::getUploadDate));
                        wzListUploadRecordEntity.setSerialNo(wzListUploadRecordEntities.size() +1);
                        wzListUploadRecordEntity.setUploadDate(uploadDate);
                        wzListUploadRecordEntity.setUploadFileName(zipName);
                        wzListUploadRecordEntity.setUploadFileUrl(fileId);
                        wzListUploadRecordEntity.setUploadFileSize(new BigDecimal(fileSize));
                        wzListUploadRecordEntity.setCreatedBy(ConstValues.SYSTEM_UM);
                        wzListUploadRecordEntity.setUpdatedBy(ConstValues.SYSTEM_UM);
                        wzListUploadRecordMapper.insert(wzListUploadRecordEntity);
                    } else {
                        LogUtil.error(uploadDate+"上传微众雇责理赔清单数据文件到COS异常！");
                    }
                } else {
                    LogUtil.error(uploadDate+"生产微众雇责理赔清单数据文件异常！");
                }
                log.info("==========上传文件到cos结束=========================");
            } catch (Exception e) {
                LogUtil.error(uploadDate+"生产微众雇责理赔清单数据文件异常！",e);
            } finally{
                FileUtil.del(filePath);
            }
        }
        log.info("结束生成微众雇责理赔清单");
    }

    /**
     * 拼接理赔信息
     *
     * @param caseWZEmployerListEntities 保险案件列表实体类列表
     * @return 拼接后的理赔信息字符串
     */
    public String handleClaimDetail(List<CaseWZEmployerListEntity> caseWZEmployerListEntities){
        StringBuffer detail = new StringBuffer();
        int count = 1;
        detail.append("policyNo|endorsementNo|insuredName|applyDate|validDate|incidentDate|reportDate|insuredAmountChange|caseFilingDate|caseClosedDate|employeeIdNo" +
                "|jobCategory|occupationName|insurancePlan|lossItem|clientClaimAmount|assessedAmount|compensationAmount|caseStatus|caseDetails|claimRemarks");
        if(caseWZEmployerListEntities.size()>0)detail.append("\n");
        for (CaseWZEmployerListEntity caseWZEmployerListEntity : caseWZEmployerListEntities) {
            String policyNo= StringUtils.isBlank(caseWZEmployerListEntity.getPolicyNo()) ? "" : caseWZEmployerListEntity.getPolicyNo();//保单号
            String endorsementNo= StringUtils.isBlank(caseWZEmployerListEntity.getAccidentPesronEndorseNo()) ? "" : caseWZEmployerListEntity.getAccidentPesronEndorseNo();//批单号
            String insuredName= StringUtils.isBlank(caseWZEmployerListEntity.getAppntName()) ? "" : caseWZEmployerListEntity.getAppntName();//被保人名称
            String applyDate= StringUtils.isBlank(caseWZEmployerListEntity.getUnderwriteEndTime()) ? "" : caseWZEmployerListEntity.getUnderwriteEndTime();//保单投保日期
            String validDate= StringUtils.isBlank(caseWZEmployerListEntity.getUnderwriteValidTime()) ? "" : caseWZEmployerListEntity.getUnderwriteValidTime();//保单生效日期
            String incidentDate= StringUtils.isBlank(caseWZEmployerListEntity.getAccidentPesronStartDate()) ? "" : caseWZEmployerListEntity.getAccidentPesronStartDate();//员工起保日期
            String reportDate= StringUtils.isBlank(caseWZEmployerListEntity.getAccidentDate()) ? "" : caseWZEmployerListEntity.getAccidentDate();//出险日期
            String insuredAmountChange= StringUtils.isBlank(caseWZEmployerListEntity.getReportTime()) ? "" : caseWZEmployerListEntity.getReportTime();//报案日期
            String caseFilingDate= StringUtils.isBlank(caseWZEmployerListEntity.getClaimTime()) ? "" : caseWZEmployerListEntity.getClaimTime();//立案日期
            String caseClosedDate= StringUtils.isBlank(caseWZEmployerListEntity.getEndCaseDate()) ? "" : caseWZEmployerListEntity.getEndCaseDate();//结案日期
            String employeeIdNo= StringUtils.isBlank(caseWZEmployerListEntity.getCertNo()) ? "" : caseWZEmployerListEntity.getCertNo();//出险员工证件号
            String jobCategory= StringUtils.isBlank(caseWZEmployerListEntity.getAccidentPesronJobRiskLevelName()) ? "" : caseWZEmployerListEntity.getAccidentPesronJobRiskLevelName();//出险员工工种类别
            String occupationName= StringUtils.isBlank(caseWZEmployerListEntity.getAccidentPesronJobName()) ? "" : caseWZEmployerListEntity.getAccidentPesronJobName();//出险员工职业名称
            String insurancePlan= StringUtils.isBlank(caseWZEmployerListEntity.getItemName()) ? "" : caseWZEmployerListEntity.getItemName();//员工投保方案
            String lossItem= StringUtils.isBlank(caseWZEmployerListEntity.getInjuryType()) ? "" : caseWZEmployerListEntity.getInjuryType();//损失项目
            String clientClaimAmount= "";//客户报损金额（元）
            String assessedAmount= null == caseWZEmployerListEntity.getOutstanding() || caseWZEmployerListEntity.getOutstanding().compareTo(BigDecimal.ZERO) == 0 ? "" : caseWZEmployerListEntity.getOutstanding().toString();//定损金额（元）
            String compensationAmount= null == caseWZEmployerListEntity.getIndemAmount() || caseWZEmployerListEntity.getIndemAmount().compareTo(BigDecimal.ZERO) == 0 ? "" : caseWZEmployerListEntity.getIndemAmount().toString();//赔付金额（元）
            String caseStatus= StringUtils.isBlank(caseWZEmployerListEntity.getEndcaseTypeName()) ? "" : caseWZEmployerListEntity.getEndcaseTypeName();//案件状态
            String caseDetails= StringUtils.isBlank(caseWZEmployerListEntity.getCaseLogs()) ? "" : caseWZEmployerListEntity.getCaseLogs();//案件详情
            String claimRemarks= StringUtils.isBlank(caseWZEmployerListEntity.getClaimRemarks()) ? "" : caseWZEmployerListEntity.getClaimRemarks();//理赔备注
            detail.append(
                    policyNo + "|" +
                            endorsementNo + "|" +
                            insuredName + "|" +
                            applyDate + "|" +
                            validDate + "|" +
                            incidentDate + "|" +
                            reportDate + "|" +
                            insuredAmountChange + "|" +
                            caseFilingDate + "|" +
                            caseClosedDate + "|" +
                            employeeIdNo + "|" +
                            jobCategory + "|" +
                            occupationName + "|" +
                            insurancePlan + "|" +
                            lossItem + "|" +
                            clientClaimAmount + "|" +
                            assessedAmount + "|" +
                            compensationAmount + "|" +
                            caseStatus + "|" +
                            caseDetails + "|" +
                            claimRemarks
            );
            if(count != caseWZEmployerListEntities.size()) detail.append("\n");
            count ++;
        }
        return detail.toString();
    }

    /**
     * 生成指定文件路径的MD5信息
     *
     * @param filePath 文件路径
     * @return 文件的MD5信息
     * @throws Exception 如果发生任何异常，则抛出异常
     */
    public static String getMD5(String filePath) throws Exception {
        InputStream fis = new FileInputStream(filePath);
        byte[] buffer = new byte[1024];
        MessageDigest complete = MessageDigest.getInstance("MD5");
        int numRead;

        do {
            numRead = fis.read(buffer);
            if (numRead > 0) {
                complete.update(buffer, 0, numRead);
            }
        } while (numRead != -1);

        fis.close();
        byte[] b = complete.digest();

        String result = "";
        for (int i = 0; i < b.length; i++) {
            result += Integer.toString((b[i] & 0xff) + 0x100, 16).substring(1);
        }
        return result;
    }

    /**
     * 将指定目录压缩到指定的zip文件中。
     *
     * @param sourceDirPath 要压缩的目录路径
     * @param zipFilePath   压缩后的zip文件路径
     * @throws IOException 如果压缩过程中发生I/O错误
     */
    public static void zipDirectory(String sourceDirPath, String zipFilePath) throws IOException {
        Path zipFile = Paths.get(zipFilePath);
        Path sourceDir = Paths.get(sourceDirPath);
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFile.toFile()))) {
            Files.walk(sourceDir)
                .filter(path -> !Files.isDirectory(path))
                .filter(path -> !path.equals(zipFile))  // 排除压缩文件本身
                .forEach(path -> {
                    ZipEntry zipEntry = new ZipEntry(sourceDir.relativize(path).toString());
                    try {
                        zipOutputStream.putNextEntry(zipEntry);
                        Files.copy(path, zipOutputStream);
                        zipOutputStream.closeEntry();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
        }
    }
}
