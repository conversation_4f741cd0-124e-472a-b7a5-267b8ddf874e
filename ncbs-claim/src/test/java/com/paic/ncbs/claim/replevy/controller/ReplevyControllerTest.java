package com.paic.ncbs.claim.replevy.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.replevy.dao.ClmsRelatedActualReceiptMapper;
import com.paic.ncbs.claim.replevy.dto.PlanAndDutyQueryDTO;
import com.paic.ncbs.claim.replevy.dto.ReplevyMainQueryDTO;
import com.paic.ncbs.claim.replevy.dto.ReplevyMainQueryResultDTO;
import com.paic.ncbs.claim.replevy.service.ReplevyChargeService;
import com.paic.ncbs.claim.replevy.service.ReplevyService;
import com.paic.ncbs.claim.replevy.vo.*;
import com.paic.ncbs.claim.service.common.ClaimCommonQueryFileInfoService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.mock.web.MockServletContext;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * ReplevyController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@WebAppConfiguration
public class ReplevyControllerTest {

    private static final Logger log = LoggerFactory.getLogger(ReplevyControllerTest.class);

    @Autowired
    private ReplevyService replevyService;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private TaskListService taskListService;


    @Autowired
    private ClaimCommonQueryFileInfoService claimCommonQueryFileInfoService;


    @Autowired
    private ClmsRelatedActualReceiptMapper clmsRelatedActualReceiptMapper;


    private ServletContext servletContext;
    private MockHttpServletRequest mockRequest;
    private MockHttpServletResponse mockResponse;
    private MockHttpSession mockSession;

    private ReplevyApiVo replevyApiVo;
//    private SendReplevyVo sendReplevyVo;
    private ClmsReplevyTextVo clmsReplevyTextVo;
//    private PayReplevyVo payReplevyVo;
    private String PolicyNo;
    private String ReportNo;
    private String ReplevyNo;
    private String ClaimNo;

    @BeforeEach
    public void setUp() {
        // 初始化WebServletContext并设置参数
        initializeWebServletContext();

        // 初始化Mock对象和用户Session
        initializeMockRequestAndUserSession();

        // 生成静态号码 - 以大写英文字母开头随机生成10位
        PolicyNo = "PC" + generateStaticNumber();    // 保单号
        ClaimNo = "LL" + generateStaticNumber();     // 立案号
//        ReportNo = "QR" + generateStaticNumber();    // 报案号
//        ReplevyNo = "ZC" + generateStaticNumber();   // 追偿号

        ReportNo = "90010000000002007895";    // 报案号
//        ReplevyNo = "ZCX668653919366";   // 追偿号


        // 初始化ReplevyApiVo测试数据
        replevyApiVo = createReplevyApiVo();


        // 初始化ClmsReplevyTextVo测试数据
        clmsReplevyTextVo = createClmsReplevyTextVo();

    }

    @Test
    @DisplayName("新单生成")
    public void testSaveOrSubmitReplevyAll() {
        log.info("开始测试save方法");

        ReplevyApiVo replevyApiVo = createReplevyApiVo();
        replevyApiVo.setSubmitFlag("0");
        ResponseResult<Object> result1 =  replevyService.saveOrSubmitReplevy(replevyApiVo);
        log.info("saveOrSubmitReplevy测试完成，结果: {}", JSON.toJSONString(result1));

        replevyApiVo.getRelatedActualReceiptVoList().stream().forEach(vo -> vo.setId(null));
        ResponseResult<Object> result2 =  replevyService.saveReplevyDetail(replevyApiVo);
        log.info("saveOrSubmitReplevy测试完成，结果: {}", JSON.toJSONString(result2));

        replevyApiVo.getPaymentInfoVo().setIdClmPaymentInfo(null);
        replevyApiVo.getInvoiceInfoDTO().setIdAhcsInvoiceInfo(null);
        replevyApiVo.getReplevyChargeVoList().stream().forEach(vo -> vo.setId(null));
        ResponseResult<Object> result3 =  replevyService.saveOrSubmitReplevyFee(replevyApiVo);
        log.info("saveOrSubmitReplevy测试完成，结果: {}", JSON.toJSONString(result3));



    }

    @Test
    @DisplayName("测试追偿主页面保存提交")
    public void testSaveOrSubmitReplevy() {
        log.info("开始测试saveOrSubmitReplevy方法");
//        String jsonStirng = "{\"reportNo\":\"RP202506110001\",\"replevyMainVo\":{\"approveFlag\":\"\",\"cancelDate\":null,\"cancelReason\":\"\",\"caseTimes\":1,\"claimNo\":\"CL202506110001\",\"comCode\":\"\",\"compensateNo\":\"\",\"createdBy\":\"1\",\"flag\":\"0\",\"handlerCode\":\"\",\"id\":null,\"makeCom\":\"\",\"opinionText\":\"建议追偿\",\"policyNo\":\"PL202506110001\",\"replevyCurrency\":\"CNY\",\"replevyId\":null,\"replevyNo\":\"REP202506110001\",\"replevyOpinion\":\"1\",\"replevyText\":\"追偿详细说明\",\"replevyTimes\":1,\"reportNo\":\"RP202506110001\",\"riskCode\":\"0001\",\"serialNo\":null,\"status\":\"\",\"sumPlanReplevy\":10000.0,\"sumRealReplevy\":9000.0,\"sumReplevyFee\":1000.0,\"sysCtime\":\"2025-07-12 10:58:03\",\"sysUtime\":\"2025-07-12 10:58:03\",\"updatedBy\":\"1\",\"validFlag\":\"Y\"}}";
//        ReplevyApiVo replevyApiVo = JSON.parseObject(jsonStirng, ReplevyApiVo.class);
//
        String jsonStirng = "{\"reportNo\":\"90011000000002004320\",\"caseTimes\":\"1\",\"policyNo\":\"2251000010000018922\",\"replevyMainVo\":{\"replevyNo\":\"ZC9001100000000200432001\",\"replevyCurrency\":\"RMB\",\"sumRealReplevy\":null,\"sumReplevyFee\":null,\"id\":\"23090d842dbe43b8bf90a8ee93d94020\",\"reportNo\":\"90011000000002004320\",\"caseNo\":\"\",\"claimNo\":null,\"policyNo\":\"2251000010000018922\",\"riskCode\":null,\"replevyTimes\":1,\"caseTimes\":1,\"sumPlanReplevy\":null,\"replevyOpinion\":\"1\",\"opinionText\":null,\"replevyText\":\"1231\",\"cancelDate\":null,\"cancelReason\":null,\"status\":null,\"makeCom\":\"1\",\"comCode\":null,\"validFlag\":\"Y\",\"flag\":\"0\",\"handlerCode\":\"blueshen\",\"serialNo\":null,\"compensateNo\":null,\"approveFlag\":\"0\",\"createdBy\":\"blueshen\",\"sysCtime\":1752894101000,\"updatedBy\":\"blueshen\",\"sysUtime\":1752805669000,\"replevyId\":null},\"submitFlag\":\"1\"}";
        ReplevyApiVo replevyApiVo = JSON.parseObject(jsonStirng, ReplevyApiVo.class);
//        log.info("saveOrSubmitReplevy测试完成，结果22: {}", replevyApiVo.getReplevyMainVo().getReplevyText());

//        ReplevyApiVo replevyApiVo = createReplevyApiVo();
        // 调用控制器方法
        ResponseResult<Object> result =  replevyService.saveOrSubmitReplevy(replevyApiVo);

        // 验证结果
        assertNotNull(result);
        assertEquals("000000", result.getCode());
        log.info("saveOrSubmitReplevy测试完成，结果: {}", JSON.toJSONString(result));
    }

    @Test
    @DisplayName("测试追偿子页面保存提交")
    public void testSaveReplevyDetail() {
        log.info("开始测试saveReplevyDetail方法");

        ReplevyApiVo replevyApiVo = createReplevyApiVo();
        replevyApiVo.getReplevyMainVo().setId("040723537a934c999a467d34d53459a8"); //已存mian ，需要带入主表ID

//        String jsonStirng = "{\"reportNo\":\"90010000000001000129\",\"caseTimes\":\"1\",\"submitFlag\":\"1\",\"replevyDetailVo\":{\"repleviedName\":\"阿汤哥\",\"repleviedType\":\"2\",\"repleviedCertiType\":\"990\",\"repleviedCertiCode\":\"114514\",\"repleviedCountryCode\":\"156\",\"repleviedMobile\":\"16766668888\",\"repleviedPhone\":\"*********\",\"repleviedLocation\":\"1\",\"repleviedAddress\":\"上海\",\"replevySum\":500,\"repleviedTax\":\"98765432\",\"repleviedEmail\":\"<EMAIL>\",\"repleviedPostCode\":\"050500\",\"replevyType\":\"1\",\"replevyAgency\":\"机构1234\",\"replevyPerson\":\"专业追偿\",\"replevyWay\":\"1\",\"replevyDate\":\"2025-07-01\",\"transferDate\":\"2025-07-02\",\"replevyeFfectDate\":\"2025-07-03\",\"replevyPersonTel\":\"***********\",\"replevyReason\":\"原因1234\",\"replevyProgress\":\"沈文 2025/7/19 14:24:09: 进度1\",\"newProgress\":\"\"},\"replevyLossVoList\":[],\"relatedActualReceiptVoList\":[{\"id\":null,\"reportNo\":null,\"caseTimes\":null,\"subTimes\":null,\"businessId\":null,\"receiptType\":null,\"businessNo\":\"\",\"transDate\":\"2025-03-27 03:39:22\",\"directionType\":\"0\",\"transAmount\":5000,\"transPurpose\":\"\",\"transAbstract\":\"SX-保费收款\",\"paymentFlowNo\":\"202408260939221666229019\",\"bankTransFlowNo\":\"C250504018310242\",\"ourBankAccount\":\"***************\",\"partnerBankBranchName\":\"中国工商银行深圳市国财支行\",\"partnerBankAccount\":\"****************\",\"partnerBankName\":\"中国工商银行\",\"partnerBankAccountName\":\"上海致景信息科技有限公司\",\"postScript\":\"\",\"writeOffRemainAmount\":1970,\"writeOffAmount\":500,\"freezeFlag\":null,\"validFlag\":null}]}";
//        ReplevyApiVo replevyApiVo = JSON.parseObject(jsonStirng, ReplevyApiVo.class);

        ResponseResult<Object> result =  replevyService.saveReplevyDetail(replevyApiVo);

        // 验证结果
        assertNotNull(result);
        assertEquals("000000", result.getCode());
        log.info("saveReplevyDetail测试完成，结果: {}", JSON.toJSONString(result));
    }

    @Test
    @DisplayName("测试直接理赔费用提交")
    public void testSaveOrSubmitReplevyFee() {
        log.info("开始测试saveOrSubmitReplevyFee方法");

        try {
            // 调用控制器方法
//            ReplevyApiVo replevyApiVo = createReplevyApiVo();
//            //已存mian ，需要带入主表ID
//            replevyApiVo.getReplevyMainVo().setId("040723537a934c999a467d34d53459a8");
//
//            replevyApiVo.getPaymentInfoVo().setIdClmPaymentInfo(null);
//            replevyApiVo.getInvoiceInfoDTO().setIdAhcsInvoiceInfo(null);
//            replevyApiVo.getReplevyChargeVoList().stream().forEach(vo -> vo.setId(null));

            String jsonStirng = "{\"submitFlag\":\"1\",\"reportNo\":\"98081000000002008583\",\"caseTimes\":\"1\",\"replevyChargeVo\":{\"currency\":\"RMB\",\"chargeType\":\"FEE_07\",\"chargeDescribe\":\"测试\",\"chargeMoney\":33,\"reportNo\":\"98081000000002008583\",\"approveFlag\":\"\",\"applyPerson\":\"沈文\",\"applyTime\":\"\",\"applyReason\":\"测试\"},\"paymentInfoVo\":{\"idClmPaymentInfo\":\"9372a2485f16462ba13594f4015ce5ef\"},\"invoiceInfoDTO\":{\"invoiceNo\":\"\",\"invoiceCode\":\"\",\"invoiceType\":\"000\",\"noTaxAmount\":33,\"taxAmount\":0,\"invoiceDate\":\"\",\"sellCompany\":\"\",\"sellTaxpayerNo\":\"\",\"taxbuyerNo\":\"\",\"taxRate\":0,\"totalAmount\":33}}";
            ReplevyApiVo replevyApiVo = JSON.parseObject(jsonStirng, ReplevyApiVo.class);
//            replevyApiVo.getPaymentInfoVo().setIdClmPaymentInfo("32316437323963382D363233302D3131");

//            replevyApiVo.getInvoiceInfoDTO().setIdAhcsInvoiceInfo("66626631333836342D343636632D3131");

            ResponseResult<Object> result = replevyService.saveOrSubmitReplevyFee(replevyApiVo);

            // 验证结果
            assertNotNull(result);
            assertEquals("000000", result.getCode());
            log.info("saveOrSubmitReplevyFee测试完成，结果: {}", JSON.toJSONString(result));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("测试追偿页面初始化")
    public void testInitReplevy() {
        log.info("开始测试initReplevy方法");

        ReplevyApiVo replevyApiVo = new ReplevyApiVo();

        replevyApiVo.setReportNo("90011000000002004320");
        replevyApiVo.setReplevyNo("ZC9001100000000200432001");
        // 1.追偿主页面信息初始数据
        replevyApiVo.setInitFlag("1");
        ResponseResult<Object> result = replevyService.initReplevy(replevyApiVo);
        log.info("initReplevy测试完成，结果1: {}", JSON.toJSONString(result));

       // 2.追偿费用信息页面信息初始数据
        replevyApiVo.setInitFlag("2");
        replevyApiVo.setReplevyChargeId("b8fb505c22224bc1b10057c9f70d99f1");
        ResponseResult<Object> result2 = replevyService.initReplevy(replevyApiVo);
        log.info("initReplevy测试完成，结果2: {}", JSON.toJSONString(result2));

        // 3.追偿子页面初始化数据
        replevyApiVo.setInitFlag("3");
        replevyApiVo.setReplevyDetailId("b8fb505c22224bc1b10057c9f70d99f1");// Detail查询
        ResponseResult<Object> result3 = replevyService.initReplevy(replevyApiVo);

        log.info("initReplevy测试完成，结果3: {}", JSON.toJSONString(result3));




    }


    @Test
    @DisplayName("删除")
    public void testSendCashFlowSearchDelete() {
        log.info("开始测试sendCashFlowSearch方法");
        ReplevyApiVo replevyApiVo = new ReplevyApiVo();

        // 1-删除费用信息，2-删除追偿明细信息
//        replevyApiVo.setInitFlag("1");
//        replevyApiVo.setReplevyDetailId("fc1cdf59f3854a58bab2bf56b89556b7");
////        replevyApiVo.setReplevyChargeId("fc1cdf59f3854a58bab2bf56b89556b7");
//
//        ResponseResult<Object> result = replevyService.deleteReplevyData(replevyApiVo);
//        log.info("sendCashFlowSearch测试完成，结果: {}", JSON.toJSONString(result));
//
//        ReplevyApiVo replevyApiVo2 = new ReplevyApiVo();
//        // 1-删除费用信息，2-删除追偿明细信息
//        replevyApiVo2.setInitFlag("2");
//        replevyApiVo2.setReplevyDetailId("30");
//        replevyApiVo2.setReplevyChargeId("ddb6fbbb65424b21b46139fdeba36b8a");
//
//        ResponseResult<Object> result0 = replevyService.deleteReplevyData(replevyApiVo2);
//        log.info("initReplevy测试完成，结果: {}", JSON.toJSONString(result0));

        ReplevyApiVo replevyApiVo8 = new ReplevyApiVo();
        // 1-删除费用信息，2-删除追偿明细信息
        replevyApiVo8.setInitFlag("3");
        replevyApiVo8.setRelatedActualReceiptId("82a88f5b5a3c4a5c8ab5d0e5de12d4ca");

        ResponseResult<Object> result5 = replevyService.deleteReplevyData(replevyApiVo8);
        log.info("initReplevy测试完成，结果: {}", JSON.toJSONString(result5));

    }


    @Test
    @DisplayName("险种&责任查询")
    public void testSendCashFlowSearchSelect() {
//        ReplevyApiVo replevyApiVo = new ReplevyApiVo();
//
//        replevyApiVo.setReportNo("90011000000001004237");
//        replevyApiVo.setInitFlag("1");//1 子页面  2.费用页面
//
//        ResponseResult<Object> result = replevyService.queryPlanAndDutyInfo(replevyApiVo);

//        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode("1");
//        log.info("sendCashFlowSearch测试完成，结果: {}", JSON.toJSONString(departmentCodes));


        String paymentStatus = "";
        if(!paymentStatus.isEmpty()){
            clmsRelatedActualReceiptMapper.updateFreezeFlagByBatchNo("312321", paymentStatus);
        }

        paymentStatus = Constants.PAYMENT_ITEM_STATUS_32;

        clmsRelatedActualReceiptMapper.updateFreezeFlagByBatchNo("312321", paymentStatus);

    }

    @Test
    @DisplayName("查询处理中追偿案件")
    public void testSendCashFlowSearchSelect1() {
        ReplevyApiVo replevyApiVo = new ReplevyApiVo();

        replevyApiVo.setReportNo("90011000000002002472");

        ResponseResult<Object> result = replevyService.queryReplevyMainByReportNo(replevyApiVo);
        log.info("sendCashFlowSearch测试完成，结果: {}", JSON.toJSONString(result));

    }



    @Test
    @DisplayName("测试追偿审核页面初始化")
    public void testInitReplevyApprove() {
        log.info("开始测试initReplevyApprove方法");

        // 调用控制器方法
//        ReplevyApiVo replevyApiVo = createReplevyApiVo();
        ReplevyApiVo replevyApiVo = new ReplevyApiVo();
        replevyApiVo.setReportNo("90010000000002007895");
        replevyApiVo.setReplevyNo("ZC9001000000000200789501");
        replevyApiVo.setInitFlag("Z");
        replevyApiVo.setInitFlag("F");
        replevyApiVo.setReplevyChargeId("59117760f87d4d5bbe74cd09bb7138d4");
        ResponseResult<Object> result = replevyService.initReplevyApprove(replevyApiVo);

        // 验证结果
        log.info("initReplevyApprove测试完成，结果: {}", JSON.toJSONString(result));

        replevyApiVo.setInitFlag("Z");
        ResponseResult<Object> result1 = replevyService.initReplevyApprove(replevyApiVo);
        log.info("initReplevyApprove测试完成，结果: {}", JSON.toJSONString(result1));
    }



    @Test
    @DisplayName("测试关联实收调用收付流水查询")
    public void testSendCashFlowSearch() {
        log.info("开始测试sendCashFlowSearch方法");
//        FileInfoDTO fileInfoDTO = new FileInfoDTO();
//        fileInfoDTO.setReportNo("98081000000002002014");
//        fileInfoDTO.setFileId("https://dsuprivate-1313541860.cos.ap-shanghai-fsi.myqcloud.com/ncbs-claim/1947695764667081288-98081000000002002014_b17ff.pdf");
//        FileInfoDTO busfileDto = claimCommonQueryFileInfoService.getFileInfo(fileInfoDTO);

        ReplevyReqApiVo replevyApiVo = new ReplevyReqApiVo();
        replevyApiVo.setReportNo("90010000000002007895");
        log.info("sendCashFlowSearch测试完成，结果: {}", JSON.toJSONString(replevyService.checkReplevy(replevyApiVo)));
    }




    /**
     * 创建ReplevyApiVo测试数据
     * 注意：所有字段都赋值，且符合数据库字段长度和类型限制
     */
    private ReplevyApiVo createReplevyApiVo() {
        ReplevyApiVo vo = new ReplevyApiVo();

        // 页面初始化和操作标志
        vo.setInitFlag("1");                    // 1代表追偿主页面初始化
        vo.setOperateFlag("C");                 // C-新增，U-修改，D-删除，V-查看
        vo.setSubmitFlag("1");                  // 0代表暂存，1代表提交

        // 基本信息字段
        vo.setReportNo(ReportNo);               // 报案号 - 10位
        vo.setPolicyNo(PolicyNo);               // 保单号 - 10位
        vo.setReplevyNo(ReplevyNo);             // 追偿案件号 - 10位
        vo.setCaseTimes(1);                     // 赔付次数
        vo.setReplevyTimes(1);                  // 追偿次数

        // 审核相关字段
        vo.setOpinionType("1");                 // 业务动作 - 1位
        vo.setApproveOpinion("1");              // 审核意见 - 1位
        vo.setHandleText("同意追偿申请");        // 意见说明 - 200位
        vo.setHandlerCode("7");        // 追偿操作员 - 10位

        // 主键ID字段
//        vo.setReplevyId();                     // 主信息ID
        vo.setReplevyDetailId("1");               // 追偿明细主键ID
        vo.setReplevyChargeId("1");               // 费用信息ID

        // 状态标志
        vo.setFlag("0");                        // 是否追偿完毕标志位

        // 设置主表信息
        ClmsReplevyMainVo mainVo = new ClmsReplevyMainVo();
        mainVo.setReportNo(ReportNo);
        mainVo.setPolicyNo(PolicyNo);
        mainVo.setClaimNo(ClaimNo);
        mainVo.setReplevyNo(ReplevyNo);
        mainVo.setRiskCode("0001");
        mainVo.setReplevyTimes(1);
        mainVo.setCaseTimes(1);
        mainVo.setReplevyCurrency("CNY");
        mainVo.setSumPlanReplevy(new BigDecimal("10000.00"));
        mainVo.setSumRealReplevy(new BigDecimal("9000.00"));
        mainVo.setSumReplevyFee(new BigDecimal("1000.00"));
        mainVo.setReplevyOpinion("1");
        mainVo.setOpinionText("建议追偿");
        mainVo.setReplevyText("追偿详细说明");
        mainVo.setValidFlag("Y");
        mainVo.setFlag("0");
        mainVo.setCreatedBy("1");
        mainVo.setSysCtime(new Date());
        mainVo.setUpdatedBy("1");
        mainVo.setSysUtime(new Date());
        vo.setReplevyMainVo(mainVo);

//        // 设置追偿建议列表
//        List<ClmsReplevySuggestVo> suggestList = new ArrayList<>();
//        ClmsReplevySuggestVo suggest = new ClmsReplevySuggestVo();
//        suggest.setReportNo("RP202506110001");
//        suggest.setReplevyNo("REP202506110001");
//        suggest.setReplevyTimes(1);
//        suggest.setCaseTimes(1);
//        suggest.setNodeType("SUGGEST");
//        suggest.setOperatorCode("OP001");
//        suggest.setOperatorName("操作员张三");
//        suggest.setMakeCom("PAIC");
//        suggest.setReplevyFlag("RECOMMEND");
//        suggest.setSuggestText("建议对第三方进行追偿");
//        suggest.setMessageType("NORMAL");
//        suggest.setValidFlag("Y");
//        suggest.setFlag("0");
//        suggest.setSerialNo(1);
//        suggest.setCreatedBy("TEST_USER");
//        suggest.setSysCtime(new Date());
//        suggestList.add(suggest);
//        vo.setReplevySuggestVoList(suggestList);

        // 设置追偿明细列表
        List<ClmsReplevyDetailVo> detailList = new ArrayList<>();
        ClmsReplevyDetailVo detail = new ClmsReplevyDetailVo();
//        detail.setId(16);
//        detail.setRepleviedName("第三方责任人update");
        detail.setRepleviedName("第三方责任人");
        detail.setReportNo(ReportNo);
        detail.setReplevyNo(ReplevyNo);
        detail.setReplevyTimes(1);
        detail.setCaseTimes(1);
        detail.setClauseCode("001");
        detail.setKindCode("001");
        detail.setRepleviedType("1");
        detail.setRepleviedCertiType("1");
        detail.setRepleviedCertiCode("1111");
        detail.setRepleviedCountryCode("CN");
        detail.setRepleviedLocation("北京市");
        detail.setReplevySum(new BigDecimal("5000.00"));
        detail.setRepleviedMobile("***********");
        detail.setRepleviedPhone("010-12345678");
        detail.setRepleviedAddress("北京市朝阳区");
        detail.setRepleviedPostCode("100000");
        detail.setRepleviedEmail("<EMAIL>");
        detail.setReplevyType("1");
        detail.setReplevyAgency("平安保险");
        detail.setReplevyPerson("李四");
        detail.setReplevyPersonTel("13900139000");
        detail.setReplevyDate(new Date());
        detail.setReplevyHandleDate(new Date());
        detail.setReplevyWay("1");
        detail.setReplevyReason("交通事故第三方责任");
        detail.setRealReplevy(new BigDecimal("4500.00"));
        detail.setReplevyFee(new BigDecimal("500.00"));
        detail.setStatus("1");
        detail.setApproveFlag("Y");
        detail.setCurrency("CNY");
        detail.setReplevyProgress("COMPLETED");
        detail.setRepleviedArea("北京");
        detail.setRepleviedAreaCode("110000");
        detail.setReplevyApproveFlag("Y");
        detail.setApprovePerson("审批人王五");
        detail.setApproveDate(new Date());
        detail.setValidFlag("Y");
        detail.setFlag("0");
        detail.setSerialNo(1);
        detail.setCreatedBy("TEST_USER");
        detail.setSysCtime(new Date());
        detailList.add(detail);
        vo.setReplevyDetailVo(detail);
        vo.setReplevyDetailVoList(detailList);

        // 设置追偿费用列表
        List<ClmsReplevyChargeVo> chargeList = new ArrayList<>();
        ClmsReplevyChargeVo charge = new ClmsReplevyChargeVo();
        // 基本信息字段
        charge.setReportNo(ReportNo);
        charge.setReplevyNo(ReplevyNo);
        charge.setReplevyTimes(1);
        charge.setCaseTimes(1);
        // 费用相关字段
        charge.setChargeType("1");
        charge.setChargeMoney(new BigDecimal("1000.00"));
        charge.setChargeDescribe("追偿案件法律费用");
        charge.setApplyReason("案件需要法律服务");
        charge.setCurrency("CNY");
        // 申请相关字段
        charge.setApplyLink("REPLEVY_APPLY");
        charge.setApplyPerson("张三");
        //charge.setApplyTime("2025-01-11 14:30:45");
        // 支付对象字段
        charge.setPayObject("1");
        charge.setClientName("第三方责任人");
        charge.setClientAccount("6222021234567890123");
        // ID字段
//        charge.setInvoiceInfoId("INV202506110001");
        charge.setPaymentInfoId("PAY202506110001");
        // 状态字段
        charge.setApproveFlag("1");
        charge.setValidFlag("Y");
        charge.setFlag("0");
        charge.setSerialNo(1);
        // 审计字段
        charge.setCreatedBy("TEST_USER");
        charge.setSysCtime(new Date());
        chargeList.add(charge);
        vo.setReplevyChargeVoList(chargeList);

        // 设置单个VO对象
        vo.setReplevyDetailVo(detail);          // 追偿明细信息
        vo.setReplevyChargeVo(charge);          // 追偿费用信息

        // 设置追偿意见信息
        ClmsReplevyTextVo textVo = createClmsReplevyTextVo();
        vo.setReplevyTextVo(textVo);

        // 设置追偿意见信息集合
        List<ClmsReplevyTextVo> textVoList = new ArrayList<>();
        textVoList.add(textVo);
        vo.setReplevyTextVoList(textVoList);

        // 设置领款人信息
        PaymentInfoVO paymentInfoVo = new PaymentInfoVO();

        // 基本信息字段
        paymentInfoVo.setPaymentInfoStatus("0");
        paymentInfoVo.setMigrateFrom("1");
        paymentInfoVo.setIdClmPaymentInfo("PAY202506110001");    // 支付信息主键 - 32位
        paymentInfoVo.setReportNo(ReportNo);                     // 报案号 - 10位
        paymentInfoVo.setCaseTimes(1);                           // 案件次数
        paymentInfoVo.setSubTimes(1);                            // 子次数
        paymentInfoVo.setIdClmChannelProcess("PROC001");         // 渠道处理ID - 32位

        // 支付类型和方式
        paymentInfoVo.setPaymentInfoType("1");             // 支付信息类型 - 10位
        paymentInfoVo.setCollectPayApproach("1");    // 收付款方式 - 20位
        paymentInfoVo.setBankAccountAttribute("2");       // 银行账户属性 - 10位
        paymentInfoVo.setPayType("2");                           // 微保支付类型：2-银行转账

        // 客户信息
        paymentInfoVo.setClientName("收款人张三");                // 客户姓名 - 50位
        paymentInfoVo.setClientType("1");                   // 客户类型 - 10位
        paymentInfoVo.setClientCertificateType("1");       // 证件类型 - 10位
        paymentInfoVo.setClientCertificateNo("110101199001011234"); // 证件号码 - 18位
        paymentInfoVo.setClientMobile("***********");            // 手机号码 - 11位
        paymentInfoVo.setClientRelation("2");                 // 客户关系 - 10位
        paymentInfoVo.setCustomerNo("CUST202506110001");         // 客户号 - 20位

        // 银行信息
        paymentInfoVo.setClientBankAccount("6222021234567890123"); // 银行账号 - 19位
        paymentInfoVo.setClientBankCode("ICBC");                 // 银行代码 - 10位
        paymentInfoVo.setClientBankName("工商银行");              // 银行名称 - 50位
        paymentInfoVo.setBankDetail("工商银行北京分行");          // 银行详细信息 - 100位
        paymentInfoVo.setBankDetailCode("110105");               // 银行详细代码 - 10位

        // 地址信息
        paymentInfoVo.setProvinceCode("110000");                 // 省份代码 - 6位
        paymentInfoVo.setProvinceName("北京市");                 // 省份名称 - 20位
        paymentInfoVo.setCityCode("110100");                     // 城市代码 - 6位
        paymentInfoVo.setCityName("北京市");                     // 城市名称 - 20位
        paymentInfoVo.setRegionCode("110105");                   // 区域代码 - 6位
        paymentInfoVo.setCountryName("中国");                    // 国家名称 - 20位
        paymentInfoVo.setAddress("北京市朝阳区");                // 地址 - 200位

        // 证件有效期
        paymentInfoVo.setCertificateEffectiveDate("2010-01-01"); // 证件生效时间
        paymentInfoVo.setCertificateExpireDate("2030-01-01");    // 证件失效时间

        // 个人信息
        paymentInfoVo.setGender("M");                            // 性别：M-男性
        paymentInfoVo.setIncomeCode("2");                        // 收入类型：2-5到15万
        paymentInfoVo.setIncomeText("5到15万");                  // 收入文本 - 200位
        paymentInfoVo.setCompany("某公司");                      // 工作单位 - 100位

        // 国籍和职业信息
        paymentInfoVo.setAreaCode("02");                         // 洲编码：亚洲-02
        paymentInfoVo.setNationCode("156");                      // 国籍：中国-156
        paymentInfoVo.setProfessionMaximumCode("001");           // 职业大类 - 3位
        paymentInfoVo.setProfessionMediumCode("00101");          // 职业中类 - 5位
        paymentInfoVo.setProfessionMinimumCode("00101001");      // 职业小类 - 8位

        // 其他信息
        paymentInfoVo.setAgencyType("PERSONAL");                 // 机构类型 - 10位
        paymentInfoVo.setCompanyCardType("610099");              // 企业证件类型：统一社会信用代码
        paymentInfoVo.setIsMergePay("N");                        // 是否合并支付 - 1位
        paymentInfoVo.setIsTaxpayer("N");                        // 是否纳税人 - 1位
        paymentInfoVo.setPaymentUsage("1");        // 支付用途 - 50位
        paymentInfoVo.setMergeStrategy("NONE");                  // 合并策略 - 20位
        paymentInfoVo.setRemark("追偿支付信息");                 // 备注 - 200位
        paymentInfoVo.setCounterPaymentReason("NORMAL");         // 柜面支付原因 - 50位
        paymentInfoVo.setOtherReason("无");                      // 其他原因 - 200位
        paymentInfoVo.setOrganizeCode("ORG001");                 // 组织代码 - 20位
        paymentInfoVo.setOpenId("OPENID123456789");              // 微信openId - 50位

        vo.setPaymentInfoVo(paymentInfoVo);

        // 设置发票信息
        InvoiceInfoDTO invoiceInfoDTO = new InvoiceInfoDTO();

        // 主键和关联信息
        invoiceInfoDTO.setIdAhcsInvoiceInfo("INV202506110001");  // 发票信息主键 - 32位
        invoiceInfoDTO.setIdAhcsFeePay("FEE202506110001");       // 理赔费用表主键 - 32位

        // 发票基本信息
        invoiceInfoDTO.setInvoiceCode("144031909110");           // 发票代码 - 12位
        invoiceInfoDTO.setInvoiceNo("23456789");                 // 发票号码 - 8位
        invoiceInfoDTO.setInvoiceType("02");                     // 发票类型：02-普通发票
        invoiceInfoDTO.setInvoiceDate(new Date());               // 发票日期

        // 购买方信息
        invoiceInfoDTO.setBuyCompany("平安财产保险股份有限公司");  // 购买方 - 100位
        invoiceInfoDTO.setTaxbuyerNo("91110000100000001X");      // 购买方纳税识别号 - 20位

        // 销售方信息
        invoiceInfoDTO.setSellCompany("某服务提供商");           // 销货单位 - 100位
        invoiceInfoDTO.setSellTaxpayerNo("91110000200000002Y");  // 纳税识别号 - 20位

        // 金额信息
        invoiceInfoDTO.setNoTaxAmount(new BigDecimal("943.40")); // 不计税金额
        invoiceInfoDTO.setTaxAmount(new BigDecimal("56.60"));    // 税额
        invoiceInfoDTO.setTotalAmount(new BigDecimal("1000.00"));// 价税合计
        invoiceInfoDTO.setTaxRate(6);                            // 税率：6%

        // 文件信息
        invoiceInfoDTO.setFileId("FILE202506110001");            // 文件ID - 32位
        invoiceInfoDTO.setFileName("发票.pdf");                  // 文件名 - 100位
        invoiceInfoDTO.setFileUrl("/files/invoice/202506110001.pdf"); // 文件链接 - 200位
        invoiceInfoDTO.setDocumentFormat("PDF");                 // 文件格式 - 10位

        // 状态信息
        invoiceInfoDTO.setIsModifiedFlag("0");                   // 修改标记：0-未修改
        invoiceInfoDTO.setReturnReason("");                      // 退回原因 - 200位
        invoiceInfoDTO.setReturnType("发票退回");                // 退回类型 - 50位
        invoiceInfoDTO.setReturnDate("");                        // 退回日期 - 10位
        invoiceInfoDTO.setReplevyFlag("Y");                      // 追偿标志 - 1位

        vo.setInvoiceInfoDTO(invoiceInfoDTO);

        // 设置追偿收入信息列表
        List<ClmsReplevyLossVo> replevyLossVoList = new ArrayList<>();

        // 创建第一个追偿收入记录
        ClmsReplevyLossVo lossVo1 = new ClmsReplevyLossVo();

        // 基本信息字段
//        lossVo1.setId(34);
        lossVo1.setReplevyDetailId("1");                      // 追偿明细表ID
        lossVo1.setReportNo(ReportNo);                      // 报案号 - 10位
        lossVo1.setReplevyNo(ReplevyNo);                    // 追偿案件号 - 10位
        lossVo1.setReplevyTimes(1);                         // 追偿次数
        lossVo1.setCaseTimes(1);                            // 赔付次数

        // 条款和险种信息
        lossVo1.setClauseCode("001");                       // 条款 - 10位
        lossVo1.setPlanCode("A001");                        // 险种 - 10位
        lossVo1.setPlanName("机动车损失保险");               // 险种名称 - 50位

        // 责任信息
        lossVo1.setDutyCode("D001");                        // 责任代码 - 10位
        lossVo1.setDutyName("车辆损失");                    // 责任名称 - 50位
        lossVo1.setDutyDetailCode("D001001");               // 责任明细代码 - 20位

        // 追回信息
        lossVo1.setRepleviedType("1");                  // 追回类型 - 10位
        lossVo1.setRepleviedLoss("车辆维修费");             // 追回物品 - 100位
        lossVo1.setPrice(new BigDecimal("5000.00"));        // 物品单价
        lossVo1.setUnit("元");                              // 物品单位 - 10位
        lossVo1.setCurrency("CNY");                         // 币别 - 3位
        lossVo1.setRepleviedMoney(new BigDecimal("4500.00")); // 实际追回金额

        // 状态字段
        lossVo1.setApproveFlag("Y");                        // 高级审核状态 - 1位
        lossVo1.setValidFlag("Y");                          // 有效标志 - 1位
        lossVo1.setFlag("0");                               // 标志字段 - 1位
        lossVo1.setSerialNo(1);                             // 序号

        // 审计字段
        lossVo1.setCreatedBy("TEST_USER");                  // 创建人 - 10位
        lossVo1.setSysCtime(new Date());                    // 创建时间

        replevyLossVoList.add(lossVo1);

        // 创建第二个追偿收入记录
        ClmsReplevyLossVo lossVo2 = new ClmsReplevyLossVo();

        // 基本信息字段
        lossVo2.setReplevyDetailId("1");                      // 追偿明细表ID
        lossVo2.setReportNo(ReportNo);                      // 报案号 - 10位
        lossVo2.setReplevyNo(ReplevyNo);                    // 追偿案件号 - 10位
        lossVo2.setReplevyTimes(1);                         // 追偿次数
        lossVo2.setCaseTimes(1);                            // 赔付次数

        // 条款和险种信息
        lossVo2.setClauseCode("002");                       // 条款 - 10位
        lossVo2.setPlanCode("A002");                        // 险种 - 10位
        lossVo2.setPlanName("第三者责任保险");               // 险种名称 - 50位

        // 责任信息
        lossVo2.setDutyCode("D002");                        // 责任代码 - 10位
        lossVo2.setDutyName("第三者损失");                  // 责任名称 - 50位
        lossVo2.setDutyDetailCode("D002001");               // 责任明细代码 - 20位

        // 追回信息
        lossVo2.setRepleviedType("2");               // 追回类型 - 10位
        lossVo2.setRepleviedLoss("第三方财产损失");          // 追回物品 - 100位
        lossVo2.setPrice(new BigDecimal("3000.00"));        // 物品单价
        lossVo2.setUnit("元");                              // 物品单位 - 10位
        lossVo2.setCurrency("CNY");                         // 币别 - 3位
        lossVo2.setRepleviedMoney(new BigDecimal("2800.00")); // 实际追回金额

        // 状态字段
        lossVo2.setApproveFlag("Y");                        // 高级审核状态 - 1位
        lossVo2.setValidFlag("Y");                          // 有效标志 - 1位
        lossVo2.setFlag("0");                               // 标志字段 - 1位
        lossVo2.setSerialNo(2);                             // 序号

        // 审计字段
        lossVo2.setCreatedBy("TEST_USER");                  // 创建人 - 10位
        lossVo2.setSysCtime(new Date());                    // 创建时间

        replevyLossVoList.add(lossVo2);

        // 设置追偿收入信息列表
        vo.setReplevyLossVoList(replevyLossVoList);

        // 设置关联实收列表
        List<ClmsRelatedActualReceiptVo> relatedActualReceiptVoList = createRelatedActualReceiptVoList();
        vo.setRelatedActualReceiptVoList(relatedActualReceiptVoList);

        return vo;
    }


    /**
     * 创建ClmsReplevyTextVo测试数据
     */
    private ClmsReplevyTextVo createClmsReplevyTextVo() {
        ClmsReplevyTextVo vo = new ClmsReplevyTextVo();

        // 基本信息字段
//        vo.setReplevyId(1);
        vo.setReportNo(ReportNo);
        vo.setReplevyNo(ReplevyNo);
        vo.setReplevyTimes(1);
        vo.setCaseTimes(1);

        // 角色和操作信息
        vo.setReplevyRole("APPROVER");
        vo.setOpinionType("1"); // 1-追偿审批,2-追偿费用审批
//        vo.setOperatorCode("OP001");
//        vo.setOperatorName("审批人张三");
        vo.setMakeCom("PAIC");

        // 审核意见
        vo.setApproveOpinion("1"); // 1-同意，2-不同意，3-退回上一级
        vo.setHandleText("同意追偿申请，建议尽快启动追偿程序");
        vo.setHandleDate(new Date());

        // 申请相关信息
//        vo.setApplyPerson("申请人李四");
        vo.setApplyDate(new Date());
//        vo.setReplevyOpinion("建议追偿");
        vo.setReplevyChargeId("1");
        vo.setTaskId("TASK202506110001");

        // 状态字段
        vo.setValidFlag("Y");
        vo.setFlag("0");
        vo.setSerialNo(1);

        // 审计字段
        vo.setCreatedBy("TEST_USER");
        vo.setSysCtime(new Date());

        return vo;
    }


    /**
     * 创建关联实收信息列表测试数据
     * 注意：所有字段都赋值，且符合数据库字段长度和类型限制
     */
    private List<ClmsRelatedActualReceiptVo> createRelatedActualReceiptVoList() {
        List<ClmsRelatedActualReceiptVo> relatedActualReceiptVoList = new ArrayList<>();

        // 创建第一个关联实收记录（收款）
        ClmsRelatedActualReceiptVo receiptVo1 = new ClmsRelatedActualReceiptVo();

        // 基本信息字段
        receiptVo1.setId(UuidUtil.getUUID());
        receiptVo1.setReportNo(ReportNo);                           // 报案号 - 10位
//        receiptVo1.setReplevyNo(ReplevyNo);                         // 追偿案件号 - 10位
//        receiptVo1.setReplevyTimes(1);                              // 追偿次数
        receiptVo1.setCaseTimes(1);                                 // 赔付次数

        // 业务信息字段
        receiptVo1.setBusinessNo("BIZ202501110001");                // 业务编码 - 64位
        receiptVo1.setTransDate(LocalDateTime.now());               // 交易日期
        receiptVo1.setDirectionType("0");                           // 收款付款：0-收款，1-付款
        receiptVo1.setTransAmount(new BigDecimal("5000.00"));       // 交易金额
        receiptVo1.setTransPurpose("追偿收款");                     // 用途
        receiptVo1.setTransAbstract("第三方责任人赔偿款");           // 摘要
        receiptVo1.setPaymentFlowNo("PAY202501110001");             // 银企直联系统支付流水号

        // 银行信息字段
        receiptVo1.setBankTransFlowNo("TXN202501110001234567");     // 银行交易流水号 - 64位
        receiptVo1.setOurBankAccount("6222021234567890001");        // 我方银行卡号 - 32位
        receiptVo1.setPartnerBankAccount("6222021234567890002");    // 客户打款银行卡号 - 32位
//        receiptVo1.setPartnerBankAccountName("第三方责任人张三");    // 客户打款银行账号名称 - 128位

        // 核销信息字段
        receiptVo1.setPostScript("追偿案件收款");                   // 附言 - 256位
        receiptVo1.setWriteOffRemainAmount(new BigDecimal("0.00")); // 核销余额
        receiptVo1.setWriteOffAmount(new BigDecimal("5000.00"));    // 核销金额

        // 状态字段
        receiptVo1.setFreezeFlag("R");                              // 冻结标记：F-冻结，R-释放
        receiptVo1.setValidFlag("Y");                               // 有效标志：Y-有效，N-无效
//        receiptVo1.setFlag("0");                                    // 标志字段：0-进行中，1-完成
//        receiptVo1.setSerialNo(1);                                  // 序号
//
//        // 审计字段
//        receiptVo1.setCreatedBy("TEST_USER");                       // 创建人 - 32位
//        receiptVo1.setSysCtime(new Date());                         // 创建时间

        relatedActualReceiptVoList.add(receiptVo1);

        // 创建第二个关联实收记录（付款）
        ClmsRelatedActualReceiptVo receiptVo2 = new ClmsRelatedActualReceiptVo();

        // 基本信息字段
        receiptVo2.setId(UuidUtil.getUUID());
        receiptVo2.setReportNo(ReportNo);                           // 报案号 - 10位
//        receiptVo2.setReplevyNo(ReplevyNo);                         // 追偿案件号 - 10位
//        receiptVo2.setReplevyTimes(1);                              // 追偿次数
        receiptVo2.setCaseTimes(1);                                 // 赔付次数

        // 业务信息字段
        receiptVo2.setBusinessNo("BIZ202501110002");                // 业务编码 - 64位
        receiptVo2.setTransDate(LocalDateTime.now());               // 交易日期
        receiptVo2.setDirectionType("1");                           // 收款付款：0-收款，1-付款
        receiptVo2.setTransAmount(new BigDecimal("1000.00"));       // 交易金额
        receiptVo2.setTransPurpose("追偿费用支付");                 // 用途
        receiptVo2.setTransAbstract("律师费用支付");                 // 摘要
        receiptVo2.setPaymentFlowNo("PAY202501110002");             // 银企直联系统支付流水号

        // 银行信息字段
        receiptVo2.setBankTransFlowNo("TXN202501110002345678");     // 银行交易流水号 - 64位
        receiptVo2.setOurBankAccount("6222021234567890001");        // 我方银行卡号 - 32位
        receiptVo2.setPartnerBankAccount("6222021234567890003");    // 客户打款银行卡号 - 32位
//        receiptVo2.setPartnerBankAccountName("律师事务所");          // 客户打款银行账号名称 - 128位

        // 核销信息字段
        receiptVo2.setPostScript("追偿费用支付");                   // 附言 - 256位
        receiptVo2.setWriteOffRemainAmount(new BigDecimal("0.00")); // 核销余额
        receiptVo2.setWriteOffAmount(new BigDecimal("1000.00"));    // 核销金额

        // 状态字段
        receiptVo2.setFreezeFlag("R");                              // 冻结标记：F-冻结，R-释放
        receiptVo2.setValidFlag("Y");                               // 有效标志：Y-有效，N-无效
//        receiptVo2.setFlag("0");                                    // 标志字段：0-进行中，1-完成
//        receiptVo2.setSerialNo(2);                                  // 序号
//
//        // 审计字段
//        receiptVo2.setCreatedBy("TEST_USER");                       // 创建人 - 32位
//        receiptVo2.setSysCtime(new Date());                         // 创建时间

        relatedActualReceiptVoList.add(receiptVo2);

        return relatedActualReceiptVoList;
    }

    /**
     * 初始化WebServletContext并设置所有参数
     */
    private void initializeWebServletContext() {
        // 获取ServletContext
        servletContext = webApplicationContext.getServletContext();

        if (servletContext == null) {
            // 如果没有ServletContext，创建MockServletContext
            servletContext = new MockServletContext();
        }

        // 设置应用基本信息
        servletContext.setAttribute("applicationName", "ncbs-claim");
        servletContext.setAttribute("applicationVersion", "1.0.0");
        servletContext.setAttribute("environment", "test");

        // 设置数据库连接信息
        servletContext.setAttribute("datasource.url", "*******************************************");
        servletContext.setAttribute("datasource.username", "test_user");
        servletContext.setAttribute("datasource.driver", "com.mysql.cj.jdbc.Driver");

        // 设置Redis连接信息
        servletContext.setAttribute("redis.host", "localhost");
        servletContext.setAttribute("redis.port", "6379");
        servletContext.setAttribute("redis.database", "0");
        servletContext.setAttribute("redis.timeout", "3000");

        // 设置文件上传配置
        servletContext.setAttribute("file.upload.path", "/tmp/ncbs-claim/uploads");
        servletContext.setAttribute("file.upload.maxSize", "10485760"); // 10MB
        servletContext.setAttribute("file.upload.allowedTypes", "pdf,jpg,jpeg,png,doc,docx,xls,xlsx");

        // 设置业务配置参数
        servletContext.setAttribute("business.replevy.maxAmount", "1000000.00");
        servletContext.setAttribute("business.replevy.timeoutDays", "30");
        servletContext.setAttribute("business.replevy.approveLevel", "3");
        servletContext.setAttribute("business.replevy.autoApproveAmount", "50000.00");

        // 设置系统配置参数
        servletContext.setAttribute("system.pageSize", "20");
        servletContext.setAttribute("system.sessionTimeout", "1800"); // 30分钟
        servletContext.setAttribute("system.logLevel", "INFO");
        servletContext.setAttribute("system.enableCache", "true");

        // 设置安全配置参数
        servletContext.setAttribute("security.tokenExpireTime", "7200"); // 2小时
        servletContext.setAttribute("security.maxLoginAttempts", "5");
        servletContext.setAttribute("security.passwordMinLength", "8");
        servletContext.setAttribute("security.enableEncryption", "true");

        // 设置邮件配置参数
        servletContext.setAttribute("mail.smtp.host", "smtp.test.com");
        servletContext.setAttribute("mail.smtp.port", "587");
        servletContext.setAttribute("mail.smtp.username", "<EMAIL>");
        servletContext.setAttribute("mail.smtp.enableTLS", "true");

        // 设置消息队列配置
        servletContext.setAttribute("mq.host", "localhost");
        servletContext.setAttribute("mq.port", "5672");
        servletContext.setAttribute("mq.username", "guest");
        servletContext.setAttribute("mq.virtualHost", "/");

        // 设置第三方接口配置
        servletContext.setAttribute("api.payment.url", "https://api.test.com/payment");
        servletContext.setAttribute("api.payment.timeout", "30000");
        servletContext.setAttribute("api.payment.retryTimes", "3");
        servletContext.setAttribute("api.payment.appKey", "test_app_key");

        // 设置监控配置
        servletContext.setAttribute("monitor.enable", "true");
        servletContext.setAttribute("monitor.interval", "60"); // 60秒
        servletContext.setAttribute("monitor.alertThreshold", "80");

        // 设置缓存配置
        servletContext.setAttribute("cache.enable", "true");
        servletContext.setAttribute("cache.expireTime", "3600"); // 1小时
        servletContext.setAttribute("cache.maxSize", "1000");

        // 设置日志配置
        servletContext.setAttribute("log.path", "/var/log/ncbs-claim");
        servletContext.setAttribute("log.maxFileSize", "100MB");
        servletContext.setAttribute("log.maxHistory", "30");
        servletContext.setAttribute("log.pattern", "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n");

        // 设置测试专用参数
        servletContext.setAttribute("test.mode", "true");
        servletContext.setAttribute("test.mockData", "true");
        servletContext.setAttribute("test.skipValidation", "false");
        servletContext.setAttribute("test.debugMode", "true");

        // 设置追偿业务专用参数
        servletContext.setAttribute("replevy.workflow.enable", "true");
        servletContext.setAttribute("replevy.notification.enable", "true");
        servletContext.setAttribute("replevy.autoAssign.enable", "true");
        servletContext.setAttribute("replevy.document.required", "true");

        log.info("WebServletContext初始化完成，已设置所有参数");
    }

    /**
     * 初始化Mock对象和用户Session
     */
    private void initializeMockRequestAndUserSession() {
        // 创建Mock对象
        mockRequest = new MockHttpServletRequest();
        mockResponse = new MockHttpServletResponse();
        mockSession = new MockHttpSession();

        // 设置Session到Request
        mockRequest.setSession(mockSession);

        // 创建测试用户对象
        com.paic.ncbs.um.model.dto.UserInfoDTO userInfoDTO = createTestUserInfoDTO();

        // 将用户对象设置到Session中
        mockSession.setAttribute(Constants.CURR_USER, userInfoDTO);
        mockSession.setAttribute(Constants.CURR_COMCODE, userInfoDTO.getComCode());

        // 设置RequestContextHolder，使得静态方法能够获取到Request
        ServletRequestAttributes attributes = new ServletRequestAttributes(mockRequest, mockResponse);
        RequestContextHolder.setRequestAttributes(attributes);

        log.info("Mock Request和用户Session初始化完成，用户: {}, 机构: {}",
                userInfoDTO.getUserName(), userInfoDTO.getComCode());
    }

    /**
     * 创建测试用的UserInfoDTO对象
     * @return UserInfoDTO测试对象
     */
    private com.paic.ncbs.um.model.dto.UserInfoDTO createTestUserInfoDTO() {
        com.paic.ncbs.um.model.dto.UserInfoDTO userInfoDTO = new com.paic.ncbs.um.model.dto.UserInfoDTO();

        // 基本用户信息
        userInfoDTO.setUserCode("007");           // 用户代码
        userInfoDTO.setUserName("测试用户张三");             // 用户姓名
        userInfoDTO.setComCode("1001");                     // 机构代码
        userInfoDTO.setComName("测试机构");                 // 机构名称

        // 联系信息
        userInfoDTO.setEmail("<EMAIL>");         // 邮箱
        userInfoDTO.setMobile("13800138001");               // 手机号

        log.debug("创建测试用户对象: {}", userInfoDTO.getUserName());
        return userInfoDTO;
    }

    /**
     * 生成静态号码 - 以大写英文字母开头随机生成10位
     * @return 10位静态号码
     */
    private String generateStaticNumber() {
        // 大写英文字母
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        // 数字
        String numbers = "0123456789";

        StringBuilder sb = new StringBuilder();

        // 第一位：大写英文字母
        sb.append(letters.charAt((int) (Math.random() * letters.length())));

        // 后9位：数字
        for (int i = 0; i < 9; i++) {
            sb.append(numbers.charAt((int) (Math.random() * numbers.length())));
        }

        return sb.toString();
    }

}