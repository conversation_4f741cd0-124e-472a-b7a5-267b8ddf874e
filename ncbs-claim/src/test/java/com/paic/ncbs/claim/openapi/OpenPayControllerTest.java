package com.paic.ncbs.claim.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.openapi.FeeInvoiceBackResultDTO;
import com.paic.ncbs.claim.service.openapi.OpenPayService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class OpenPayControllerTest {

    @Autowired
    private OpenPayService openPayService;

    private FeeInvoiceBackResultDTO validDto;

    @BeforeEach
    public void setUp() {
        // 创建一个有效的测试DTO
        validDto = new FeeInvoiceBackResultDTO();
        validDto.setReportNo("90011000000002007711");
        validDto.setCaseNo("40012507100002007719");
        validDto.setPaySerialNo("4344035cd75d4500909e1a094002abfe");
        validDto.setInvoiceCode("1111111");
        validDto.setInvoiceNo("22222");
        validDto.setReason("发票信息错误");
    }

    @Test
    @DisplayName("测试成功场景：正常参数调用feeInvoiceBackResult")
    public void testFeeInvoiceBackResultSuccess() {
        // 直接调用服务层方法，方便打断点调试
        assertDoesNotThrow(() -> {
            openPayService.feeInvoiceBackResult(validDto);
        });
    }

    @Test
    @DisplayName("测试失败场景1：缺少必填参数-报案号为空")
    public void testFeeInvoiceBackResultFailWithEmptyReportNo() {
        // 设置无效参数
        validDto.setReportNo("");
        
        // 直接调用服务层方法，验证会抛出异常
        assertThrows(Exception.class, () -> {
            openPayService.feeInvoiceBackResult(validDto);
        });
    }

    @Test
    @DisplayName("测试失败场景2：缺少必填参数-发票代码为空")
    public void testFeeInvoiceBackResultFailWithEmptyInvoiceCode() {
        // 设置无效参数
        validDto.setInvoiceCode("");
        
        // 直接调用服务层方法，验证会抛出异常
        assertThrows(Exception.class, () -> {
            openPayService.feeInvoiceBackResult(validDto);
        });
    }
    
    @Test
    @DisplayName("测试失败场景3：所有必填字段都为空")
    public void testFeeInvoiceBackResultFailWithAllFieldsEmpty() {
        // 创建一个所有字段都为空的DTO
        FeeInvoiceBackResultDTO invalidDto = new FeeInvoiceBackResultDTO();
        invalidDto.setReportNo("");
        invalidDto.setCaseNo("");
        invalidDto.setPaySerialNo("");
        invalidDto.setInvoiceCode("");
        invalidDto.setInvoiceNo("");
        invalidDto.setReason("");

        // 直接调用服务层方法，验证会抛出异常
        assertThrows(Exception.class, () -> {
            openPayService.feeInvoiceBackResult(invalidDto);
        });
    }
}