-- 第三方委托相关表建表sql

-- 1. 第三方委托主表
create table clms_entrustment (
    id_entrustment varchar(32) primary key comment '委托表主键',
    report_no varchar(50) comment '报案号',
    case_times int comment '赔付次数',
    third_party_type varchar(2) comment '第三方类型：01-公估，02-律师，03-其他',
    insured_apply_status varchar(100) comment '事故者现状',
    accident_scene varchar(200) comment '事故场景编号，取自数据字典。如有多个，用英文逗号分隔',
    accident_scene_name varchar(200) comment '事故场景编号名称',
    other varchar(500) comment '其他',
    entrustment_dpm_code varchar(100) comment '第三方公估公司代码',
    entrustment_dpm_name varchar(100) comment '第三方公估公司名称',
    contact_name varchar(100) comment '联系人姓名',
    contact_phone varchar(100) comment '联系方式',
    entrustment_description varchar(500) comment '委托说明',
    entrustment_object varchar(200) comment '委托对象',
    litigation_strategy varchar(500) comment '诉讼策略',
    fee_standard varchar(500) comment '收费标准',
    auditor_um_code varchar(50) comment '审批人代码',
    auditor_um_name varchar(50) comment '审批人姓名',
    auditor_dpm_code varchar(50) comment '审批人机构代码',
    auditor_dpm_name varchar(50) comment '审批人机构名称',
    entrustment_status varchar(2) comment '委托状态 0-草稿（暂存时为草稿）、1-待审批、2-不同意、3-同意',
    file_id varchar(300) comment '打印文件id',
    file_url varchar(1000) comment '打印中心文件地址',
    print_status varchar(2) comment '委托打印状态 1-已完成 2-未完成',
    operate varchar(2) comment '操作状态 0：暂存 1提交',
    created_by varchar(50)  not null comment '创建人',
    sys_ctime datetime not null default current_timestamp comment '创建时间',
    updated_by varchar(50)  default null comment '修改人员',
    sys_utime datetime not null default current_timestamp comment '修改时间',
    valid_flag varchar(1) comment '有效标志 Y-有效 N-无效',
    key idx_entrustment (id_entrustment),
    key idx_sys_ctime (sys_ctime),
    key idx_sys_utime (sys_utime),
    key idx_report_no (report_no, replevy_times)
)engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='第三方委托信息表';

-- 2. 委托审批表
create table clms_entrustment_audit (
    id_entrustment_audit varchar(32) primary key comment '委托审核表主键',
    id_entrustment varchar(32) comment '委托表主键',
    report_no varchar(50) comment '报案号',
    case_times int comment '赔付次数',
    policy_no varchar(30) comment '保单号',
    insured_name varchar(30) comment '被保险人名称',
    third_party_type varchar(2) comment '第三方类型：01-公估，02-律师，03-其他',
    entrustment_dpm_code varchar(100) comment '第三方公估公司代码',
    entrustment_dpm_name varchar(100) comment '第三方公估公司名称',
    initiator_um varchar(50) comment '发起人',
    initiator_um_name varchar(50) comment '发起人姓名',
    auditor_um varchar(50) comment '审批人',
    auditor_um_name varchar(50) comment '审批人姓名',
    auditor_dpm_code varchar(50) comment '审批人机构代码',
    audit_opinion varchar(10) comment '审批意见 1-不同意、2-同意',
    examine_time datetime comment '审核时间',
    valid_flag varchar(1) comment '有效标志 Y-有效 N-无效',
    remark varchar(2000) comment '备注',
    created_by varchar(50)  not null comment '创建人',
    sys_ctime datetime not null default current_timestamp comment '创建时间',
    updated_by varchar(50)  default null comment '修改人员',
    sys_utime datetime not null default current_timestamp comment '修改时间',
    key idx_entrustment (id_entrustment),
    key idx_entrustment_audit (id_entrustment_audit),
    key idx_sys_ctime (sys_ctime),
    key idx_sys_utime (sys_utime),
    key idx_report_no (report_no, replevy_times)
)engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='第三方委托审批信息表';

